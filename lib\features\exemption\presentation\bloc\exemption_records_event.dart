/// -----
/// exemption_records_event.dart
///
/// 免实习记录BLoC事件定义
/// 定义所有与免实习记录相关的用户操作事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 免实习记录事件基类
///
/// 所有免实习记录相关的事件都应继承此类
abstract class ExemptionRecordsEvent extends Equatable {
  const ExemptionRecordsEvent();

  @override
  List<Object?> get props => [];
}

/// 加载免实习记录列表事件
///
/// 根据当前选中的实习计划ID加载对应的免实习记录列表
class LoadExemptionRecordsEvent extends ExemptionRecordsEvent {
  /// 实习计划ID（可选，如果不提供则从全局状态获取）
  final String? planId;

  const LoadExemptionRecordsEvent({this.planId});

  @override
  List<Object?> get props => [planId];
}

/// 刷新免实习记录列表事件
///
/// 重新从服务器获取最新的免实习记录列表数据
class RefreshExemptionRecordsEvent extends ExemptionRecordsEvent {
  /// 实习计划ID（可选，如果不提供则从全局状态获取）
  final String? planId;

  const RefreshExemptionRecordsEvent({this.planId});

  @override
  List<Object?> get props => [planId];
}

/// 查看免实习记录详情事件
///
/// 点击列表项查看具体的免实习记录详情
class ViewExemptionRecordDetailEvent extends ExemptionRecordsEvent {
  /// 免实习记录ID
  final String recordId;

  const ViewExemptionRecordDetailEvent({required this.recordId});

  @override
  List<Object?> get props => [recordId];
}

/// 删除免实习记录事件
///
/// 删除指定的免实习记录（仅限待审核状态）
class DeleteExemptionRecordEvent extends ExemptionRecordsEvent {
  /// 免实习记录ID
  final String recordId;

  const DeleteExemptionRecordEvent({required this.recordId});

  @override
  List<Object?> get props => [recordId];
}

/// 重新提交免实习申请事件
///
/// 对于被拒绝的申请，可以重新提交
class ResubmitExemptionRecordEvent extends ExemptionRecordsEvent {
  /// 免实习记录ID
  final String recordId;

  const ResubmitExemptionRecordEvent({required this.recordId});

  @override
  List<Object?> get props => [recordId];
}

/// 筛选免实习记录事件
///
/// 根据状态筛选免实习记录列表
class FilterExemptionRecordsEvent extends ExemptionRecordsEvent {
  /// 筛选状态（null表示显示所有状态）
  final String? statusFilter;

  const FilterExemptionRecordsEvent({this.statusFilter});

  @override
  List<Object?> get props => [statusFilter];
}

/// 搜索免实习记录事件
///
/// 根据关键词搜索免实习记录
class SearchExemptionRecordsEvent extends ExemptionRecordsEvent {
  /// 搜索关键词
  final String keyword;

  const SearchExemptionRecordsEvent({required this.keyword});

  @override
  List<Object?> get props => [keyword];
}

/// 清除搜索事件
///
/// 清除搜索条件，显示所有记录
class ClearSearchEvent extends ExemptionRecordsEvent {
  const ClearSearchEvent();
}

/// 加载更多免实习记录事件
///
/// 分页加载更多免实习记录数据
class LoadMoreExemptionRecordsEvent extends ExemptionRecordsEvent {
  const LoadMoreExemptionRecordsEvent();
}
