import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import '../../../../core/config/injection/injection.dart';
import '../../../../core/router/app_navigator.dart';
import '../bloc/splash_bloc.dart';
import '../bloc/splash_event.dart';
import '../bloc/splash_state.dart';

/// 启动页面
///
/// 显示应用启动画面，处理初始化逻辑
class SplashScreen extends StatelessWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<SplashBloc>()..add(InitializeAppEvent()),
      child: const SplashView(),
    );
  }
}

/// 启动页视图
///
/// 启动页面的内容部分
class SplashView extends StatelessWidget {
  const SplashView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 监听状态变化
    return BlocListener<SplashBloc, SplashState>(
      listener: (context, state) {
        if (state is AuthenticatedState) {
          // 已登录，导航到首页
          AppNavigator.goToHome(context);
        } else if (state is UnauthenticatedState) {
          // 未登录，导航到登录页面
          AppNavigator.goToLogin(context);
        } else if (state is InitializationFailureState) {
          Logger.info('splash','应用初始化失败: ${state.message}');
          AppNavigator.goToLogin(context);
        }
      },
      child: Scaffold(
        body: Container(
          width: double.infinity,
          color: Colors.white,
          child: Column(
            children: [
              // 主要内容区域
              Expanded(
                flex: 3,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 应用logo
                      Container(
                        width: 120,
                        height: 120,
                        child: Image.asset(
                          'assets/images/logo.png',
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              decoration: BoxDecoration(
                                color: const Color(0xFF00C3B6),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.emoji_emotions_outlined,
                                size: 60,
                                color: Colors.white,
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(height: 20),
                      // 应用名称
                      const Text(
                        '亿硕教育',
                        style: TextStyle(
                          fontSize: 30,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF8C8C8C),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // 底部加载指示器区域
              Expanded(
                flex: 1,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 加载指示器
                    BlocBuilder<SplashBloc, SplashState>(
                      builder: (context, state) {
                        // 根据状态显示不同的加载指示器
                        if (state is AppInitializingState) {
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.grey[300],
                                ),
                              ),
                            ],
                          );
                        } else if (state is InitializationFailureState) {
                          return const Icon(Icons.error_outline, color: Colors.red);
                        } else {
                          return Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 10,
                                height: 10,
                                decoration: const BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Color(0xFF00C3B6),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Container(
                                width: 10,
                                height: 10,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: Colors.grey[300],
                                ),
                              ),
                            ],
                          );
                        }
                      },
                    ),
                    const SizedBox(height: 20),
                    // 底部标语
                    const Text(
                      '为了更好的实习教育',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFFAAAAAA),
                      ),
                    ),
                    const SizedBox(height: 50),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
