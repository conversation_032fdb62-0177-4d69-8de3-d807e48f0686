/// -----
/// application_submit_button.dart
///
/// 申请提交按钮组件
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_state.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 申请提交按钮组件
class ApplicationSubmitButton extends StatelessWidget {
  const ApplicationSubmitButton({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<InternshipApplicationBloc, InternshipApplicationState>(
      builder: (context, state) {
        return Container(
          width: double.infinity,
          color: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: ElevatedButton(
            onPressed: state.isSubmitting ? null : () => _handleSubmit(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: state.isSubmitting 
                  ? Colors.grey 
                  : AppTheme.primaryColor,
              padding: EdgeInsets.symmetric(vertical: 24.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
              elevation: 0,
            ),
            child: state.isSubmitting
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 20.w,
                        height: 20.h,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        '提交中...',
                        style: TextStyle(
                          fontSize: 32.sp,
                          fontWeight: FontWeight.w500,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  )
                : Text(
                    '提交',
                    style: TextStyle(
                      fontSize: 32.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
          ),
        );
      },
    );
  }

  /// 处理提交
  void _handleSubmit(BuildContext context) {
    context.read<InternshipApplicationBloc>().add(
      const SubmitApplicationEvent(),
    );
  }
}
