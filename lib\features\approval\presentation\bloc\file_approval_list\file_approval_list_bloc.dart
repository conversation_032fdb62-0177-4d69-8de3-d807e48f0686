/// -----
/// file_approval_list_bloc.dart
///
/// 文件审批列表 BLoC
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/logger.dart';
import '../../../domain/usecases/get_file_approval_list_usecase.dart';
import 'file_approval_list_event.dart';
import 'file_approval_list_state.dart';

/// 文件审批列表 BLoC
class FileApprovalListBloc extends Bloc<FileApprovalListEvent, FileApprovalListState> {
  final GetFileApprovalListUseCase _getFileApprovalListUseCase;

  FileApprovalListBloc({
    required GetFileApprovalListUseCase getFileApprovalListUseCase,
  })  : _getFileApprovalListUseCase = getFileApprovalListUseCase,
        super(const FileApprovalListInitial()) {
    on<LoadFileApprovalListEvent>(_onLoadFileApprovalList);
    on<RefreshFileApprovalListEvent>(_onRefreshFileApprovalList);
  }

  /// 处理加载文件审批列表事件
  Future<void> _onLoadFileApprovalList(
    LoadFileApprovalListEvent event,
    Emitter<FileApprovalListState> emit,
  ) async {
    Logger.info('FileApprovalListBloc', '开始加载文件审批列表，planId: ${event.planId}');
    
    emit(const FileApprovalListLoading());

    final result = await _getFileApprovalListUseCase(
      GetFileApprovalListParams(planId: event.planId),
    );

    result.fold(
      (failure) {
        Logger.error('FileApprovalListBloc', '加载文件审批列表失败: ${failure.message}');
        emit(FileApprovalListError(message: failure.message));
      },
      (fileApprovalList) {
        Logger.info('FileApprovalListBloc', '成功加载${fileApprovalList.length}个文件审批项');
        emit(FileApprovalListLoaded(fileApprovalList: fileApprovalList));
      },
    );
  }

  /// 处理刷新文件审批列表事件
  Future<void> _onRefreshFileApprovalList(
    RefreshFileApprovalListEvent event,
    Emitter<FileApprovalListState> emit,
  ) async {
    Logger.info('FileApprovalListBloc', '开始刷新文件审批列表，planId: ${event.planId}');

    // 刷新时不显示加载状态，保持当前状态
    final result = await _getFileApprovalListUseCase(
      GetFileApprovalListParams(planId: event.planId),
    );

    result.fold(
      (failure) {
        Logger.error('FileApprovalListBloc', '刷新文件审批列表失败: ${failure.message}');
        emit(FileApprovalListError(message: failure.message));
      },
      (fileApprovalList) {
        Logger.info('FileApprovalListBloc', '成功刷新${fileApprovalList.length}个文件审批项');
        emit(FileApprovalListLoaded(fileApprovalList: fileApprovalList));
      },
    );
  }
}
