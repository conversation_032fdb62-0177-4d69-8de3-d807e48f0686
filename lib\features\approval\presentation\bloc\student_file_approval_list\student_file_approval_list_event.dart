/// -----
/// student_file_approval_list_event.dart
///
/// 学生文件审批列表事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 学生文件审批列表事件基类
abstract class StudentFileApprovalListEvent extends Equatable {
  const StudentFileApprovalListEvent();

  @override
  List<Object?> get props => [];
}

/// 加载学生文件审批列表事件
class LoadStudentFileApprovalListEvent extends StudentFileApprovalListEvent {
  final String planId;
  final int type;

  const LoadStudentFileApprovalListEvent({
    required this.planId,
    required this.type,
  });

  @override
  List<Object?> get props => [planId, type];

  @override
  String toString() {
    return 'LoadStudentFileApprovalListEvent{planId: $planId, type: $type}';
  }
}

/// 刷新学生文件审批列表事件
class RefreshStudentFileApprovalListEvent extends StudentFileApprovalListEvent {
  final String planId;
  final int type;

  const RefreshStudentFileApprovalListEvent({
    required this.planId,
    required this.type,
  });

  @override
  List<Object?> get props => [planId, type];

  @override
  String toString() {
    return 'RefreshStudentFileApprovalListEvent{planId: $planId, type: $type}';
  }
}
