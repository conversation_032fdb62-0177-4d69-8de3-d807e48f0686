/// -----
/// teacher_safety_education_state.dart
/// 
/// 教师端安全教育考试状态类
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../../domain/models/teacher_safety_education_response.dart';

/// 教师端安全教育考试状态基类
/// 
/// 所有教师端安全教育考试相关状态的基类
abstract class TeacherSafetyEducationState extends Equatable {
  const TeacherSafetyEducationState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
/// 
/// 教师端安全教育考试页面的初始状态
class TeacherSafetyEducationInitial extends TeacherSafetyEducationState {
  const TeacherSafetyEducationInitial();
}

/// 加载中状态
/// 
/// 正在加载教师端安全教育考试数据
class TeacherSafetyEducationLoading extends TeacherSafetyEducationState {
  const TeacherSafetyEducationLoading();
}

/// 加载成功状态
/// 
/// 成功加载教师端安全教育考试数据
class TeacherSafetyEducationLoaded extends TeacherSafetyEducationState {
  /// 教师端安全教育考试数据响应
  final TeacherSafetyEducationResponse data;
  
  /// 当前实习计划ID
  final String planId;

  const TeacherSafetyEducationLoaded({
    required this.data,
    required this.planId,
  });

  @override
  List<Object?> get props => [data, planId];

  /// 复制状态并更新部分属性
  TeacherSafetyEducationLoaded copyWith({
    TeacherSafetyEducationResponse? data,
    String? planId,
  }) {
    return TeacherSafetyEducationLoaded(
      data: data ?? this.data,
      planId: planId ?? this.planId,
    );
  }
}

/// 刷新中状态
/// 
/// 正在刷新教师端安全教育考试数据，保持之前的数据显示
class TeacherSafetyEducationRefreshing extends TeacherSafetyEducationState {
  /// 之前的教师端安全教育考试数据响应
  final TeacherSafetyEducationResponse previousData;
  
  /// 当前实习计划ID
  final String planId;

  const TeacherSafetyEducationRefreshing({
    required this.previousData,
    required this.planId,
  });

  @override
  List<Object?> get props => [previousData, planId];
}

/// 加载失败状态
/// 
/// 加载教师端安全教育考试数据失败
class TeacherSafetyEducationError extends TeacherSafetyEducationState {
  /// 错误信息
  final String message;
  
  /// 当前实习计划ID（可选）
  final String? planId;

  const TeacherSafetyEducationError({
    required this.message,
    this.planId,
  });

  @override
  List<Object?> get props => [message, planId];
}

/// 刷新失败状态
/// 
/// 刷新教师端安全教育考试数据失败，保持之前的数据显示
class TeacherSafetyEducationRefreshError extends TeacherSafetyEducationState {
  /// 错误信息
  final String message;
  
  /// 之前的教师端安全教育考试数据响应
  final TeacherSafetyEducationResponse previousData;
  
  /// 当前实习计划ID
  final String planId;

  const TeacherSafetyEducationRefreshError({
    required this.message,
    required this.previousData,
    required this.planId,
  });

  @override
  List<Object?> get props => [message, previousData, planId];
}

/// 空数据状态
/// 
/// 没有安全教育考试数据
class TeacherSafetyEducationEmpty extends TeacherSafetyEducationState {
  /// 当前实习计划ID
  final String planId;

  const TeacherSafetyEducationEmpty({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}
