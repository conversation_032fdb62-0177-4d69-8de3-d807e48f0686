/// -----------------------------------------------------------------------------
/// plan_list_global_state.dart
///
/// 全局实习计划列表状态定义
/// 用于管理全局的实习计划列表状态
///
/// <AUTHOR>
/// @date 2025-06-16
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:equatable/equatable.dart';
import '../../../domain/entities/internship_plan_list_item.dart';

/// 全局实习计划列表状态基类
abstract class PlanListGlobalState extends Equatable {
  const PlanListGlobalState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class PlanListGlobalInitialState extends PlanListGlobalState {
  const PlanListGlobalInitialState();
}

/// 加载中状态
class PlanListGlobalLoadingState extends PlanListGlobalState {
  const PlanListGlobalLoadingState();
}

/// 加载成功状态
class PlanListGlobalLoadedState extends PlanListGlobalState {
  /// 实习计划列表
  final List<InternshipPlanListItem> plans;
  
  /// 当前选中的实习计划ID
  final String? currentPlanId;

  const PlanListGlobalLoadedState({
    required this.plans,
    this.currentPlanId,
  });

  /// 获取当前选中的实习计划
  InternshipPlanListItem? get currentPlan {
    if (currentPlanId == null) return null;
    try {
      return plans.firstWhere((plan) => plan.planId == currentPlanId);
    } catch (e) {
      return null;
    }
  }

  /// 复制状态并更新部分属性
  PlanListGlobalLoadedState copyWith({
    List<InternshipPlanListItem>? plans,
    String? currentPlanId,
  }) {
    return PlanListGlobalLoadedState(
      plans: plans ?? this.plans,
      currentPlanId: currentPlanId ?? this.currentPlanId,
    );
  }

  @override
  List<Object?> get props => [plans, currentPlanId];
}

/// 加载失败状态
class PlanListGlobalErrorState extends PlanListGlobalState {
  /// 错误信息
  final String message;

  const PlanListGlobalErrorState(this.message);

  @override
  List<Object?> get props => [message];
}

/// 刷新成功状态
class PlanListGlobalRefreshSuccessState extends PlanListGlobalLoadedState {
  const PlanListGlobalRefreshSuccessState({
    required super.plans,
    super.currentPlanId,
  });
}

/// 刷新失败状态
class PlanListGlobalRefreshErrorState extends PlanListGlobalState {
  /// 错误信息
  final String message;
  
  /// 之前的计划列表（如果有的话）
  final List<InternshipPlanListItem>? previousPlans;
  
  /// 之前选中的计划ID
  final String? previousCurrentPlanId;

  const PlanListGlobalRefreshErrorState({
    required this.message,
    this.previousPlans,
    this.previousCurrentPlanId,
  });

  @override
  List<Object?> get props => [message, previousPlans, previousCurrentPlanId];
}
