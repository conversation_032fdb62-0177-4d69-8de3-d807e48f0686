/// -----
/// plan_list_event.dart
/// 
/// 实习计划列表事件定义
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习计划列表事件基类
abstract class PlanListEvent extends Equatable {
  const PlanListEvent();

  @override
  List<Object?> get props => [];
}

/// 加载实习计划列表事件
class LoadPlanListEvent extends PlanListEvent {
  const LoadPlanListEvent();
}

/// 刷新实习计划列表事件
class RefreshPlanListEvent extends PlanListEvent {
  const RefreshPlanListEvent();
}

/// 申请实习事件
class ApplyInternshipEvent extends PlanListEvent {
  final String planId;

  const ApplyInternshipEvent({required this.planId});

  @override
  List<Object?> get props => [planId];
}

/// 申请免实习事件
class ApplyExemptionEvent extends PlanListEvent {
  final String planId;

  const ApplyExemptionEvent({required this.planId});

  @override
  List<Object?> get props => [planId];
}
