import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/config/env_config.dart';
import 'package:flutter_demo/core/network/api_debugger.dart';
import 'package:flutter_demo/core/network/dio_client.dart';

/// API调试界面
///
/// 提供环境切换、请求历史查看、模拟响应设置等功能
class ApiDebugScreen extends StatefulWidget {
  const ApiDebugScreen({Key? key}) : super(key: key);

  @override
  State<ApiDebugScreen> createState() => _ApiDebugScreenState();
}

class _ApiDebugScreenState extends State<ApiDebugScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ApiDebugger _apiDebugger = ApiDebugger.instance;
  final DioClient _dioClient = DioClient();
  
  // 当前选中的环境
  EnvType _selectedEnv = Env.instance.config.envType;
  
  // 是否启用调试模式
  bool _debugEnabled = ApiDebugger.instance.isDebugEnabled;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API调试工具'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: '环境设置'),
            Tab(text: '请求历史'),
            Tab(text: '模拟响应'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildEnvironmentTab(),
          _buildHistoryTab(),
          _buildMockTab(),
        ],
      ),
    );
  }
  
  /// 构建环境设置选项卡
  Widget _buildEnvironmentTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '环境配置',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // 环境选择
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '选择环境',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // 开发环境
                  RadioListTile<EnvType>(
                    title: const Text('开发环境'),
                    subtitle: Text(EnvConfig.development.baseUrl),
                    value: EnvType.dev,
                    groupValue: _selectedEnv,
                    onChanged: (value) {
                      setState(() {
                        _selectedEnv = value!;
                      });
                    },
                  ),
                  
                  // 测试环境
                  RadioListTile<EnvType>(
                    title: const Text('测试环境'),
                    subtitle: Text(EnvConfig.test.baseUrl),
                    value: EnvType.test,
                    groupValue: _selectedEnv,
                    onChanged: (value) {
                      setState(() {
                        _selectedEnv = value!;
                      });
                    },
                  ),
                  
                  // 生产环境
                  RadioListTile<EnvType>(
                    title: const Text('生产环境'),
                    subtitle: Text(EnvConfig.production.baseUrl),
                    value: EnvType.prod,
                    groupValue: _selectedEnv,
                    onChanged: (value) {
                      setState(() {
                        _selectedEnv = value!;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 应用环境按钮
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _applyEnvironment,
                      child: const Text('应用环境配置'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 调试模式设置
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '调试设置',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // 启用调试模式开关
                  SwitchListTile(
                    title: const Text('启用API调试模式'),
                    subtitle: const Text('开启后可记录请求历史、设置模拟响应'),
                    value: _debugEnabled,
                    onChanged: (value) {
                      setState(() {
                        _debugEnabled = value;
                        if (_debugEnabled) {
                          _apiDebugger.enable();
                        } else {
                          _apiDebugger.disable();
                        }
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 清除历史按钮
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        _apiDebugger.clearHistory();
                        setState(() {});
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('已清除请求历史')),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange,
                      ),
                      child: const Text('清除请求历史'),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // 当前环境信息
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '当前环境信息',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  _buildInfoItem('环境类型', _dioClient.envConfig.name),
                  _buildInfoItem('API基础URL', _dioClient.envConfig.baseUrl),
                  _buildInfoItem('超时设置', '${_dioClient.envConfig.timeoutMillis}毫秒'),
                  _buildInfoItem('日志记录', _dioClient.envConfig.enableLogging ? '已启用' : '已禁用'),
                  _buildInfoItem('缓存功能', _dioClient.envConfig.enableCache ? '已启用' : '已禁用'),
                  if (_dioClient.envConfig.enableCache)
                    _buildInfoItem('缓存时间', '${_dioClient.envConfig.cacheMaxAge.inMinutes}分钟'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建信息项
  Widget _buildInfoItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建请求历史选项卡
  Widget _buildHistoryTab() {
    final history = _apiDebugger.requestHistory;
    
    if (!_debugEnabled) {
      return const Center(
        child: Text('请先在"环境设置"选项卡中启用API调试模式'),
      );
    }
    
    if (history.isEmpty) {
      return const Center(
        child: Text('暂无请求历史记录'),
      );
    }
    
    return ListView.builder(
      itemCount: history.length,
      itemBuilder: (context, index) {
        final record = history[history.length - 1 - index];
        final hasResponse = record.response != null;
        final hasError = record.error != null;
        
        Color statusColor = Colors.grey;
        String statusText = '等待中';
        
        if (hasResponse) {
          final statusCode = record.response!.statusCode;
          if (statusCode >= 200 && statusCode < 300) {
            statusColor = Colors.green;
            statusText = '$statusCode';
          } else {
            statusColor = Colors.orange;
            statusText = '$statusCode';
          }
        } else if (hasError) {
          statusColor = Colors.red;
          statusText = '错误';
        }
        
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListTile(
            title: Text('${record.method} ${record.path}'),
            subtitle: Text(
              '${record.timestamp.toString().substring(0, 19)}'
              '${hasResponse ? ' • 响应时间: ${record.response!.timestamp.toString().substring(0, 19)}' : ''}'
            ),
            trailing: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: statusColor,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                statusText,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            onTap: () => _showRequestDetails(record),
          ),
        );
      },
    );
  }
  
  /// 构建模拟响应选项卡
  Widget _buildMockTab() {
    if (!_debugEnabled) {
      return const Center(
        child: Text('请先在"环境设置"选项卡中启用API调试模式'),
      );
    }
    
    return const Center(
      child: Text('模拟响应功能开发中...'),
    );
  }
  
  /// 应用环境配置
  void _applyEnvironment() {
    EnvConfig config;
    
    switch (_selectedEnv) {
      case EnvType.dev:
        config = EnvConfig.development;
        break;
      case EnvType.test:
        config = EnvConfig.test;
        break;
      case EnvType.prod:
        config = EnvConfig.production;
        break;
      default:
        config = EnvConfig.development;
    }
    
    // 初始化环境
    Env.instance.initialize(environment: _selectedEnv);
    
    // 更新DioClient配置
    _dioClient.initWithConfig(config);
    
    setState(() {});
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('已切换到${config.name}环境')),
    );
  }
  
  /// 显示请求详情
  void _showRequestDetails(RequestRecord record) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return DraggableScrollableSheet(
          initialChildSize: 0.6,
          maxChildSize: 0.9,
          minChildSize: 0.4,
          expand: false,
          builder: (context, scrollController) {
            return SingleChildScrollView(
              controller: scrollController,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  
                  Text(
                    '${record.method} ${record.path}',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  _buildDetailSection('请求信息', [
                    _buildDetailItem('URL', '${record.baseUrl}${record.path}'),
                    _buildDetailItem('方法', record.method),
                    _buildDetailItem('时间', record.timestamp.toString()),
                    if (record.queryParameters != null && record.queryParameters!.isNotEmpty)
                      _buildDetailItem('查询参数', _prettyJson(record.queryParameters)),
                    if (record.data != null)
                      _buildDetailItem('请求体', _prettyJson(record.data)),
                    _buildDetailItem('请求头', _prettyJson(record.headers)),
                  ]),
                  
                  if (record.response != null) ...[
                    const SizedBox(height: 16),
                    _buildDetailSection('响应信息', [
                      _buildDetailItem('状态码', '${record.response!.statusCode}'),
                      _buildDetailItem('时间', record.response!.timestamp.toString()),
                      _buildDetailItem('响应体', _prettyJson(record.response!.data)),
                      _buildDetailItem('响应头', _prettyJson(record.response!.headers)),
                    ]),
                  ],
                  
                  if (record.error != null) ...[
                    const SizedBox(height: 16),
                    _buildDetailSection('错误信息', [
                      _buildDetailItem('错误类型', record.error!.type),
                      _buildDetailItem('错误消息', record.error!.message),
                      if (record.error!.statusCode != null)
                        _buildDetailItem('状态码', '${record.error!.statusCode}'),
                      if (record.error!.data != null)
                        _buildDetailItem('错误数据', _prettyJson(record.error!.data)),
                      _buildDetailItem('时间', record.error!.timestamp.toString()),
                    ]),
                  ],
                ],
              ),
            );
          },
        );
      },
    );
  }
  
  /// 构建详情部分
  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }
  
  /// 构建详情项
  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 4),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(4),
            ),
            child: SelectableText(value),
          ),
        ],
      ),
    );
  }
  
  /// 格式化JSON
  String _prettyJson(dynamic json) {
    try {
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(json);
    } catch (e) {
      return json.toString();
    }
  }
}
