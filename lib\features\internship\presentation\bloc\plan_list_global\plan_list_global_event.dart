/// -----------------------------------------------------------------------------
/// plan_list_global_event.dart
///
/// 全局实习计划列表事件定义
/// 用于管理全局的实习计划列表状态
///
/// <AUTHOR>
/// @date 2025-06-16
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:equatable/equatable.dart';

/// 全局实习计划列表事件基类
abstract class PlanListGlobalEvent extends Equatable {
  const PlanListGlobalEvent();

  @override
  List<Object?> get props => [];
}

/// 加载实习计划列表事件
class LoadPlanListGlobalEvent extends PlanListGlobalEvent {
  const LoadPlanListGlobalEvent();
}

/// 刷新实习计划列表事件
class RefreshPlanListGlobalEvent extends PlanListGlobalEvent {
  const RefreshPlanListGlobalEvent();
}

/// 选择当前实习计划事件
class SelectCurrentPlanEvent extends PlanListGlobalEvent {
  /// 选中的实习计划ID
  final String planId;

  const SelectCurrentPlanEvent(this.planId);

  @override
  List<Object?> get props => [planId];
}
