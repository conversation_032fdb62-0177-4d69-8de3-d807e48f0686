---
trigger: always_on
---

# 编码规范：文件注释

## 文件头注释

所有代码文件必须在文件开头包含标准的文件头注释，使用以下格式：

```dart
/// -----
/// 文件名（如：teacher_internship_plan_detail_screen.dart）
/// 
/// 文件描述（简要说明文件的主要功能和用途）
///
/// <AUTHOR>
/// @date 创建日期（必填，格式：YYYY-MM-DD）
/// @copyright 版权信息（必填，格式：Copyright © 2025 亿硕教育）
/// -----
```

## 类注释

所有类定义前必须添加类注释：

```dart
/// 类功能描述
///
/// 详细说明类的职责、主要功能和使用场景。
/// 如果类有特殊的设计模式或实现细节，也应在此说明。
///
/// 示例代码（可选）：
/// ```dart
/// final example = ExampleClass();
/// example.doSomething();
/// ```
///
/// @see 相关类或方法（可选）
class ExampleClass {
  // 类实现...
}
```

## 方法注释

所有公共方法和复杂私有方法前必须添加方法注释：

```dart
/// 方法功能描述
///
/// 详细说明方法的功能、参数、返回值和可能的异常。
///
/// @param param1 参数1的说明
/// @param param2 参数2的说明
/// @return 返回值的说明
/// @throws 可能抛出的异常说明
/// @see 相关方法或类（可选）
Future<Result> exampleMethod(String param1, int param2) async {
  // 方法实现...
}
```

## 属性注释

所有公共属性和需要说明的私有属性前应添加注释：

```dart
/// 属性说明
///
/// 详细说明属性的用途、约束条件等。
/// 如果属性有特定的取值范围或格式要求，也应在此说明。
final String exampleProperty;
```

## 代码块注释

1. **单行注释**：使用 `//` 注释单行代码
   ```dart
   // 这是一个单行注释
   final name = 'example';
   ```

2. **多行注释**：使用 `/* */` 或 `///` 注释多行代码
   ```dart
   /*
    * 这是一个
    * 多行注释
    */
   ```

3. **TODO注释**：使用 `// TODO:` 标记待办事项
   ```dart
   // TODO: 实现XX功能
   ```

4. **FIXME注释**：使用 `// FIXME:` 标记需要修复的问题
   ```dart
   // FIXME: 修复性能问题
   ```

## 文档注释规范

1. 使用 `///` 为公共API添加文档注释
2. 文档注释应该清晰、简洁地说明代码的功能和用法
3. 包含参数、返回值和异常的说明
4. 使用Markdown格式增强可读性
5. 为复杂算法或业务逻辑添加必要的说明

## 检查规则

1. **required**: 文件头注释、公共类、公共方法必须添加文档注释
2. **recommended**: 建议为复杂私有方法和属性添加注释
3. **strict**: 注释必须清晰、准确，避免无意义的注释
4. **consistent**: 保持注释风格一致，遵循项目规范

## 自动化检查

项目配置了 `dart doc` 和 `effective_dart` 工具来自动检查文档注释的质量。在提交代码前，请确保：

1. 所有公共API都有完整的文档注释
2. 没有未解决的TODO/FIXME注释（除非有合理解释）
3. 注释内容与代码实现保持一致
4. 注释格式符合规范