plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

import java.text.SimpleDateFormat
import java.util.Date

android {
    namespace = "com.yishuo.internship"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.yishuo.internship"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = flutter.minSdkVersion
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")
        }
    }

    // 自定义APK输出文件名
    applicationVariants.all {
        val variant = this
        variant.outputs.all {
            val output = this as com.android.build.gradle.internal.api.BaseVariantOutputImpl

            // 获取版本名称
            val versionName = variant.versionName ?: "1.0.0"

            // 获取当前日期，格式为yyyyMMdd
            val dateFormat = SimpleDateFormat("yyyyMMdd")
            val currentDate = dateFormat.format(Date())

            // 获取构建类型（debug/release）
            val buildType = variant.buildType.name

            // 构造新的APK文件名：app-{版本号}-{年月日}-{构建类型}.apk
            val newApkName = if (buildType == "release") {
                "app-${versionName}-${currentDate}.apk"
            } else {
                "app-${versionName}-${currentDate}-${buildType}.apk"
            }

            output.outputFileName = newApkName
        }
    }
}

flutter {
    source = "../.."
}
