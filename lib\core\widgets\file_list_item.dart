/// -----
/// file_list_item.dart
/// 
/// 通用文件列表项组件，支持不同的右侧内容显示模式（状态标签或数字统计）
/// 用于文件上传页面和文件审批页面的列表项展示
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 文件列表项组件
/// 
/// 支持两种显示模式：
/// 1. 状态标签模式：显示文件状态（未上传、已通过、已驳回等）
/// 2. 数字统计模式：显示数字统计信息（如 15/25）
class FileListItem extends StatelessWidget {
  /// 文件ID，用于导航和识别
  final String fileId;
  
  /// 文件图标路径
  final String iconPath;
  
  /// 文件名称
  final String fileName;
  
  /// 点击回调
  final VoidCallback? onTap;
  
  /// 状态标签文本（状态模式使用）
  final String? statusText;
  
  /// 状态标签背景色（状态模式使用）
  final Color? statusBackgroundColor;
  
  /// 状态标签文字颜色（状态模式使用）
  final Color? statusTextColor;
  
  /// 数字统计文本（统计模式使用）
  final String? statisticsText;
  
  /// 数字统计文字颜色（统计模式使用）
  final Color? statisticsTextColor;

  const FileListItem({
    Key? key,
    required this.fileId,
    required this.iconPath,
    required this.fileName,
    this.onTap,
    this.statusText,
    this.statusBackgroundColor,
    this.statusTextColor,
    this.statisticsText,
    this.statisticsTextColor,
  }) : super(key: key);

  /// 创建状态标签模式的文件列表项
  factory FileListItem.withStatus({
    Key? key,
    required String fileId,
    required String iconPath,
    required String fileName,
    required String statusText,
    required Color statusBackgroundColor,
    required Color statusTextColor,
    VoidCallback? onTap,
  }) {
    return FileListItem(
      key: key,
      fileId: fileId,
      iconPath: iconPath,
      fileName: fileName,
      statusText: statusText,
      statusBackgroundColor: statusBackgroundColor,
      statusTextColor: statusTextColor,
      onTap: onTap,
    );
  }

  /// 创建数字统计模式的文件列表项
  factory FileListItem.withStatistics({
    Key? key,
    required String fileId,
    required String iconPath,
    required String fileName,
    required String statisticsText,
    Color? statisticsTextColor,
    VoidCallback? onTap,
  }) {
    return FileListItem(
      key: key,
      fileId: fileId,
      iconPath: iconPath,
      fileName: fileName,
      statisticsText: statisticsText,
      statisticsTextColor: statisticsTextColor ?? const Color(0xFFB0B0B0),
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(top: 20.h),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.r),
        ),
        child: Row(
          children: [
            // 文件图标
            Image.asset(
              iconPath,
              width: 40.w,
              height: 48.h,
            ),
            SizedBox(width: 10.w),
            
            // 文件名
            Expanded(
              child: Text(
                fileName,
                style: const TextStyle(
                  fontSize: 16,
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            
            // 右侧内容：状态标签或数字统计
            if (statusText != null) ...[
              Container(
                margin: EdgeInsets.only(left: 18.w),
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: BoxDecoration(
                  color: statusBackgroundColor ?? Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(6.r),
                ),
                child: Text(
                  statusText!,
                  style: TextStyle(
                    fontSize: 20.sp,
                    color: statusTextColor ?? Colors.grey,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ] else if (statisticsText != null) ...[
              Text(
                statisticsText!,
                style: TextStyle(
                  fontSize: 16,
                  color: statisticsTextColor,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
            
            SizedBox(width: 8.w),
            
            // 右箭头
            const Icon(
              Icons.chevron_right,
              color: Color(0xFFB0B0B0),
            ),
          ],
        ),
      ),
    );
  }
}
