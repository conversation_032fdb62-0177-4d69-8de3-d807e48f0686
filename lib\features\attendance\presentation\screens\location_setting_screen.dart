/// -----
/// location_setting_screen.dart
/// 
/// 设置签到地址页面，用于设置家庭或单位地址
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

class LocationSettingScreen extends StatefulWidget {
  final String locationType; // '家' 或 '单位'
  
  const LocationSettingScreen({
    Key? key,
    required this.locationType,
  }) : super(key: key);

  @override
  State<LocationSettingScreen> createState() => _LocationSettingScreenState();
}

class _LocationSettingScreenState extends State<LocationSettingScreen> {
  final TextEditingController _addressController = TextEditingController();
  
  // 模拟的位置列表
  final List<Map<String, String>> _suggestedLocations = [
    {
      'name': '湖北省武汉市武昌区中南路21号1',
      'address': '湖北省武汉市武昌区中南路21号1',
    },
    {
      'name': '中南建筑设计院路东生活区',
      'address': '中南省衔中南路72号(中南路地铁站旁)',
    },
    {
      'name': '中南建筑设计院生活区',
      'address': '中南路21号(中南路地铁站对面300米处)',
    },
  ];
  
  // 当前选中的位置
  String _selectedLocation = '';
  
  @override
  void initState() {
    super.initState();
    // 设置页面标题及默认地址
    if (widget.locationType == '家') {
      _addressController.text = '请输入家庭住址';
    } else {
      _addressController.text = '请输入单位地址';
    }
  }
  
  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: '签到',
        centerTitle: true,
        showBackButton: true,
        actions: [
          TextButton(
            onPressed: () {
              // 保存设置并返回
              Navigator.pop(context);
            },
            child: const Text(
              '确定',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ),
        ],
        backgroundColor: const Color(0xFF00ACD1),
        titleColor: Colors.white,
      ),
      body: Column(
        children: [
          // 地址输入区域
          _buildAddressInputSection(),
          
          // 地图区域
          Expanded(
            child: _buildMapSection(),
          ),
          
          // 底部地址建议列表
          _buildSuggestedLocations(),
        ],
      ),
    );
  }
  
  // 构建地址输入区域
  Widget _buildAddressInputSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '设置${widget.locationType}庭住址',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _addressController,
            decoration: InputDecoration(
              hintText: '请输入${widget.locationType}庭住址',
              border: const OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey, width: 0.5),
              ),
              enabledBorder: const OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey, width: 0.5),
              ),
              focusedBorder: const OutlineInputBorder(
                borderSide: BorderSide(color: Color(0xFF00ACD1), width: 1.0),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }
  
  // 构建地图区域（模拟）
  Widget _buildMapSection() {
    return Stack(
      children: [
        // 地图背景（使用图片代替实际的百度地图SDK）
        Image.asset(
          'assets/images/map_placeholder.jpg',
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            // 如果图片不存在，显示模拟的地图背景
            return Container(
              color: const Color(0xFFEAECF0),
              child: Stack(
                children: [
                  // 模拟地图网格
                  GridView.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 10,
                    ),
                    itemCount: 200,
                    itemBuilder: (context, index) {
                      return Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.withOpacity(0.2)),
                          color: index % 3 == 0 ? Colors.grey.withOpacity(0.1) : Colors.transparent,
                        ),
                      );
                    },
                  ),
                  // 模拟道路
                  Center(
                    child: Container(
                      width: 20,
                      height: double.infinity,
                      color: Colors.green.withOpacity(0.3),
                    ),
                  ),
                  // 模拟当前位置
                  const Center(
                    child: Icon(
                      Icons.location_on,
                      color: Colors.blue,
                      size: 40,
                    ),
                  ),
                  // 模拟周围位置点
                  Positioned(
                    left: 100,
                    top: 120,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        '元银甲',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                  ),
                  Positioned(
                    right: 100,
                    top: 180,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        '罗氏热干牛肉面',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                  ),
                  Positioned(
                    right: 150,
                    bottom: 150,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        '当天婆',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                  ),
                  Positioned(
                    left: 80,
                    bottom: 100,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        '湘聚室内房装',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                  ),
                  Positioned(
                    left: 20,
                    top: 80,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: const Text(
                        '武汉市第七医院',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        
        // 地图控制按钮
        Positioned(
          right: 16,
          bottom: 100,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.gps_fixed, color: Colors.black87),
              onPressed: () {
                // 定位当前位置
              },
            ),
          ),
        ),
        
        // 底部位置信息
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                const Icon(Icons.location_on, color: Colors.grey),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '湖北省武汉市武昌区中南路21号1',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: const Color(0xFF00ACD1)),
                  ),
                  child: const Text(
                    '确认选点',
                    style: TextStyle(
                      color: Color(0xFF00ACD1),
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  // 构建推荐地址列表
  Widget _buildSuggestedLocations() {
    return Container(
      color: Colors.white,
      child: Column(
        children: _suggestedLocations.map((location) {
          return InkWell(
            onTap: () {
              setState(() {
                _selectedLocation = location['address']!;
                _addressController.text = location['address']!;
              });
            },
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Colors.grey[300]!, width: 0.5),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.location_on, color: Colors.grey, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          location['name']!,
                          style: const TextStyle(
                            fontSize: 14,
                            color: Colors.black87,
                          ),
                        ),
                        if (location['address'] != null && location['address']!.isNotEmpty)
                          Text(
                            location['address']!,
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.grey,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: const Color(0xFF00ACD1)),
                    ),
                    child: const Text(
                      '确认选点',
                      style: TextStyle(
                        color: Color(0xFF00ACD1),
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
} 