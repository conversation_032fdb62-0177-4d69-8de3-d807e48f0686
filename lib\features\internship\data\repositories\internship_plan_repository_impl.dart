/// -----
/// internship_plan_repository_impl.dart
/// 
/// 实习计划仓库实现
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/error/failures/auth_failure.dart';
import '../../../../core/error/exceptions/auth_exception.dart';
import '../../../../core/error/exceptions/server_exception.dart';
import '../../../../core/error/exceptions/network_exception.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/internship_plan.dart';
import '../../domain/entities/internship_plan_list_item.dart';
import '../../domain/repositories/internship_plan_repository.dart';
import '../datasources/remote/internship_plan_remote_data_source.dart';

/// 实习计划仓库实现
/// 
/// 实现实习计划数据操作的具体逻辑，处理数据源协调和错误转换
class InternshipPlanRepositoryImpl implements InternshipPlanRepository {
  final InternshipPlanRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  InternshipPlanRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<InternshipPlan>>> getTeacherInternshipPlans() async {
    try {
      Logger.info('InternshipPlanRepository', '开始获取教师实习计划列表');

      // 检查网络连接
      if (!await _networkInfo.isConnected) {
        Logger.warning('InternshipPlanRepository', '网络连接不可用');
        return const Left(NetworkFailure('网络连接失败，请检查网络设置'));
      }

      // 从远程数据源获取数据
      final planModels = await _remoteDataSource.getTeacherInternshipPlans();
      
      // 转换为领域实体
      final plans = planModels.map((model) => model.toEntity()).toList();
      
      Logger.info('InternshipPlanRepository', '成功获取${plans.length}个实习计划');
      return Right(plans);
      
    } on UnauthorizedException catch (e) {
      Logger.error('InternshipPlanRepository', '认证异常: ${e.message}');
      return Left(UnauthorizedFailure(e.message));
    } on ForbiddenException catch (e) {
      Logger.error('InternshipPlanRepository', '权限异常: ${e.message}');
      return Left(ForbiddenFailure(e.message));
    } on ServerException catch (e) {
      Logger.error('InternshipPlanRepository', '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error('InternshipPlanRepository', '网络异常: ${e.message}');
      return Left(NetworkFailure(e.message));
    } on Exception catch (e) {
      Logger.error('InternshipPlanRepository', '未知异常: $e');
      return Left(ServerFailure('获取实习计划列表失败: $e'));
    }
  }

  @override
  Future<Either<Failure, InternshipPlan>> getInternshipPlanDetail(String id) async {
    Logger.info('InternshipPlanRepository', '开始获取实习计划详情，ID: $id');

    if (await _networkInfo.isConnected) {
      try {
        final planModel = await _remoteDataSource.getInternshipPlanDetail(id);
        final plan = planModel.toEntity();
        Logger.info('InternshipPlanRepository', '成功获取实习计划详情');
        return Right(plan);
      } on UnauthorizedException catch (e) {
        Logger.error('InternshipPlanRepository', '认证异常: ${e.message}');
        return Left(UnauthorizedFailure(e.message));
      } on ForbiddenException catch (e) {
        Logger.error('InternshipPlanRepository', '权限异常: ${e.message}');
        return Left(ForbiddenFailure(e.message));
      } on ServerException catch (e) {
        Logger.error('InternshipPlanRepository', '服务器异常: ${e.message}');
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        Logger.error('InternshipPlanRepository', '网络异常: ${e.message}');
        return Left(NetworkFailure(e.message));
      } on Exception catch (e) {
        Logger.error('InternshipPlanRepository', '未知异常: $e');
        return Left(ServerFailure('获取实习计划详情失败: $e'));
      }
    } else {
      Logger.warning('InternshipPlanRepository', '网络连接不可用');
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }

  @override
  Future<Either<Failure, List<InternshipPlanListItem>>> getInternshipPlanList() async {
    Logger.info('InternshipPlanRepository', '开始获取实习计划列表');

    if (await _networkInfo.isConnected) {
      try {
        final planModels = await _remoteDataSource.getInternshipPlanList();
        final plans = planModels.map((model) => model.toEntity()).toList();
        Logger.info('InternshipPlanRepository', '成功获取实习计划列表');
        return Right(plans);
      } on UnauthorizedException catch (e) {
        Logger.error('InternshipPlanRepository', '认证异常: ${e.message}');
        return Left(UnauthorizedFailure(e.message));
      } on ForbiddenException catch (e) {
        Logger.error('InternshipPlanRepository', '权限异常: ${e.message}');
        return Left(ForbiddenFailure(e.message));
      } on ServerException catch (e) {
        Logger.error('InternshipPlanRepository', '服务器异常: ${e.message}');
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        Logger.error('InternshipPlanRepository', '网络异常: ${e.message}');
        return Left(NetworkFailure(e.message));
      } on Exception catch (e) {
        Logger.error('InternshipPlanRepository', '未知异常: $e');
        return Left(ServerFailure('获取实习计划列表失败: $e'));
      }
    } else {
      Logger.warning('InternshipPlanRepository', '网络连接不可用');
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }
}
