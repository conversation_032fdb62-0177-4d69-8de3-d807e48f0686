/// -----------------------------------------------------------------------------
/// location_picker.dart
/// 
/// 省市区选择器组件，用于在表单中选择中国省市区地址信息
/// 基于city_pickers库实现，提供统一的UI和交互体验
/// 
/// <AUTHOR>
/// @date 2025-04-10
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:city_pickers/city_pickers.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

class LocationPicker extends StatelessWidget {
  final String label;
  final String value;
  final Function(String) onSelected;
  final bool showLabel;
  final bool required;

  const LocationPicker({
    Key? key,
    required this.label,
    required this.value,
    required this.onSelected,
    this.showLabel = true,
    this.required = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (required)
            const Text(
              '*',
              style: TextStyle(color: Colors.red, fontSize: 14),
            ),
          if (showLabel)
            SizedBox(
              width: 160,
              child: Text(
                label,
                style: TextStyle(
                  fontSize: 15,
                  color: Colors.grey[600],
                ),
              ),
            ),
          Expanded(
            child: GestureDetector(
              onTap: () => _showCityPicker(context),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    value.isEmpty ? '请选择省市区' : value,
                    style: TextStyle(
                      fontSize: 15,
                      color: value.isEmpty ? Colors.grey[400] : Colors.black,
                    ),
                  ),
                  const Icon(Icons.keyboard_arrow_down, size: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showCityPicker(BuildContext context) async {
    Result? result = await CityPickers.showCityPicker(
      context: context,
      height: 300,
      cancelWidget: Text(
        '取消',
        style: TextStyle(color: Colors.grey[600]),
      ),
      confirmWidget: const Text(
        '确定',
        style: TextStyle(color: AppTheme.primaryColor),
      ),
      itemExtent: 40, // 项目高度
      // 如果有初始值，可以在这里设置
      // locationCode: '110100',
    );

    if (result != null) {
      // 组合省市区字符串
      String selectedLocation = '${result.provinceName}/${result.cityName}/${result.areaName}';
      onSelected(selectedLocation);
    }
  }
} 