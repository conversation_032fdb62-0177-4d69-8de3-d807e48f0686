import '../../../../core/constants/constants.dart';
import '../../../../core/widgets/approval_list_item.dart';

/// -----
/// free_internship_application_model.dart
///
/// 免实习申请数据模型，用于存储免实习申请的数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

class FreeInternshipApplicationModel implements ApprovalItemData {
  @override
  final String id;
  @override
  final String studentName;
  @override
  final String studentAvatar;
  final String studentDestination; // 学生去向
  final String applyDate;
  @override
  final String status; // 待审批、已通过、已驳回
  final List<AttachmentInfo> attachments; // 证明文件

  @override
  String get submitTime => applyDate;

  FreeInternshipApplicationModel({
    required this.id,
    required this.studentName,
    required this.studentAvatar,
    required this.studentDestination,
    required this.applyDate,
    required this.status,
    required this.attachments,
  });

  // 从JSON创建模型
  factory FreeInternshipApplicationModel.fromJson(Map<String, dynamic> json) {
    return FreeInternshipApplicationModel(
      id: json['id'],
      studentName: json['studentName'],
      studentAvatar: json['studentAvatar'],
      studentDestination: json['studentDestination'],
      applyDate: json['applyDate'],
      status: json['status'],
      attachments: (json['attachments'] as List)
          .map((item) => AttachmentInfo.fromJson(item))
          .toList(),
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentName': studentName,
      'studentAvatar': studentAvatar,
      'studentDestination': studentDestination,
      'applyDate': applyDate,
      'status': status,
      'attachments': attachments.map((item) => item.toJson()).toList(),
    };
  }

  // 获取示例数据
  static List<FreeInternshipApplicationModel> getSampleData() {
    return [
      // 已审批数据
      FreeInternshipApplicationModel(
        id: '1',
        studentName: '李成儒',
        studentAvatar: AppConstants.avatar1,
        studentDestination: '参军',
        applyDate: '2025-04-23 22:12',
        status: '已通过',
        attachments: [
          AttachmentInfo(name: '参军', url: ''),
        ],
      ),
      FreeInternshipApplicationModel(
        id: '2',
        studentName: '李成儒',
        studentAvatar: AppConstants.avatar2,
        studentDestination: '参军',
        applyDate: '2025-04-23 22:12',
        status: '已驳回',
        attachments: [
          AttachmentInfo(name: '参军', url: ''),
        ],
      ),
      FreeInternshipApplicationModel(
        id: '3',
        studentName: '李成儒',
        studentAvatar: AppConstants.avatar3,
        studentDestination: '参军',
        applyDate: '2025-04-23 22:12',
        status: '已驳回',
        attachments: [
          AttachmentInfo(name: '参军', url: ''),
        ],
      ),

      // 待审批数据
      FreeInternshipApplicationModel(
        id: '4',
        studentName: '李成儒',
        studentAvatar: AppConstants.avatar1,
        studentDestination: '参军',
        applyDate: '2025-04-23 22:12',
        status: '待审批',
        attachments: [
          AttachmentInfo(name: '参军', url: ''),
        ],
      ),
      FreeInternshipApplicationModel(
        id: '5',
        studentName: '李成儒',
        studentAvatar: AppConstants.avatar2,
        studentDestination: '参军',
        applyDate: '2025-04-23 22:12',
        status: '待审批',
        attachments: [
          AttachmentInfo(name: '参军', url: ''),
        ],
      ),
      FreeInternshipApplicationModel(
        id: '6',
        studentName: '李成儒',
        studentAvatar: AppConstants.avatar3,
        studentDestination: '参军',
        applyDate: '2025-04-23 22:12',
        status: '待审批',
        attachments: [
          AttachmentInfo(name: '参军', url: ''),
        ],
      ),
    ];
  }
}

class AttachmentInfo {
  final String name;
  final String url;

  AttachmentInfo({
    required this.name,
    required this.url,
  });

  factory AttachmentInfo.fromJson(Map<String, dynamic> json) {
    return AttachmentInfo(
      name: json['name'],
      url: json['url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'url': url,
    };
  }
}
