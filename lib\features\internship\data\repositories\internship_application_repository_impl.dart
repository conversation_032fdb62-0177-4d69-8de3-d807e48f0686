/// -----
/// internship_application_repository_impl.dart
///
/// 实习申请仓库具体实现
/// 实现实习申请相关的数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions/server_exception.dart';
import '../../../../core/error/exceptions/network_exception.dart';
import '../../../../core/error/exceptions/auth_exception.dart';
import '../../../../core/error/failures/failure.dart' show Failure;
import '../../../../core/error/failures/server_failure.dart';
import '../../../../core/error/failures/network_failure.dart';
import '../../../../core/error/failures/auth_failure.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/repositories/internship_application_repository.dart';
import '../datasources/remote/internship_application_remote_data_source.dart';
import '../models/internship_application_request_model.dart';

/// 实习申请仓库具体实现
///
/// 处理实习申请相关的数据操作，包括网络检查和异常处理
class InternshipApplicationRepositoryImpl implements InternshipApplicationRepository {
  /// 远程数据源
  final InternshipApplicationRemoteDataSource remoteDataSource;
  
  /// 网络信息检查器
  final NetworkInfo networkInfo;

  /// 构造函数
  ///
  /// 参数:
  ///   - [remoteDataSource]: 远程数据源实例
  ///   - [networkInfo]: 网络信息检查器实例
  const InternshipApplicationRepositoryImpl({
    required this.remoteDataSource,
    required this.networkInfo,
  });

  @override
  Future<Either<Failure, bool>> submitApplication(
    InternshipApplicationRequestModel request,
  ) async {
    // 检查网络连接
    if (await networkInfo.isConnected) {
      try {
        // 调用远程数据源提交申请
        final response = await remoteDataSource.submitApplication(request);
        
        // 检查响应是否成功
        if (response.isSuccess) {
          return const Right(true);
        } else {
          return Left(ServerFailure(response.errorMessage ?? '提交失败'));
        }
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } on AuthException catch (e) {
        return Left(UnauthorizedFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('提交申请失败: $e'));
      }
    } else {
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }
}
