import 'package:get_it/get_it.dart';
import '../../../core/storage/local_storage.dart';
import '../../../features/auth/domain/usecases/get_current_user_usecase.dart';
import '../presentation/bloc/splash_bloc.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化启动页模块依赖
///
/// 注册启动页模块的BLoC
Future<void> setupSplashDependencies() async {
  // BLoC
  getIt.registerFactory<SplashBloc>(
    () => SplashBloc(
      getCurrentUserUseCase: getIt<GetCurrentUserUseCase>(),
      localStorage: getIt<LocalStorage>(),
    ),
  );
}
