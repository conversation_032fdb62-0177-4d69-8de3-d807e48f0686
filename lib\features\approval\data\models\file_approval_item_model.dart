/// -----
/// file_approval_item_model.dart
///
/// 文件审批项数据模型，用于存储文件审批的数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../../core/constants/constants.dart';
import '../../../../core/widgets/approval_list_item.dart';

/// 文件审批项数据模型
class FileApprovalItemModel implements ApprovalItemData {
  @override
  final String id;
  
  @override
  final String studentName;
  
  @override
  final String studentAvatar;
  
  final String fileType; // 文件类型（三方协议、告家长通知书等）
  final String fileName; // 文件名称
  final String filePreviewUrl; // 文件预览图URL
  
  @override
  final String status; // 待审批、已通过、已驳回
  
  @override
  final String submitTime; // 提交时间
  
  final String? rejectReason; // 驳回原因

  FileApprovalItemModel({
    required this.id,
    required this.studentName,
    required this.studentAvatar,
    required this.fileType,
    required this.fileName,
    required this.filePreviewUrl,
    required this.status,
    required this.submitTime,
    this.rejectReason,
  });

  // 从JSON创建模型
  factory FileApprovalItemModel.fromJson(Map<String, dynamic> json) {
    return FileApprovalItemModel(
      id: json['id'],
      studentName: json['studentName'],
      studentAvatar: json['studentAvatar'],
      fileType: json['fileType'],
      fileName: json['fileName'],
      filePreviewUrl: json['filePreviewUrl'],
      status: json['status'],
      submitTime: json['submitTime'],
      rejectReason: json['rejectReason'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentName': studentName,
      'studentAvatar': studentAvatar,
      'fileType': fileType,
      'fileName': fileName,
      'filePreviewUrl': filePreviewUrl,
      'status': status,
      'submitTime': submitTime,
      'rejectReason': rejectReason,
    };
  }

  /// 根据文件类型获取示例数据
  static List<FileApprovalItemModel> getSampleDataByFileType(String fileType) {
    final Map<String, List<FileApprovalItemModel>> sampleData = {
      'tripartite_agreement': [
        // 待审批数据
        FileApprovalItemModel(
          id: '1',
          studentName: '李成儒',
          studentAvatar: AppConstants.avatar1,
          fileType: '三方协议',
          fileName: '三方协议.pdf',
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          status: '待审批',
          submitTime: '2025-04-23 22:12',
        ),
        FileApprovalItemModel(
          id: '2',
          studentName: '李成儒',
          studentAvatar: AppConstants.avatar2,
          fileType: '三方协议',
          fileName: '三方协议.pdf',
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          status: '待审批',
          submitTime: '2025-04-23 22:12',
        ),
        FileApprovalItemModel(
          id: '3',
          studentName: '李成儒',
          studentAvatar: AppConstants.avatar3,
          fileType: '三方协议',
          fileName: '三方协议.pdf',
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          status: '待审批',
          submitTime: '2025-04-23 22:12',
        ),
        // 已审批数据
        FileApprovalItemModel(
          id: '4',
          studentName: '王小明',
          studentAvatar: AppConstants.avatar1,
          fileType: '三方协议',
          fileName: '三方协议.pdf',
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          status: '已通过',
          submitTime: '2025-04-22 15:30',
        ),
        FileApprovalItemModel(
          id: '5',
          studentName: '张小红',
          studentAvatar: AppConstants.avatar2,
          fileType: '三方协议',
          fileName: '三方协议.pdf',
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          status: '已驳回',
          submitTime: '2025-04-21 09:15',
          rejectReason: '文件不清晰，请重新上传',
        ),
      ],
      'parent_notice': [
        // 告家长通知书示例数据
        FileApprovalItemModel(
          id: '6',
          studentName: '李成儒',
          studentAvatar: AppConstants.avatar1,
          fileType: '告家长通知书',
          fileName: '告家长通知书.pdf',
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          status: '待审批',
          submitTime: '2025-04-23 22:12',
        ),
      ],
      'insurance': [
        // 实习保险单示例数据
        FileApprovalItemModel(
          id: '7',
          studentName: '李成儒',
          studentAvatar: AppConstants.avatar1,
          fileType: '实习保险单',
          fileName: '实习保险单.pdf',
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          status: '待审批',
          submitTime: '2025-04-23 22:12',
        ),
      ],
      'self_agreement': [
        // 自助协议申请表示例数据
        FileApprovalItemModel(
          id: '8',
          studentName: '李成儒',
          studentAvatar: AppConstants.avatar1,
          fileType: '自助协议申请表',
          fileName: '自助协议申请表.pdf',
          filePreviewUrl: 'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
          status: '待审批',
          submitTime: '2025-04-23 22:12',
        ),
      ],
    };

    return sampleData[fileType] ?? [];
  }

  /// 获取所有示例数据
  static List<FileApprovalItemModel> getAllSampleData() {
    final List<FileApprovalItemModel> allData = [];
    final fileTypes = ['tripartite_agreement', 'parent_notice', 'insurance', 'self_agreement'];
    
    for (String fileType in fileTypes) {
      allData.addAll(getSampleDataByFileType(fileType));
    }
    
    return allData;
  }
}
