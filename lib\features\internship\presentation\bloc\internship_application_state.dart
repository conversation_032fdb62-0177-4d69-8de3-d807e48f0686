/// -----
/// internship_application_state.dart
///
/// 实习申请页面状态定义
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/internship/data/models/company_info.dart';
import 'package:flutter_demo/features/internship/data/models/position_info.dart';
import 'package:flutter_demo/features/internship/data/models/internship_info.dart';

/// 实习申请状态
class InternshipApplicationState extends Equatable {
  /// 实习计划ID
  final String planId;

  /// 加载状态
  final bool isLoading;

  /// 提交状态
  final bool isSubmitting;

  /// 错误信息
  final String? errorMessage;

  /// 成功信息
  final String? successMessage;

  /// 企业信息
  final CompanyInfo companyInfo;

  /// 岗位信息
  final PositionInfo positionInfo;

  /// 实习信息
  final InternshipInfo internshipInfo;

  /// 表单验证错误
  final Map<String, String> validationErrors;

  /// 是否表单有效
  final bool isFormValid;

  const InternshipApplicationState({
    required this.planId,
    this.isLoading = false,
    this.isSubmitting = false,
    this.errorMessage,
    this.successMessage,
    required this.companyInfo,
    required this.positionInfo,
    required this.internshipInfo,
    this.validationErrors = const {},
    this.isFormValid = false,
  });

  /// 初始状态
  factory InternshipApplicationState.initial({String planId = ''}) {
    return InternshipApplicationState(
      planId: planId,
      companyInfo: CompanyInfo.sampleData(),
      positionInfo: PositionInfo.sampleData(),
      internshipInfo: InternshipInfo.sampleData(),
    );
  }

  /// 复制状态
  InternshipApplicationState copyWith({
    String? planId,
    bool? isLoading,
    bool? isSubmitting,
    String? errorMessage,
    String? successMessage,
    CompanyInfo? companyInfo,
    PositionInfo? positionInfo,
    InternshipInfo? internshipInfo,
    Map<String, String>? validationErrors,
    bool? isFormValid,
  }) {
    return InternshipApplicationState(
      planId: planId ?? this.planId,
      isLoading: isLoading ?? this.isLoading,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      errorMessage: errorMessage,
      successMessage: successMessage,
      companyInfo: companyInfo ?? this.companyInfo,
      positionInfo: positionInfo ?? this.positionInfo,
      internshipInfo: internshipInfo ?? this.internshipInfo,
      validationErrors: validationErrors ?? this.validationErrors,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }

  /// 清除消息
  InternshipApplicationState clearMessages() {
    return copyWith(
      errorMessage: null,
      successMessage: null,
    );
  }

  @override
  List<Object?> get props => [
        planId,
        isLoading,
        isSubmitting,
        errorMessage,
        successMessage,
        companyInfo,
        positionInfo,
        internshipInfo,
        validationErrors,
        isFormValid,
      ];
}
