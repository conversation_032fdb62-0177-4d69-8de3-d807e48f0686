/// -----------------------------------------------------------------------------
/// score_item.dart
/// 
/// 可复用的评分项组件，用于显示各种评分项目，包括标题、描述、分数
/// 
/// <AUTHOR>
/// @date 2025-04-10
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';

class ScoreItem extends StatelessWidget {
  final String title;
  final String? description;
  final String? weight;
  final String score;
  final bool hasArrow;
  final VoidCallback? onTap;
  final Color scoreColor;

  const ScoreItem({
    Key? key,
    required this.title,
    this.description,
    this.weight,
    required this.score,
    this.hasArrow = false,
    this.onTap,
    this.scoreColor = Colors.black87,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isDetailView = description != null;

    return InkWell(
      onTap: onTap,
      child: Container(
        color: Colors.white,
        margin: isDetailView ? const EdgeInsets.only(top: 1) : null,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: isDetailView 
            ? _buildDetailItem(context) 
            : _buildSimpleItem(context),
      ),
    );
  }

  Widget _buildSimpleItem(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            color: Colors.black87,
          ),
        ),
        Row(
          children: [
            Text(
              score,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: score == '暂未评分' ? Colors.grey : scoreColor,
              ),
            ),
            if (hasArrow)
              const Padding(
                padding: EdgeInsets.only(left: 8),
                child: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey,
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailItem(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                weight != null ? '$title（占比$weight）' : title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (description != null) 
                const SizedBox(height: 4),
              if (description != null)
                Text(
                  description!,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ),
        Text(
          '$score分',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: scoreColor,
          ),
        ),
      ],
    );
  }
} 