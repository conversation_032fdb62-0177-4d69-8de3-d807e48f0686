/// -----
/// attachment_item.dart
/// 
/// 附件项组件，用于显示附件项
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

class AttachmentItem extends StatelessWidget {
  final String name;
  final VoidCallback? onTap;
  
  const AttachmentItem({
    Key? key,
    required this.name,
    this.onTap,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // 根据文件名后缀确定图标
    IconData fileIcon = Icons.insert_drive_file;
    Color iconColor = Colors.blue;
    
    if (name.toLowerCase().endsWith('.pdf')) {
      fileIcon = Icons.picture_as_pdf;
      iconColor = Colors.red;
    } else if (name.toLowerCase().endsWith('.docx') || name.toLowerCase().endsWith('.doc')) {
      fileIcon = Icons.description;
      iconColor = Colors.blue;
    } else if (name.toLowerCase().endsWith('.xlsx') || name.toLowerCase().endsWith('.xls')) {
      fileIcon = Icons.table_chart;
      iconColor = Colors.green;
    } else if (name.toLowerCase().endsWith('.pptx') || name.toLowerCase().endsWith('.ppt')) {
      fileIcon = Icons.slideshow;
      iconColor = Colors.orange;
    } else if (name.toLowerCase().endsWith('.jpg') || 
               name.toLowerCase().endsWith('.jpeg') || 
               name.toLowerCase().endsWith('.png')) {
      fileIcon = Icons.image;
      iconColor = Colors.purple;
    }
    
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                fileIcon,
                color: iconColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                name,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ),
            Icon(
              Icons.more_horiz,
              color: Colors.grey[400],
              size: 20,
            ),
          ],
        ),
      ),
    );
  }
}
