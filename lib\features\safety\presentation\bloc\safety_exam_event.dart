/// -----
/// safety_exam_event.dart
/// 
/// 安全教育考试事件类
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 安全教育考试事件基类
///
/// 所有安全教育考试相关事件的基类
abstract class SafetyExamEvent extends Equatable {
  /// 构造函数
  const SafetyExamEvent();

  @override
  List<Object?> get props => [];
}

/// 加载安全教育考试题目事件
///
/// 触发加载安全教育考试题目的操作
class LoadExamQuestionsEvent extends SafetyExamEvent {}

/// 选择答案事件（单选题）
///
/// 用户选择了一个答案选项
class SelectAnswerEvent extends SafetyExamEvent {
  /// 选择的答案选项ID
  final String selectedOptionId;

  /// 构造函数
  const SelectAnswerEvent({
    required this.selectedOptionId,
  });

  @override
  List<Object?> get props => [selectedOptionId];
}

/// 切换多选答案事件（多选题）
///
/// 用户切换了多选题的某个选项状态
class ToggleMultipleAnswerEvent extends SafetyExamEvent {
  /// 要切换的选项ID
  final String optionId;

  /// 构造函数
  const ToggleMultipleAnswerEvent({
    required this.optionId,
  });

  @override
  List<Object?> get props => [optionId];
}

/// 下一题事件
///
/// 用户点击了"下一题"按钮
class NextQuestionEvent extends SafetyExamEvent {}

/// 查看结果事件
///
/// 用户点击了"查看结果"按钮
class ViewResultsEvent extends SafetyExamEvent {}

/// 保存考试记录事件
///
/// 保存学生考试记录到后端
class SaveExamRecordEvent extends SafetyExamEvent {}

/// 跳转到指定题目事件
///
/// 用户从答题卡中点击了某个题目编号，跳转到该题目
class JumpToQuestionEvent extends SafetyExamEvent {
  /// 要跳转到的题目索引
  final int questionIndex;

  /// 构造函数
  const JumpToQuestionEvent({
    required this.questionIndex,
  });

  @override
  List<Object?> get props => [questionIndex];
}
