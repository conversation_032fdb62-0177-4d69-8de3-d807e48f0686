/// -----
/// internship_application_example.dart
///
/// 实习申请页面使用示例
/// 展示如何正确使用重构后的InternshipApplicationScreen
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_event.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_application_screen.dart';

/// 实习申请页面使用示例
/// 
/// 展示如何正确使用重构后的InternshipApplicationScreen
class InternshipApplicationExample extends StatelessWidget {
  const InternshipApplicationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('实习申请示例'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () => _navigateToInternshipApplication(context),
          child: const Text('打开实习申请页面'),
        ),
      ),
    );
  }

  /// 导航到实习申请页面
  void _navigateToInternshipApplication(BuildContext context) {
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => BlocProvider(
            create: (context) => GetIt.instance<InternshipApplicationBloc>()
              ..add(const InitializeApplicationEvent('123')),
            child: const InternshipApplicationScreen(planId: '123'),
          ),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('导航失败: $e')),
      );
    }
  }
}
