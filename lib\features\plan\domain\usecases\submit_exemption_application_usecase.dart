/// -----
/// submit_exemption_application_usecase.dart
///
/// 提交免实习申请用例
/// 处理提交免实习申请的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// 第三方库
import 'package:dartz/dartz.dart';

// 项目内部库
import 'package:flutter_demo/core/error/failures/failure.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_request_model.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_response_model.dart';
import 'package:flutter_demo/features/plan/domain/repositories/exemption_application_repository.dart';

/// 提交免实习申请用例参数
class SubmitExemptionApplicationParams {
  /// 实习计划ID
  final int planId;
  
  /// 免实习理由
  final String reason;
  
  /// 证明材料文件URL
  final String fileUrl;

  const SubmitExemptionApplicationParams({
    required this.planId,
    required this.reason,
    required this.fileUrl,
  });

  /// 转换为请求模型
  ExemptionApplicationRequestModel toRequestModel() {
    return ExemptionApplicationRequestModel(
      planId: planId,
      reason: reason,
      fileUrl: fileUrl,
    );
  }
}

/// 提交免实习申请用例
///
/// 处理提交免实习申请的业务逻辑
class SubmitExemptionApplicationUseCase implements UseCase<ExemptionApplicationResponseModel, SubmitExemptionApplicationParams> {
  final ExemptionApplicationRepository _repository;

  const SubmitExemptionApplicationUseCase({
    required ExemptionApplicationRepository repository,
  }) : _repository = repository;

  @override
  Future<Either<Failure, ExemptionApplicationResponseModel>> call(SubmitExemptionApplicationParams params) async {
    // 将参数转换为请求模型
    final request = params.toRequestModel();
    
    // 调用仓库提交申请
    return await _repository.submitExemptionApplication(request);
  }
}
