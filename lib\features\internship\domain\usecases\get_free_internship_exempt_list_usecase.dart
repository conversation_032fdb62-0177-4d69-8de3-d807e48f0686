/// -----
/// get_free_internship_exempt_list_usecase.dart
///
/// 获取免实习申请列表用例
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/free_internship_exempt.dart';
import '../repositories/free_internship_exempt_repository.dart';

/// 获取免实习申请列表用例
/// 
/// 封装获取免实习申请列表的业务逻辑
class GetFreeInternshipExemptListUseCase implements UseCase<List<FreeInternshipExempt>, GetFreeInternshipExemptListParams> {
  final FreeInternshipExemptRepository _repository;

  GetFreeInternshipExemptListUseCase(this._repository);

  @override
  Future<Either<Failure, List<FreeInternshipExempt>>> call(GetFreeInternshipExemptListParams params) async {
    return await _repository.getFreeInternshipExemptList(
      planId: params.planId,
      type: params.type,
    );
  }
}

/// 获取免实习申请列表用例参数
class GetFreeInternshipExemptListParams extends Equatable {
  /// 实习计划ID
  final int planId;
  
  /// 申请类型（0:待审批，1:已审批）
  final int type;

  const GetFreeInternshipExemptListParams({
    required this.planId,
    required this.type,
  });

  @override
  List<Object> get props => [planId, type];
}
