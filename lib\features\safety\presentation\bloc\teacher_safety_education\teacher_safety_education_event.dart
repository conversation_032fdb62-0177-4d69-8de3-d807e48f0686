/// -----
/// teacher_safety_education_event.dart
/// 
/// 教师端安全教育考试事件类
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 教师端安全教育考试事件基类
/// 
/// 所有教师端安全教育考试相关事件的基类
abstract class TeacherSafetyEducationEvent extends Equatable {
  const TeacherSafetyEducationEvent();

  @override
  List<Object?> get props => [];
}

/// 加载教师端安全教育考试数据事件
/// 
/// 触发加载指定实习计划的安全教育考试数据
class LoadTeacherSafetyEducationDataEvent extends TeacherSafetyEducationEvent {
  /// 实习计划ID
  final String planId;

  const LoadTeacherSafetyEducationDataEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}

/// 刷新教师端安全教育考试数据事件
/// 
/// 触发刷新当前实习计划的安全教育考试数据
class RefreshTeacherSafetyEducationDataEvent extends TeacherSafetyEducationEvent {
  /// 实习计划ID
  final String planId;

  const RefreshTeacherSafetyEducationDataEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}

/// 学生列表项点击事件
/// 
/// 当用户点击学生列表项时触发，用于跳转到学生考试详情页面
class StudentItemClickedEvent extends TeacherSafetyEducationEvent {
  /// 学生ID
  final String studentId;
  
  /// 学生姓名
  final String studentName;
  
  /// 试题记录ID
  final String recordId;

  const StudentItemClickedEvent({
    required this.studentId,
    required this.studentName,
    required this.recordId,
  });

  @override
  List<Object?> get props => [studentId, studentName, recordId];
}
