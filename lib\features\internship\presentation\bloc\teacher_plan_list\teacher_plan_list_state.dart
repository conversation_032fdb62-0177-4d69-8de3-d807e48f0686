/// -----
/// teacher_plan_list_state.dart
/// 
/// 教师实习计划列表状态定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../../domain/entities/internship_plan.dart';

/// 教师实习计划列表状态基类
abstract class TeacherPlanListState extends Equatable {
  const TeacherPlanListState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class TeacherPlanListInitialState extends TeacherPlanListState {
  const TeacherPlanListInitialState();
}

/// 加载中状态
class TeacherPlanListLoadingState extends TeacherPlanListState {
  const TeacherPlanListLoadingState();
}

/// 加载成功状态
class TeacherPlanListLoadedState extends TeacherPlanListState {
  final List<InternshipPlan> plans;

  const TeacherPlanListLoadedState(this.plans);

  @override
  List<Object?> get props => [plans];
}

/// 加载失败状态
class TeacherPlanListErrorState extends TeacherPlanListState {
  final String message;

  const TeacherPlanListErrorState(this.message);

  @override
  List<Object?> get props => [message];
}

/// 刷新成功状态
class TeacherPlanListRefreshSuccessState extends TeacherPlanListLoadedState {
  const TeacherPlanListRefreshSuccessState(List<InternshipPlan> plans) : super(plans);
}

/// 刷新失败状态
class TeacherPlanListRefreshErrorState extends TeacherPlanListErrorState {
  const TeacherPlanListRefreshErrorState(String message) : super(message);
}

/// 认证失败状态
class TeacherPlanListAuthFailureState extends TeacherPlanListErrorState {
  const TeacherPlanListAuthFailureState(String message) : super(message);
}
