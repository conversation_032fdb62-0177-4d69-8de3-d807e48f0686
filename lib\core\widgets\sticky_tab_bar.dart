/// -----
/// sticky_tab_bar.dart
///
/// 可固定在顶部的标签栏组件，用于在NestedScrollView中使用
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';

/// 标签栏委托类
///
/// 用于创建可固定在顶部的标签栏
class StickyTabBarDelegate extends SliverPersistentHeaderDelegate {
  /// 标签栏
  final TabBar tabBar;

  /// 背景颜色
  final Color backgroundColor;

  /// 创建标签栏委托
  ///
  /// [tabBar] 标签栏
  /// [backgroundColor] 背景颜色，默认为白色
  StickyTabBarDelegate({
    required this.tabBar,
    this.backgroundColor = Colors.white,
  });

  @override
  double get minExtent => tabBar.preferredSize.height;

  @override
  double get maxExtent => tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: backgroundColor,
      child: tabBar,
    );
  }

  @override
  bool shouldRebuild(StickyTabBarDelegate oldDelegate) {
    return tabBar != oldDelegate.tabBar || backgroundColor != oldDelegate.backgroundColor;
  }
}

/// 带数字统计的标签
///
/// 用于在标签栏中显示带有数字统计的标签
class CountedTab extends StatelessWidget {
  /// 标签文本
  final String text;

  /// 数量
  final int count;

  /// 是否显示数字统计为单独的标签
  ///
  /// 如果为true，则在标签右上角显示数字统计
  /// 如果为false，则在标签文本中显示数字统计，如"未提交 (20)"
  final bool showCountAsBadge;

  /// 数字统计标签的颜色
  final Color badgeColor;

  /// 数字统计标签的文本颜色
  final Color badgeTextColor;

  /// 创建带数字统计的标签
  ///
  /// [text] 标签文本
  /// [count] 数量
  /// [showCountAsBadge] 是否显示数字统计为单独的标签，默认为false
  /// [badgeColor] 数字统计标签的颜色，默认为红色
  /// [badgeTextColor] 数字统计标签的文本颜色，默认为白色
  const CountedTab({
    Key? key,
    required this.text,
    required this.count,
    this.showCountAsBadge = false,
    this.badgeColor = Colors.red,
    this.badgeTextColor = Colors.white,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (showCountAsBadge) {
      return Tab(
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Text(text),
            if (count > 0)
              Positioned(
                right: -15,
                top: -8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
                  decoration: BoxDecoration(
                    color: badgeColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '$count',
                    style: TextStyle(
                      color: badgeTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    } else {
      return Tab(text: '$text (${count > 0 ? count : 0})');
    }
  }
}
