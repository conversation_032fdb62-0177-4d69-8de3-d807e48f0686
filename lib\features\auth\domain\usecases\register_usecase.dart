import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

/// 注册用例
///
/// 处理用户注册的业务逻辑
class RegisterUseCase implements UseCase<bool, RegisterParams> {
  final AuthRepository _repository;

  RegisterUseCase(this._repository);

  @override
  Future<Either<Failure, bool>> call(RegisterParams params) async {
    return await _repository.register(
      phone: params.phone,
      code: params.code,
      password: params.password,
    );
  }
}

/// 注册参数
///
/// 包含注册所需的参数
class RegisterParams extends Equatable {
  final String phone;
  final String code;
  final String password;

  const RegisterParams({
    required this.phone,
    required this.code,
    required this.password,
  });

  @override
  List<Object?> get props => [phone, code, password];
}
