/// -----
/// exemption_application_event.dart
///
/// 免实习申请BLoC事件定义
/// 定义免实习申请相关的用户操作事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// 第三方库
import 'package:equatable/equatable.dart';

/// 免实习申请事件基类
///
/// 所有免实习申请相关的事件都继承自此类
abstract class ExemptionApplicationEvent extends Equatable {
  const ExemptionApplicationEvent();

  @override
  List<Object?> get props => [];
}

/// 提交免实习申请事件
///
/// 当用户点击提交按钮时触发
class SubmitExemptionApplicationEvent extends ExemptionApplicationEvent {
  /// 实习计划ID
  final int planId;
  
  /// 免实习理由
  final String reason;
  
  /// 证明材料文件URL
  final String fileUrl;

  const SubmitExemptionApplicationEvent({
    required this.planId,
    required this.reason,
    required this.fileUrl,
  });

  @override
  List<Object?> get props => [planId, reason, fileUrl];
}

/// 重置申请状态事件
///
/// 重置BLoC状态到初始状态
class ResetExemptionApplicationEvent extends ExemptionApplicationEvent {
  const ResetExemptionApplicationEvent();
}
