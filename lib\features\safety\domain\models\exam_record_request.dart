/// -----
/// exam_record_request.dart
/// 
/// 考试记录保存请求模型
///
/// <AUTHOR>
/// @date 2025-06-19
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:convert';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';

/// 考试题目记录模型
/// 
/// 用于保存单个题目的答题记录
class ExamQuestionRecord extends Equatable {
  /// 正确答案（如A、A,B）
  final String correctAnswer;
  
  /// 题目ID
  final int id;
  
  /// 是否正确（1:正确 0:错误）
  final int isCorrect;
  
  /// 选项JSON字符串，如：[{'A':'xxx','B':'xxx'}]
  final String optionsJson;
  
  /// 分数
  final int score;
  
  /// 学生答案
  final String studentAnswer;
  
  /// 题干（支持富文本）
  final String title;

  const ExamQuestionRecord({
    required this.correctAnswer,
    required this.id,
    required this.isCorrect,
    required this.optionsJson,
    required this.score,
    required this.studentAnswer,
    required this.title,
  });

  @override
  List<Object?> get props => [
    correctAnswer,
    id,
    isCorrect,
    optionsJson,
    score,
    studentAnswer,
    title,
  ];

  /// 将对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'correctAnswer': correctAnswer,
      'id': id,
      'isCorrect': isCorrect,
      'optionsJson': optionsJson,
      'score': score,
      'studentAnswer': studentAnswer,
      'title': title,
    };
  }

  /// 从SafetyExamQuestion和用户答案创建ExamQuestionRecord
  factory ExamQuestionRecord.fromQuestionAndAnswer({
    required SafetyExamQuestion question,
    required List<String> userAnswer,
    required bool isCorrect,
    required int questionScore,
  }) {
    // 构造optionsJson字符串
    final optionsMap = <String, String>{};
    for (final option in question.options) {
      optionsMap[option.id] = option.content;
    }
    final optionsJson = jsonEncode([optionsMap]);

    return ExamQuestionRecord(
      correctAnswer: question.correctAnswers.join(''),
      id: int.parse(question.id),
      isCorrect: isCorrect ? 1 : 0,
      optionsJson: optionsJson,
      score: questionScore,
      studentAnswer: userAnswer.join(''),
      title: question.title,
    );
  }
}

/// 考试记录保存请求模型
/// 
/// 用于向后端保存完整的考试记录
class ExamRecordRequest extends Equatable {
  /// 试题及记录列表
  final List<ExamQuestionRecord> examQuestionList;
  
  /// 是否通过（0=否，1=是）
  final int passStatus;
  
  /// 关联实习计划ID
  final int planId;
  
  /// 实际得分
  final int score;

  const ExamRecordRequest({
    required this.examQuestionList,
    required this.passStatus,
    required this.planId,
    required this.score,
  });

  @override
  List<Object?> get props => [
    examQuestionList,
    passStatus,
    planId,
    score,
  ];

  /// 将对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'examQuestionList': examQuestionList.map((e) => e.toJson()).toList(),
      'passStatus': passStatus,
      'planId': planId,
      'score': score,
    };
  }
}
