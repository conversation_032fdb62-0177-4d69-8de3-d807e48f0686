/// -----
/// exemption_application_repository.dart
///
/// 免实习申请仓库接口
/// 定义免实习申请相关的业务操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// 第三方库
import 'package:dartz/dartz.dart';

// 项目内部库
import 'package:flutter_demo/core/error/failures/failure.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_request_model.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_response_model.dart';

/// 免实习申请仓库接口
///
/// 定义免实习申请相关的业务操作方法
abstract class ExemptionApplicationRepository {
  /// 提交免实习申请
  ///
  /// [request] 免实习申请请求数据
  /// 返回Either类型，Left为失败信息，Right为申请响应数据
  Future<Either<Failure, ExemptionApplicationResponseModel>> submitExemptionApplication(
    ExemptionApplicationRequestModel request,
  );
}
