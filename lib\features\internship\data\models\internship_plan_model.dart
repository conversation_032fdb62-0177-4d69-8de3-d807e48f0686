/// -----
/// internship_plan_model.dart
///
/// 实习计划数据模型，用于存储实习计划的数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:convert';
import 'package:equatable/equatable.dart';
import '../../domain/entities/internship_plan.dart';

class InternshipPlanModel extends Equatable {
  final String id;
  final String academicYear;
  final String grade;
  final int planType;
  final int practiceMode;
  final String planName;
  final String planCode;
  final String facultyId;
  final String? facultyName;
  final String majorId;
  final String? majorName;
  final String? majorDirection;
  final String semester;
  final String? schoolId;
  final int status;
  final int dr;
  final String createPerson;
  final int createTime;
  final int startTime;
  final int endTime;
  final String level;
  final String subsidy;
  final String majorAdmin;
  final String? majorAdminName;
  final String? file;
  final String? aiSummary;
  final int planStatus;
  final String? requiredFiles;
  final List<String>? requiredFileList;
  final int signNum;
  final int dailyReportNum;
  final int weeklyReportNum;
  final int monthlyReportNum;
  final int summaryNum;
  final double scoreEnterpriseTeacher;
  final double scoreSchoolTeacher;
  final double scoreProcess;
  final int? safeExamQuestionCount;
  final int? checkNum;
  final int? mulCheckNum;
  final int? safeExamEachTimeCount;

  const InternshipPlanModel({
    required this.id,
    required this.academicYear,
    required this.grade,
    required this.planType,
    required this.practiceMode,
    required this.planName,
    required this.planCode,
    required this.facultyId,
    this.facultyName,
    required this.majorId,
    this.majorName,
    this.majorDirection,
    required this.semester,
    this.schoolId,
    required this.status,
    required this.dr,
    required this.createPerson,
    required this.createTime,
    required this.startTime,
    required this.endTime,
    required this.level,
    required this.subsidy,
    required this.majorAdmin,
    this.majorAdminName,
    this.file,
    this.aiSummary,
    required this.planStatus,
    this.requiredFiles,
    this.requiredFileList,
    required this.signNum,
    required this.dailyReportNum,
    required this.weeklyReportNum,
    required this.monthlyReportNum,
    required this.summaryNum,
    required this.scoreEnterpriseTeacher,
    required this.scoreSchoolTeacher,
    required this.scoreProcess,
    this.safeExamQuestionCount,
    this.checkNum,
    this.mulCheckNum,
    this.safeExamEachTimeCount,
  });

  // 从JSON创建模型
  factory InternshipPlanModel.fromJson(Map<String, dynamic> json) {
    try {
      // 安全的字符串转换函数
      String safeToString(dynamic value, String fieldName) {
        if (value == null) {
          return '';
        }
        if (value is String) {
          return value;
        }
        if (value is Map || value is List) {
          throw FormatException('字段 $fieldName 期望 String 类型，但收到 ${value.runtimeType}: $value');
        }
        return value.toString();
      }

      // 安全的整数转换函数
      int safeToInt(dynamic value, String fieldName, {int defaultValue = 0}) {
        if (value == null) {
          return defaultValue;
        }
        if (value is int) {
          return value;
        }
        if (value is String) {
          final parsed = int.tryParse(value);
          if (parsed != null) {
            return parsed;
          }
        }
        if (value is double) {
          return value.toInt();
        }
        if (value is Map || value is List) {
          throw FormatException('字段 $fieldName 期望 int 类型，但收到 ${value.runtimeType}: $value');
        }
        return defaultValue;
      }

      // 安全的双精度浮点数转换函数
      double safeToDouble(dynamic value, String fieldName, {double defaultValue = 0.0}) {
        if (value == null) {
          return defaultValue;
        }
        if (value is double) {
          return value;
        }
        if (value is int) {
          return value.toDouble();
        }
        if (value is String) {
          final parsed = double.tryParse(value);
          if (parsed != null) {
            return parsed;
          }
        }
        if (value is Map || value is List) {
          throw FormatException('字段 $fieldName 期望 double 类型，但收到 ${value.runtimeType}: $value');
        }
        return defaultValue;
      }

      // 安全的字符串列表转换函数
      List<String>? safeToStringList(dynamic value, String fieldName) {
        if (value == null) {
          return null;
        }
        if (value is List) {
          return value.map((item) => item?.toString() ?? '').toList();
        }
        if (value is String) {
          // 如果是字符串，尝试解析为JSON数组
          try {
            final decoded = jsonDecode(value);
            if (decoded is List) {
              return decoded.map((item) => item?.toString() ?? '').toList();
            }
          } catch (e) {
            // 如果解析失败，将字符串作为单个元素
            return [value];
          }
        }
        throw FormatException('字段 $fieldName 期望 List<String> 类型，但收到 ${value.runtimeType}: $value');
      }

      return InternshipPlanModel(
        id: safeToString(json['id'], 'id'),
        academicYear: safeToString(json['academicYear'], 'academicYear'),
        grade: safeToString(json['grade'], 'grade'),
        planType: safeToInt(json['planType'], 'planType'),
        practiceMode: safeToInt(json['practiceMode'], 'practiceMode'),
        planName: safeToString(json['planName'], 'planName'),
        planCode: safeToString(json['planCode'], 'planCode'),
        facultyId: safeToString(json['facultyId'], 'facultyId'),
        facultyName: json['facultyName'] != null ? safeToString(json['facultyName'], 'facultyName') : null,
        majorId: safeToString(json['majorId'], 'majorId'),
        majorName: json['majorName'] != null ? safeToString(json['majorName'], 'majorName') : null,
        majorDirection: json['majorDirection'] != null ? safeToString(json['majorDirection'], 'majorDirection') : null,
        semester: safeToString(json['semester'], 'semester'),
        schoolId: json['schoolId'] != null ? safeToString(json['schoolId'], 'schoolId') : null,
        status: safeToInt(json['status'], 'status'),
        dr: safeToInt(json['dr'], 'dr'),
        createPerson: safeToString(json['createPerson'], 'createPerson'),
        createTime: safeToInt(json['createTime'], 'createTime'),
        startTime: safeToInt(json['startTime'], 'startTime'),
        endTime: safeToInt(json['endTime'], 'endTime'),
        level: safeToString(json['level'], 'level'),
        subsidy: safeToString(json['subsidy'], 'subsidy'),
        majorAdmin: safeToString(json['majorAdmin'], 'majorAdmin'),
        majorAdminName: json['majorAdminName'] != null ? safeToString(json['majorAdminName'], 'majorAdminName') : null,
        file: json['file'] != null ? safeToString(json['file'], 'file') : null,
        aiSummary: json['aiSummary'] != null ? safeToString(json['aiSummary'], 'aiSummary') : null,
        planStatus: safeToInt(json['planStatus'], 'planStatus'),
        requiredFiles: json['requiredFiles'] != null ? safeToString(json['requiredFiles'], 'requiredFiles') : null,
        requiredFileList: safeToStringList(json['requiredFileList'], 'requiredFileList'),
        signNum: safeToInt(json['signNum'], 'signNum'),
        dailyReportNum: safeToInt(json['dailyReportNum'], 'dailyReportNum'),
        weeklyReportNum: safeToInt(json['weeklyReportNum'], 'weeklyReportNum'),
        monthlyReportNum: safeToInt(json['monthlyReportNum'], 'monthlyReportNum'),
        summaryNum: safeToInt(json['summaryNum'], 'summaryNum'),
        scoreEnterpriseTeacher: safeToDouble(json['scoreEnterpriseTeacher'], 'scoreEnterpriseTeacher'),
        scoreSchoolTeacher: safeToDouble(json['scoreSchoolTeacher'], 'scoreSchoolTeacher'),
        scoreProcess: safeToDouble(json['scoreProcess'], 'scoreProcess'),
        safeExamQuestionCount: json['safeExamQuestionCount'] != null ? safeToInt(json['safeExamQuestionCount'], 'safeExamQuestionCount') : null,
        checkNum: json['checkNum'] != null ? safeToInt(json['checkNum'], 'checkNum') : null,
        mulCheckNum: json['mulCheckNum'] != null ? safeToInt(json['mulCheckNum'], 'mulCheckNum') : null,
        safeExamEachTimeCount: json['safeExamEachTimeCount'] != null ? safeToInt(json['safeExamEachTimeCount'], 'safeExamEachTimeCount') : null,
      );
    } catch (e) {
      throw FormatException('InternshipPlanModel.fromJson 解析失败: $e\n原始数据: $json');
    }
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'academicYear': academicYear,
      'grade': grade,
      'planType': planType,
      'practiceMode': practiceMode,
      'planName': planName,
      'planCode': planCode,
      'facultyId': facultyId,
      'facultyName': facultyName,
      'majorId': majorId,
      'majorName': majorName,
      'majorDirection': majorDirection,
      'semester': semester,
      'schoolId': schoolId,
      'status': status,
      'dr': dr,
      'createPerson': createPerson,
      'createTime': createTime,
      'startTime': startTime,
      'endTime': endTime,
      'level': level,
      'subsidy': subsidy,
      'majorAdmin': majorAdmin,
      'majorAdminName': majorAdminName,
      'file': file,
      'aiSummary': aiSummary,
      'planStatus': planStatus,
      'requiredFiles': requiredFiles,
      'requiredFileList': requiredFileList,
      'signNum': signNum,
      'dailyReportNum': dailyReportNum,
      'weeklyReportNum': weeklyReportNum,
      'monthlyReportNum': monthlyReportNum,
      'summaryNum': summaryNum,
      'scoreEnterpriseTeacher': scoreEnterpriseTeacher,
      'scoreSchoolTeacher': scoreSchoolTeacher,
      'scoreProcess': scoreProcess,
      'safeExamQuestionCount': safeExamQuestionCount,
      'checkNum': checkNum,
      'mulCheckNum': mulCheckNum,
      'safeExamEachTimeCount': safeExamEachTimeCount,
    };
  }

  // 转换为领域实体
  InternshipPlan toEntity() {
    return InternshipPlan(
      id: id,
      planName: planName,
      planCode: planCode,
      academicYear: academicYear,
      semester: semester,
      grade: grade,
      level: level,
      majorName: majorName ?? '',
      facultyName: facultyName ?? '',
      planStatus: planStatus,
      planType: planType,
      practiceMode: practiceMode,
      startTime: startTime,
      endTime: endTime,
      createPerson: createPerson,
      createTime: createTime,
      majorAdminName: majorAdminName ?? '',
      subsidy: subsidy,
      dailyReportNum: dailyReportNum,
      weeklyReportNum: weeklyReportNum,
      monthlyReportNum: monthlyReportNum,
      summaryNum: summaryNum,
      signNum: signNum,
      checkNum: checkNum ?? 0,
      mulCheckNum: mulCheckNum ?? 0,
    );
  }

  @override
  List<Object?> get props => [
    id,
    academicYear,
    grade,
    planType,
    practiceMode,
    planName,
    planCode,
    facultyId,
    facultyName,
    majorId,
    majorName,
    majorDirection,
    semester,
    schoolId,
    status,
    dr,
    createPerson,
    createTime,
    startTime,
    endTime,
    level,
    subsidy,
    majorAdmin,
    majorAdminName,
    file,
    aiSummary,
    planStatus,
    requiredFiles,
    requiredFileList,
    signNum,
    dailyReportNum,
    weeklyReportNum,
    monthlyReportNum,
    summaryNum,
    scoreEnterpriseTeacher,
    scoreSchoolTeacher,
    scoreProcess,
    safeExamQuestionCount,
    checkNum,
    mulCheckNum,
    safeExamEachTimeCount,
  ];

  // 获取示例数据
  static List<InternshipPlanModel> getSampleData() {
    return [
      InternshipPlanModel(
        id: '1',
        planName: '2021级市场销售2023-2024实习学年第二学期岗位实习',
        planCode: 'PLAN001',
        academicYear: '2024-2025',
        semester: '2024-2025实习学年第一学期',
        grade: '2021',
        level: '本科',
        majorName: '市场销售',
        facultyName: '商学院',
        planStatus: 1,
        planType: 1,
        practiceMode: 1,
        startTime: 1714060800000, // 2024-04-25
        endTime: 1724515200000, // 2024-08-25
        createPerson: '张三',
        createTime: 1713888720000, // 2024-04-23 22:12
        majorAdminName: '李教授',
        subsidy: '500元/月',
        dailyReportNum: 30,
        weeklyReportNum: 4,
        monthlyReportNum: 1,
        summaryNum: 1,
        signNum: 60,
        checkNum: 10,
        mulCheckNum: 5,
        aiSummary: '',
        facultyId: '1',
        majorAdmin: '1001',
        majorDirection: '销售管理',
        majorId: '10',
        status: 1,
        dr: 0,
        scoreEnterpriseTeacher: 0.0,
        scoreSchoolTeacher: 0.0,
        scoreProcess: 0.0,
      ),
    ];
  }
}
