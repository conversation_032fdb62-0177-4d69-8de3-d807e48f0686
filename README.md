# 亿硕教育实习管理平台

一个基于Flutter开发的实习管理移动应用，帮助学生和教师管理实习过程。

## 功能特点

- 实习生信息查看
- 安全教育与预警
- 实习审批管理
- 实习报告提交与查看（日报、周报、月报、总结）
- 实习信息变更和转就业申请

## 如何运行

### 环境要求

- Flutter SDK (3.0.0+)
- Dart SDK (3.0.0+)
- Android Studio / VS Code
- Android SDK (部署到Android设备)
- Xcode (部署到iOS设备，仅Mac)

### 安装步骤

1. 克隆代码仓库
   ```
   git clone <仓库地址>
   cd flutter_demo
   ```

2. 安装依赖
   ```
   flutter pub get
   ```

3. 运行应用
   ```
   flutter run
   ```

## 部署到移动设备

### Android设备

1. 启用开发者选项和USB调试
   - 在设备上，进入`设置` > `关于手机`，点击`版本号`七次启用开发者选项
   - 返回设置，进入`开发者选项`，启用`USB调试`

2. 连接设备
   - 用USB数据线连接手机和电脑
   - 在手机上允许USB调试

3. 检查设备连接
   ```
   flutter devices
   ```

4. 运行应用
   ```
   flutter run
   ```
   或者生成APK
   ```
   flutter build apk
   ```
   APK文件位于`build/app/outputs/flutter-apk/app-release.apk`

### iOS设备 (需要Mac电脑)

1. 打开项目的iOS部分
   ```
   cd ios
   pod install
   cd ..
   ```

2. 打开Xcode项目
   ```
   open ios/Runner.xcworkspace
   ```

3. 在Xcode中配置开发团队
   - 选择`Runner`项目
   - 在`Signing & Capabilities`中选择你的开发团队

4. 连接你的iOS设备并运行
   - 在Xcode中选择你的设备
   - 点击运行按钮

## 许可证

[MIT](LICENSE)
