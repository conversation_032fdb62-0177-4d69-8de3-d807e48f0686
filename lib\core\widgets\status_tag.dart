/// -----
/// status_tag.dart
/// 
/// 通用状态标签组件，用于显示各种状态信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

/// 状态标签类型枚举
enum StatusTagType {
  /// 待实习状态
  pending,
  /// 实习中状态
  ongoing,
  /// 未认证状态
  unverified,
  /// 已认证状态
  verified,
  /// 自定义状态
  custom,
}

/// 通用状态标签组件
/// 
/// 用于显示各种状态信息，支持预定义的状态类型和自定义状态
/// 可以配置背景色、文字颜色、内边距、圆角等样式
class StatusTag extends StatelessWidget {
  /// 状态文本
  final String text;
  
  /// 状态类型
  final StatusTagType type;
  
  /// 自定义背景色（当type为custom时使用）
  final Color? backgroundColor;
  
  /// 自定义文字颜色（当type为custom时使用）
  final Color? textColor;
  
  /// 内边距
  final EdgeInsetsGeometry? padding;
  
  /// 圆角半径
  final double? borderRadius;
  
  /// 字体大小
  final double? fontSize;
  
  /// 字体粗细
  final FontWeight? fontWeight;

  const StatusTag({
    Key? key,
    required this.text,
    required this.type,
    this.backgroundColor,
    this.textColor,
    this.padding,
    this.borderRadius,
    this.fontSize,
    this.fontWeight,
  }) : super(key: key);

  /// 创建待实习状态标签
  const StatusTag.pending({
    Key? key,
    required String text,
    EdgeInsetsGeometry? padding,
    double? borderRadius,
    double? fontSize,
    FontWeight? fontWeight,
  }) : this(
    key: key,
    text: text,
    type: StatusTagType.pending,
    padding: padding,
    borderRadius: borderRadius,
    fontSize: fontSize,
    fontWeight: fontWeight,
  );

  /// 创建实习中状态标签
  const StatusTag.ongoing({
    Key? key,
    required String text,
    EdgeInsetsGeometry? padding,
    double? borderRadius,
    double? fontSize,
    FontWeight? fontWeight,
  }) : this(
    key: key,
    text: text,
    type: StatusTagType.ongoing,
    padding: padding,
    borderRadius: borderRadius,
    fontSize: fontSize,
    fontWeight: fontWeight,
  );

  /// 创建未认证状态标签
  const StatusTag.unverified({
    Key? key,
    required String text,
    EdgeInsetsGeometry? padding,
    double? borderRadius,
    double? fontSize,
    FontWeight? fontWeight,
  }) : this(
    key: key,
    text: text,
    type: StatusTagType.unverified,
    padding: padding,
    borderRadius: borderRadius,
    fontSize: fontSize,
    fontWeight: fontWeight,
  );

  /// 创建已认证状态标签
  const StatusTag.verified({
    Key? key,
    required String text,
    EdgeInsetsGeometry? padding,
    double? borderRadius,
    double? fontSize,
    FontWeight? fontWeight,
  }) : this(
    key: key,
    text: text,
    type: StatusTagType.verified,
    padding: padding,
    borderRadius: borderRadius,
    fontSize: fontSize,
    fontWeight: fontWeight,
  );

  /// 创建自定义状态标签
  const StatusTag.custom({
    Key? key,
    required String text,
    required Color backgroundColor,
    required Color textColor,
    EdgeInsetsGeometry? padding,
    double? borderRadius,
    double? fontSize,
    FontWeight? fontWeight,
  }) : this(
    key: key,
    text: text,
    type: StatusTagType.custom,
    backgroundColor: backgroundColor,
    textColor: textColor,
    padding: padding,
    borderRadius: borderRadius,
    fontSize: fontSize,
    fontWeight: fontWeight,
  );

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(borderRadius ?? 6.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: fontSize ?? 20.sp,
          color: _getTextColor(),
          fontWeight: fontWeight ?? FontWeight.w500,
        ),
      ),
    );
  }

  /// 获取背景色
  Color _getBackgroundColor() {
    if (type == StatusTagType.custom && backgroundColor != null) {
      return backgroundColor!;
    }

    switch (type) {
      case StatusTagType.pending:
      case StatusTagType.unverified:
        return const Color(0xFFEBF1FF);
      case StatusTagType.ongoing:
      case StatusTagType.verified:
        return const Color(0xFFDDFFF1);
      case StatusTagType.custom:
        return backgroundColor ?? const Color(0xFFEBF1FF);
    }
  }

  /// 获取文字颜色
  Color _getTextColor() {
    if (type == StatusTagType.custom && textColor != null) {
      return textColor!;
    }

    switch (type) {
      case StatusTagType.pending:
      case StatusTagType.unverified:
        return AppTheme.primaryColor;
      case StatusTagType.ongoing:
      case StatusTagType.verified:
        return const Color(0xFF04AE68);
      case StatusTagType.custom:
        return textColor ?? AppTheme.primaryColor;
    }
  }
}
