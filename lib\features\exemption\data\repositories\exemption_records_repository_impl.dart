/// -----
/// exemption_records_repository_impl.dart
///
/// 免实习记录仓库实现
/// 实现免实习记录相关的业务操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions/auth_exception.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/error/failures/failure.dart';
import 'package:flutter_demo/core/error/failures/auth_failure.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import '../../domain/repositories/exemption_records_repository.dart';
import '../datasources/exemption_records_data_source.dart';
import '../models/exemption_record_model.dart';

/// 免实习记录仓库实现
///
/// 实现免实习记录相关的业务操作，处理网络状态和异常
class ExemptionRecordsRepositoryImpl implements ExemptionRecordsRepository {
  static const String _tag = 'ExemptionRecordsRepository';
  
  final ExemptionRecordsDataSource _dataSource;
  final NetworkInfo _networkInfo;

  const ExemptionRecordsRepositoryImpl({
    required ExemptionRecordsDataSource dataSource,
    required NetworkInfo networkInfo,
  }) : _dataSource = dataSource, _networkInfo = networkInfo;

  @override
  Future<Either<Failure, ExemptionRecordModel?>> getExemptionRecord({
    required String planId,
  }) async {
    Logger.info(_tag, '开始获取免实习记录，planId: $planId');

    if (await _networkInfo.isConnected) {
      try {
        final record = await _dataSource.getExemptionRecord(planId: planId);
        Logger.info(_tag, '成功获取免实习记录');
        return Right(record);
      } on UnauthorizedException catch (e) {
        Logger.error(_tag, '认证异常: ${e.message}');
        return Left(UnauthorizedFailure(e.message));
      } on ForbiddenException catch (e) {
        Logger.error(_tag, '权限异常: ${e.message}');
        return Left(ForbiddenFailure(e.message));
      } on ServerException catch (e) {
        Logger.error(_tag, '服务器异常: ${e.message}');
        return Left(ServerFailure(e.message));
      } catch (e) {
        Logger.error(_tag, '未知异常: $e');
        return Left(ServerFailure('获取免实习记录失败: $e'));
      }
    } else {
      Logger.warning(_tag, '网络连接不可用');
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }
}
