/// -----
/// info_item.dart
///
/// 信息项组件，用于显示标签和值的信息项
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

class InfoItem extends StatelessWidget {
  final String label;
  final String value;
  final bool isHighlighted;
  final bool showDivider;
  final VoidCallback? onTap;
  final Widget? trailing;

  const InfoItem({
    Key? key,
    required this.label,
    required this.value,
    this.isHighlighted = false,
    this.showDivider = false,
    this.onTap,
    this.trailing,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 100,
                  child: Text(
                    label,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.black999,
                    ),
                  ),
                ),
                Expanded(
                  child: Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      color: isHighlighted ? const Color(0xFFFFB35E) : AppTheme.black333,
                    ),
                    textAlign: TextAlign.right,
                  ),
                ),
                if (trailing != null) trailing!,
              ],
            ),
          ),
          if (showDivider)
            Divider(
              height: 1,
              thickness: 0.5,
              color: Colors.grey[200],
              indent: 16,
              endIndent: 16,
            ),
        ],
      ),
    );
  }
}
