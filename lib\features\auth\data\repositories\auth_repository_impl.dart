/// -----
/// auth_repository_impl.dart
///
/// 认证仓库实现类，实现认证相关的数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions/cache_exception.dart';
import '../../../../core/error/exceptions/network_exception.dart';
import '../../../../core/error/exceptions/server_exception.dart';
import '../../../../core/error/failures/failure.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/local/auth_local_data_source.dart';
import '../datasources/remote/auth_remote_data_source.dart';
import 'package:flutter_demo/features/auth/domain/entities/reset_password_entity.dart';

/// 认证仓库实现类
///
/// 实现认证相关的数据操作
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  AuthRepositoryImpl(this.remoteDataSource, this._localDataSource);

  @override
  Future<Either<Failure, User>> login({
    required String phone,
    required String password,
  }) async {
    try {
      final userModel = await remoteDataSource.login(
        phone: phone,
        password: password,
      );

      // 缓存用户信息
      await _localDataSource.cacheUser(userModel);

      // 缓存令牌
      if (userModel.token != null) {
        await _localDataSource.cacheToken(userModel.token!);
      }
      // 缓存用户类型
      if  (userModel.userType != null) {
        await _localDataSource.cacheUserType(userModel.userType!);
      }

      return Right(userModel);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('登录失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> register({
    required String phone,
    required String code,
    required String password,
  }) async {
    try {
      final result = await remoteDataSource.register(
        phone: phone,
        code: code,
        password: password,
      );

      return Right(result);
    } on ServerException catch (e) {
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      return Left(NetworkFailure(e.message));
    } catch (e) {
      return Left(ServerFailure('注册失败: $e'));
    }
  }

  @override
  Future<Either<Failure, User?>> getCurrentUser() async {
    try {
      final userModel = _localDataSource.getCachedUser();
      return Right(userModel);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('获取当前用户失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> logout() async {
    try {
      // 尝试调用远程退出登录接口
      try {
        await remoteDataSource.logout();
      } catch (e) {
        // 忽略远程退出登录失败，继续清除本地缓存
      }

      // 清除本地缓存
      final result = await _localDataSource.clearCache();
      return Right(result);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('退出登录失败: $e'));
    }
  }

  @override
  Future<Either<Failure, bool>> isLoggedIn() async {
    try {
      final token = _localDataSource.getCachedToken();
      return Right(token != null && token.isNotEmpty);
    } on CacheException catch (e) {
      return Left(CacheFailure(e.message));
    } catch (e) {
      return Left(CacheFailure('检查登录状态失败: $e'));
    }
  }

  @override
  Future<String> sendVerificationCode(String phone, {required String deviceId}) async {
    return remoteDataSource.sendVerificationCode(
      phone: phone,
      deviceId: deviceId,
    );
  }

  @override
  Future<String> resetPassword(ResetPasswordEntity entity) async {
    return remoteDataSource.resetPassword(
      phone: entity.phone,
      code: entity.code,
      newPassword: entity.newPassword,
    );
  }
  
  @override
  Future<int> authenticate({
    required String deptName,
    required String userCode,
    required String userName,
    required String userType,
  }) async {
    try {
      final result = await remoteDataSource.authenticate(
        deptName: deptName,
        userCode: userCode,
        userName: userName,
        userType: userType,
      );
      
      // 认证成功后缓存用户类型
      if (result == 0 || result == '0') {
        await _localDataSource.cacheUserType(userType);
      }
      
      return result;
    } on ServerException catch (e) {
      throw ServerException(e.message);
    } catch (e) {
      throw ServerException('认证失败: $e');
    }
  }
}
