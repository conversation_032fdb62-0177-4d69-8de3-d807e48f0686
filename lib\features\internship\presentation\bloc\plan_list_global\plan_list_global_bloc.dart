/// -----------------------------------------------------------------------------
/// plan_list_global_bloc.dart
///
/// 全局实习计划列表BLoC
/// 用于管理全局的实习计划列表状态和当前选中的计划
///
/// <AUTHOR>
/// @date 2025-06-16
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/usecases/usecase.dart';
import '../../../../../core/utils/logger.dart';
import '../../../domain/usecases/get_internship_plan_list_usecase.dart';
import 'plan_list_global_event.dart';
import 'plan_list_global_state.dart';

/// 全局实习计划列表BLoC
/// 
/// 管理全局的实习计划列表状态，包括加载、刷新、选择当前计划等功能
class PlanListGlobalBloc extends Bloc<PlanListGlobalEvent, PlanListGlobalState> {
  final GetInternshipPlanListUseCase _getInternshipPlanListUseCase;
  
  static const String _tag = 'PlanListGlobalBloc';

  PlanListGlobalBloc({
    required GetInternshipPlanListUseCase getInternshipPlanListUseCase,
  })  : _getInternshipPlanListUseCase = getInternshipPlanListUseCase,
        super(const PlanListGlobalInitialState()) {
    on<LoadPlanListGlobalEvent>(_onLoadPlanList);
    on<RefreshPlanListGlobalEvent>(_onRefreshPlanList);
    on<SelectCurrentPlanEvent>(_onSelectCurrentPlan);
  }

  /// 处理加载实习计划列表事件
  Future<void> _onLoadPlanList(
    LoadPlanListGlobalEvent event,
    Emitter<PlanListGlobalState> emit,
  ) async {
    Logger.info(_tag, '开始加载实习计划列表');
    emit(const PlanListGlobalLoadingState());

    final result = await _getInternshipPlanListUseCase(const NoParams());

    result.fold(
      (failure) {
        Logger.error(_tag, '加载实习计划列表失败: ${failure.message}');
        emit(PlanListGlobalErrorState(failure.message));
      },
      (plans) {
        Logger.info(_tag, '成功加载${plans.length}个实习计划');
        
        // 如果有计划，默认选择第一个进行中的计划，如果没有进行中的则选择第一个
        String? defaultPlanId;
        if (plans.isNotEmpty) {
          final activePlans = plans.where((plan) => plan.planStatus == 1).toList();
          if (activePlans.isNotEmpty) {
            defaultPlanId = activePlans.first.planId;
          } else {
            defaultPlanId = plans.first.planId;
          }
        }
        
        emit(PlanListGlobalLoadedState(
          plans: plans,
          currentPlanId: defaultPlanId,
        ));
      },
    );
  }

  /// 处理刷新实习计划列表事件
  Future<void> _onRefreshPlanList(
    RefreshPlanListGlobalEvent event,
    Emitter<PlanListGlobalState> emit,
  ) async {
    Logger.info(_tag, '开始刷新实习计划列表');

    // 保存当前状态中的数据（如果有的话）
    final currentState = state;
    final previousPlans = currentState is PlanListGlobalLoadedState ? currentState.plans : null;
    final previousCurrentPlanId = currentState is PlanListGlobalLoadedState ? currentState.currentPlanId : null;

    final result = await _getInternshipPlanListUseCase(const NoParams());

    result.fold(
      (failure) {
        Logger.error(_tag, '刷新实习计划列表失败: ${failure.message}');
        emit(PlanListGlobalRefreshErrorState(
          message: failure.message,
          previousPlans: previousPlans,
          previousCurrentPlanId: previousCurrentPlanId,
        ));
      },
      (plans) {
        Logger.info(_tag, '成功刷新${plans.length}个实习计划');
        
        // 保持之前选中的计划，如果之前选中的计划不存在了，则重新选择
        String? newCurrentPlanId = previousCurrentPlanId;
        if (newCurrentPlanId != null) {
          final planExists = plans.any((plan) => plan.planId == newCurrentPlanId);
          if (!planExists) {
            newCurrentPlanId = null;
          }
        }
        
        // 如果没有选中的计划，选择默认计划
        if (newCurrentPlanId == null && plans.isNotEmpty) {
          final activePlans = plans.where((plan) => plan.planStatus == 1).toList();
          if (activePlans.isNotEmpty) {
            newCurrentPlanId = activePlans.first.planId;
          } else {
            newCurrentPlanId = plans.first.planId;
          }
        }
        
        emit(PlanListGlobalRefreshSuccessState(
          plans: plans,
          currentPlanId: newCurrentPlanId,
        ));
      },
    );
  }

  /// 处理选择当前实习计划事件
  Future<void> _onSelectCurrentPlan(
    SelectCurrentPlanEvent event,
    Emitter<PlanListGlobalState> emit,
  ) async {
    Logger.info(_tag, '选择实习计划: ${event.planId}');

    final currentState = state;
    if (currentState is PlanListGlobalLoadedState) {
      // 检查选择的计划是否存在
      final planExists = currentState.plans.any((plan) => plan.planId == event.planId);
      if (planExists) {
        emit(currentState.copyWith(currentPlanId: event.planId));
        Logger.info(_tag, '成功选择实习计划: ${event.planId}');
      } else {
        Logger.warning(_tag, '选择的实习计划不存在: ${event.planId}');
      }
    } else {
      Logger.warning(_tag, '当前状态不支持选择计划: ${currentState.runtimeType}');
    }
  }
}
