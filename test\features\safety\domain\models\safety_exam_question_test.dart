/// -----
/// safety_exam_question_test.dart
/// 
/// 安全教育考试题目模型测试
///
/// <AUTHOR>
/// @date 2025-06-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';

void main() {
  group('SafetyExamQuestion', () {
    test('应该正确解析单选题的API JSON数据', () {
      final json = {
        'id': '1',
        'planId': '8',
        'title': '这是一道单选题',
        'optionsJson': r'["{\"A\":\"选项A\",\"B\":\"选项B\",\"C\":\"选项C\"}"]',
        'correctAnswer': 'A',
      };

      final question = SafetyExamQuestion.fromJson(json, 1);

      expect(question.id, '1');
      expect(question.planId, '8');
      expect(question.title, '这是一道单选题');
      expect(question.number, 1);
      expect(question.options.length, 3);
      expect(question.options[0].id, 'A');
      expect(question.options[0].content, '选项A');
      expect(question.correctAnswers, ['A']);
      expect(question.isMultipleChoice, false);
      expect(question.type, QuestionType.singleChoice);
    });

    test('应该正确解析多选题的API JSON数据', () {
      final json = {
        'id': '2',
        'planId': '8',
        'title': '这是一道多选题',
        'optionsJson': r'["{\"A\":\"选项A\",\"B\":\"选项B\",\"C\":\"选项C\",\"D\":\"选项D\"}"]',
        'correctAnswer': 'AC',
      };

      final question = SafetyExamQuestion.fromJson(json, 2);

      expect(question.id, '2');
      expect(question.planId, '8');
      expect(question.title, '这是一道多选题');
      expect(question.number, 2);
      expect(question.options.length, 4);
      expect(question.correctAnswers, ['A', 'C']);
      expect(question.isMultipleChoice, true);
      expect(question.type, QuestionType.multipleChoice);
    });

    test('应该正确转换为JSON格式', () {
      final question = SafetyExamQuestion(
        id: '1',
        planId: '8',
        title: '测试题目',
        options: [
          ExamOption(id: 'A', content: '选项A'),
          ExamOption(id: 'B', content: '选项B'),
        ],
        correctAnswers: ['A'],
        number: 1,
      );

      final json = question.toJson();

      expect(json['id'], '1');
      expect(json['planId'], '8');
      expect(json['title'], '测试题目');
      expect(json['correctAnswers'], ['A']);
      expect(json['number'], 1);
      expect(json['options'], isA<List>());
    });

    test('应该正确判断题目类型', () {
      final singleChoiceQuestion = SafetyExamQuestion(
        id: '1',
        planId: '8',
        title: '单选题',
        options: [],
        correctAnswers: ['A'],
        number: 1,
      );

      final multipleChoiceQuestion = SafetyExamQuestion(
        id: '2',
        planId: '8',
        title: '多选题',
        options: [],
        correctAnswers: ['A', 'B'],
        number: 2,
      );

      expect(singleChoiceQuestion.isMultipleChoice, false);
      expect(singleChoiceQuestion.type, QuestionType.singleChoice);
      
      expect(multipleChoiceQuestion.isMultipleChoice, true);
      expect(multipleChoiceQuestion.type, QuestionType.multipleChoice);
    });
  });

  group('ExamOption', () {
    test('应该正确创建选项对象', () {
      final option = ExamOption(id: 'A', content: '选项内容');

      expect(option.id, 'A');
      expect(option.content, '选项内容');
    });

    test('应该正确解析JSON数据', () {
      final json = {
        'id': 'B',
        'content': '选项B的内容',
      };

      final option = ExamOption.fromJson(json);

      expect(option.id, 'B');
      expect(option.content, '选项B的内容');
    });

    test('应该正确转换为JSON格式', () {
      final option = ExamOption(id: 'C', content: '选项C');
      final json = option.toJson();

      expect(json['id'], 'C');
      expect(json['content'], '选项C');
    });

    test('应该正确比较选项对象', () {
      final option1 = ExamOption(id: 'A', content: '选项A');
      final option2 = ExamOption(id: 'A', content: '选项A');
      final option3 = ExamOption(id: 'B', content: '选项B');

      expect(option1, equals(option2));
      expect(option1, isNot(equals(option3)));
    });
  });
}
