/// -----
/// student_report_list_screen.dart
///
/// 学生报告列表视图组件，用于显示日报/周报列表，支持展开/收起功能
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';
import 'package:flutter_demo/features/report/data/models/weekly_report.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:intl/intl.dart';

class StudentReportListView extends StatefulWidget {
  final ReportType type;

  const StudentReportListView({
    Key? key,
    required this.type,
  }) : super(key: key);

  @override
  State<StudentReportListView> createState() => _StudentReportListViewState();
}

class _StudentReportListViewState extends State<StudentReportListView> {
  final String courseName = '2021级市场营销2023-2024学年学期第三学期 财经实习';
  final List<String> availableCourses = [
    '2021级市场营销2023-2024学年学期第三学期 财经实习',
    '2022级软件工程2023-2024实习学年第一学期岗位实习',
    '2020级电子商务2022-2023实习学年第二学期岗位实习',
  ];

  // 用于跟踪每个报告项的展开状态
  final Map<String, bool> _expandedStates = {};

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: CustomAppBar(
        title: '我的报告',
        backgroundColor: Colors.transparent,
        elevation: 0,
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 课程头部选择器
          CourseHeaderSection(
            courseName: courseName,
            initialExpanded: false,
            availableCourses: availableCourses,
            onCourseChanged: (newCourse) {
              // 在实际应用中，这里应该根据新选择的课程重新加载数据
              setState(() {});
            },
          ),

          // 报告列表
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
              itemCount: 3, // 临时数据
              itemBuilder: (context, index) {
                final report = _createMockReport(index);
                return _buildReportItem(context, report, index);
              },
            ),
          ),
        ],
      ),
    );
  }

  // 构建报告项
  Widget _buildReportItem(BuildContext context, BaseReport report, int index) {
    final isExpanded = _expandedStates[report.id] ?? false;

    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学生信息头部
          _buildStudentHeader(report),

          // 周报信息
          _buildReportInfo(report),

          // 问题列表
          _buildQuestionsList(report, isExpanded),

          // 底部信息
          _buildBottomInfo(report),
        ],
      ),
    );
  }

  // 构建学生信息头部
  Widget _buildStudentHeader(BaseReport report) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 学生头像
          CircleAvatar(
            radius: 44.r,
            backgroundColor: Colors.grey[200],
            backgroundImage: const NetworkImage(AppConstants.avatar1),
          ),
          SizedBox(width: 12.w),

          // 学生信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      report.userName,
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(width: 8.w),
                    // 实习生标签
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4.r),
                        border: Border.all(color: AppTheme.blue2165f6, width: 0.5),
                      ),
                      child: Text(
                        '实习生',
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: AppTheme.blue2165f6,
                        ),
                      ),
                    ),
                    const Spacer(),
                    // 详情按钮
                    Text(
                      '详情',
                      style: TextStyle(
                        fontSize: 24.sp,
                        color: AppTheme.blue2165f6,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 16.h),
                // 公司名称
                Text(
                  '武汉谦通信息技术有限公司',
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建周报信息
  Widget _buildReportInfo(BaseReport report) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      child: Text(
        '第三周周报 2025.05.15-2025.05.20',
        style: TextStyle(
          fontSize: 28.sp,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  // 构建问题列表
  Widget _buildQuestionsList(BaseReport report, bool isExpanded) {
    final questions = _getQuestions(report);

    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: questions.asMap().entries.map((entry) {
          final index = entry.key;
          final question = entry.value;
          return _buildQuestionItem(question, index + 1, isExpanded, report.id);
        }).toList(),
      ),
    );
  }

  // 构建单个问题项
  Widget _buildQuestionItem(Map<String, String> question, int questionNumber, bool isExpanded, String reportId) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 问题
          Text(
            '问题$questionNumber：${question['question']}',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),

          // 答案容器
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '答：${question['answer']}',
                  style: TextStyle(
                    fontSize: 24.sp,
                    color: Colors.black87,
                    height: 1.5,
                  ),
                  maxLines: isExpanded ? null : 2,
                  overflow: isExpanded ? TextOverflow.visible : TextOverflow.ellipsis,
                ),
                if (question['answer']!.length > 50) // 只有长文本才显示展开/收起按钮
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        _expandedStates[reportId] = !isExpanded;
                      });
                    },
                    child: Padding(
                      padding: EdgeInsets.only(top: 8.h),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            isExpanded ? '收起' : '展开',
                            style: TextStyle(
                              fontSize: 22.sp,
                              color: AppTheme.blue2165f6,
                            ),
                          ),
                          Icon(
                            isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                            color: AppTheme.blue2165f6,
                            size: 20.sp,
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建底部信息
  Widget _buildBottomInfo(BaseReport report) {
    return Padding(
      padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 16.h),
      child: Row(
        children: [
          Text(
            '提交时间：${DateFormat('yyyy-MM-dd HH:mm').format(report.createdAt)}',
            style: TextStyle(
              fontSize: 22.sp,
              color: Colors.grey[600],
            ),
          ),
          const Spacer(),
          // 保存按钮
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: AppTheme.blue2165f6,
              borderRadius: BorderRadius.circular(4.r),
            ),
            child: Text(
              '保存',
              style: TextStyle(
                fontSize: 22.sp,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 获取问题列表
  List<Map<String, String>> _getQuestions(BaseReport report) {
    return [
      {
        'question': '已完成用户个人中心UI优化，已全量上线？',
        'answer': '保持1.5倍行高，可以展开显示完整内容。保持1.5倍行高，可以展开显示完整内容。保持1.5倍行高，可以展开显示完整内容。保持1.5倍行高，可以展开显示完整内容。',
      },
      {
        'question': '已完成用户个人中心UI优化，已全量上线？',
        'answer': '保持1.5倍行高，可以展开显示完整内容。保持1.5倍行高，可以展开显示完整内容。保持1.5倍行高，可以展开显示完整内容。',
      },
      {
        'question': '已完成用户个人中心UI优化，已全量上线？',
        'answer': '保持1.5倍行高，可以展开显示完整内容。保持1.5倍行高，可以展开显示完整内容。保持1.5倍行高，可以展开显示完整内容。',
      },
    ];
  }

  BaseReport _createMockReport(int index) {
    // 固定创建周报数据，因为设计图显示的是周报
    return WeeklyReport(
      id: 'report_$index',
      status: ReportStatus.values[index % 4],
      userId: 'user_1',
      userName: ['李成刚', '陈诚', '张洪涛'][index % 3],
      createdAt: DateTime.now().subtract(Duration(days: index * 7)),
      startDate: DateTime.now().subtract(Duration(days: index * 7 + 7)),
      endDate: DateTime.now().subtract(Duration(days: index * 7)),
      weekSummary: '已完成用户个人中心UI优化，已全量上线；\n修复订单状态同步延迟BUG，测试通过率100%...',
      achievements: '成功上线新功能',
      problems: '遇到了一些技术难题',
      nextWeekPlan: '计划完成剩余功能开发',
      teacherName: '王老师',
      teacherComment: '写的不错，但仍然有改进的空间，可以把周报写的更加详细点',
      commentTime: DateTime.now(),
      courseName: courseName,
      title: '第三周周报',
      infoTitle: '基础信息',
      contentTitle: '周报内容',
    );
  }
}
