/// -----
/// reset_password_usecase.dart
///
/// 重置密码用例，处理重置密码的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/reset_password_entity.dart';
import '../repositories/auth_repository.dart';

/// 重置密码用例
///
/// 处理重置密码的业务逻辑
class ResetPasswordUseCase implements UseCase<String, ResetPasswordParams> {
  final AuthRepository repository;

  ResetPasswordUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(ResetPasswordParams params) async {
    try {
      final response = await repository.resetPassword(
        ResetPasswordEntity(
          phone: params.phone,
          code: params.code,
          newPassword: params.newPassword,
        ),
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

/// 重置密码参数
///
/// 包含重置密码所需的参数
class ResetPasswordParams extends Equatable {
  final String phone;
  final String code;
  final String newPassword;
  final String deviceId;

  const ResetPasswordParams({
    required this.phone,
    required this.code,
    required this.newPassword,
    required this.deviceId,
  });

  @override
  List<Object?> get props => [phone, code, newPassword, deviceId];
}