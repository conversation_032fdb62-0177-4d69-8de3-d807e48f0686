/// -----
/// exemption_injection.dart
///
/// 免实习模块依赖注入配置
/// 注册免实习模块的BLoC和相关依赖
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import '../presentation/bloc/exemption_records_bloc.dart';
import '../data/datasources/exemption_records_data_source.dart';
import '../data/datasources/remote_exemption_records_data_source.dart';
import '../data/repositories/exemption_records_repository_impl.dart';
import '../domain/repositories/exemption_records_repository.dart';
import '../domain/usecases/get_exemption_record_usecase.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化免实习模块依赖
///
/// 注册免实习模块的数据源、仓库、用例和BLoC
Future<void> setupExemptionDependencies() async {
  // 数据源
  getIt.registerLazySingleton<ExemptionRecordsDataSource>(
    () => RemoteExemptionRecordsDataSource(
      dioClient: getIt<DioClient>(),
    ),
  );

  // 仓库
  getIt.registerLazySingleton<ExemptionRecordsRepository>(
    () => ExemptionRecordsRepositoryImpl(
      dataSource: getIt<ExemptionRecordsDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
    ),
  );

  // 用例
  getIt.registerLazySingleton<GetExemptionRecordUseCase>(
    () => GetExemptionRecordUseCase(
      repository: getIt<ExemptionRecordsRepository>(),
    ),
  );

  // BLoC - 使用工厂模式，每次使用时创建新实例
  getIt.registerFactory<ExemptionRecordsBloc>(
    () => ExemptionRecordsBloc(
      getExemptionRecordUseCase: getIt<GetExemptionRecordUseCase>(),
    ),
  );
}
