import 'package:get_it/get_it.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/storage/local_storage.dart';
import '../data/datasources/local/auth_local_data_source.dart';
import '../data/datasources/remote/auth_remote_data_source.dart';
import '../data/repositories/auth_repository_impl.dart';
import '../domain/repositories/auth_repository.dart';
import '../domain/usecases/get_current_user_usecase.dart';
import '../domain/usecases/login_usecase.dart';
import '../domain/usecases/logout_usecase.dart';
import '../domain/usecases/register_usecase.dart';
import '../domain/usecases/reset_password_usecase.dart';
import '../domain/usecases/send_verification_code_usecase.dart';
import '../domain/usecases/authenticate_usecase.dart';
import '../presentation/bloc/login/login_bloc.dart';
import '../presentation/bloc/register/register_bloc.dart';
import '../presentation/bloc/reset_password/reset_password_bloc.dart';
import '../presentation/bloc/role_info/role_info.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化认证模块依赖
///
/// 注册认证模块的数据源、仓库、用例和BLoC
Future<void> setupAuthDependencies() async {
  // 数据源
  getIt.registerLazySingleton<AuthRemoteDataSource>(
    () => AuthRemoteDataSourceImpl(getIt<DioClient>()),
  );

  getIt.registerLazySingleton<AuthLocalDataSource>(
    () => AuthLocalDataSourceImpl(getIt<LocalStorage>()),
  );

  // 仓库
  getIt.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(
      getIt<AuthRemoteDataSource>(),
      getIt<AuthLocalDataSource>(),
    ),
  );

  // 用例
  getIt.registerFactory<LoginUseCase>(
    () => LoginUseCase(getIt<AuthRepository>()),
  );

  getIt.registerFactory<LogoutUseCase>(
    () => LogoutUseCase(getIt<AuthRepository>()),
  );

  getIt.registerFactory<GetCurrentUserUseCase>(
    () => GetCurrentUserUseCase(getIt<AuthRepository>()),
  );

  getIt.registerFactory<RegisterUseCase>(
    () => RegisterUseCase(getIt<AuthRepository>()),
  );

  getIt.registerFactory<SendVerificationCodeUseCase>(
    () => SendVerificationCodeUseCase(getIt<AuthRepository>()),
  );

  // BLoC
  getIt.registerFactory<LoginBloc>(
    () => LoginBloc(
      loginUseCase: getIt<LoginUseCase>(),
      logoutUseCase: getIt<LogoutUseCase>(),
      getCurrentUserUseCase: getIt<GetCurrentUserUseCase>(),
    ),
  );

  getIt.registerFactory<RegisterBloc>(
    () => RegisterBloc(
      registerUseCase: getIt<RegisterUseCase>(),
      sendVerificationCodeUseCase: getIt<SendVerificationCodeUseCase>(),
    ),
  );

  // ResetPasswordBloc
  getIt.registerFactory<ResetPasswordBloc>(
    () => ResetPasswordBloc(
      sendVerificationCodeUseCase: getIt<SendVerificationCodeUseCase>(),
      resetPasswordUseCase: getIt<ResetPasswordUseCase>(),
    ),
  );

  // ResetPasswordUseCase
  getIt.registerFactory<ResetPasswordUseCase>(
    () => ResetPasswordUseCase(getIt<AuthRepository>()),
  );
  
  // AuthenticateUseCase
  getIt.registerFactory<AuthenticateUseCase>(
    () => AuthenticateUseCase(getIt<AuthRepository>()),
  );
  
  // RoleInfoBloc
  getIt.registerFactory<RoleInfoBloc>(
    () => RoleInfoBloc(getIt<AuthenticateUseCase>()),
  );
}
