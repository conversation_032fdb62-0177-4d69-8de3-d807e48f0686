/// -----
/// internship_application_remote_data_source_impl.dart
///
/// 实习申请远程数据源具体实现
/// 实现实习申请相关的远程数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dio/dio.dart';
import '../../../../../core/error/exceptions/server_exception.dart';
import '../../../../../core/error/exceptions/network_exception.dart';
import '../../../../../core/error/exceptions/auth_exception.dart';
import '../../../../../core/network/dio_client.dart';
import '../../../data/models/internship_application_request_model.dart';
import '../../../data/models/internship_application_response_model.dart';
import 'internship_application_remote_data_source.dart';

/// 实习申请远程数据源具体实现
///
/// 使用DioClient进行网络请求，实现实习申请相关的远程数据操作
class InternshipApplicationRemoteDataSourceImpl implements InternshipApplicationRemoteDataSource {
  /// DIO网络客户端
  final DioClient dioClient;

  /// 构造函数
  ///
  /// 参数:
  ///   - [dioClient]: DIO网络客户端实例
  const InternshipApplicationRemoteDataSourceImpl({
    required this.dioClient,
  });

  @override
  Future<InternshipApplicationResponseModel> submitApplication(
    InternshipApplicationRequestModel request,
  ) async {
    try {
      // 调用API接口
      final response = await dioClient.post(
        'internshipservice/v1/internship/apply/student/save',
        data: request.toJson(),
      );

      // 解析响应数据
      if (response.data != null) {
        return InternshipApplicationResponseModel.fromJson(response.data);
      } else {
        throw ServerException('响应数据为空');
      }
    } on DioException catch (e) {
      // 处理网络异常
      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.receiveTimeout ||
          e.type == DioExceptionType.sendTimeout) {
        throw NetworkException('网络连接超时');
      } else if (e.type == DioExceptionType.connectionError) {
        throw NetworkException('网络连接失败');
      } else if (e.response?.statusCode == 401) {
        throw UnauthorizedException('认证失败，请重新登录');
      } else if (e.response?.statusCode == 403) {
        throw ForbiddenException('权限不足');
      } else if (e.response?.statusCode == 404) {
        throw ServerException('接口不存在');
      } else if (e.response?.statusCode == 500) {
        throw ServerException('服务器内部错误');
      } else {
        // 尝试解析服务器返回的错误信息
        String errorMessage = '网络请求失败';
        if (e.response?.data != null && e.response!.data is Map) {
          final errorData = e.response!.data as Map<String, dynamic>;
          errorMessage = errorData['resultMsg'] ?? errorData['message'] ?? errorMessage;
        }
        throw ServerException(errorMessage);
      }
    } catch (e) {
      // 处理其他异常
      throw ServerException('提交申请失败: $e');
    }
  }
}
