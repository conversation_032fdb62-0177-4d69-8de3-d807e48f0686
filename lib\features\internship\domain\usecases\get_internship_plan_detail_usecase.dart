/// -----
/// get_internship_plan_detail_usecase.dart
/// 
/// 获取实习计划详情用例
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/utils/logger.dart';
import '../entities/internship_plan.dart';
import '../repositories/internship_plan_repository.dart';

/// 获取实习计划详情用例
///
/// 封装获取实习计划详情的业务逻辑
class GetInternshipPlanDetailUseCase implements UseCase<InternshipPlan, InternshipPlanDetailParams> {
  final InternshipPlanRepository _repository;

  GetInternshipPlanDetailUseCase(this._repository);

  @override
  Future<Either<Failure, InternshipPlan>> call(InternshipPlanDetailParams params) async {
    Logger.info('GetInternshipPlanDetailUseCase', '执行获取实习计划详情用例，ID: ${params.id}');

    final result = await _repository.getInternshipPlanDetail(params.id);

    return result.fold(
      (failure) {
        Logger.error('GetInternshipPlanDetailUseCase', '获取实习计划详情失败: ${failure.message}');
        return Left(failure);
      },
      (plan) {
        Logger.info('GetInternshipPlanDetailUseCase', '成功获取实习计划详情: ${plan.planName}');
        return Right(plan);
      },
    );
  }
}

/// 获取实习计划详情用例参数
class InternshipPlanDetailParams extends Equatable {
  /// 实习计划ID
  final String id;

  const InternshipPlanDetailParams({required this.id});

  @override
  List<Object?> get props => [id];
}
