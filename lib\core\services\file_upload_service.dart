/// -----
/// file_upload_service.dart
///
/// 文件上传服务，处理文件上传到服务器的逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter_demo/core/network/dio_client.dart';

class FileUploadService {
  final DioClient _dioClient;

  FileUploadService(this._dioClient);

  /// 上传文件到服务器
  /// 
  /// [filePath] 文件本地路径
  /// [fileName] 文件名
  /// [onProgress] 上传进度回调
  /// 
  /// 返回服务器返回的文件URL
  Future<String> uploadFile({
    required String filePath,
    required String fileName,
    Function(double progress)? onProgress,
  }) async {
    try {
      final file = File(filePath);
      
      // 检查文件是否存在
      if (!await file.exists()) {
        throw Exception('文件不存在: $filePath');
      }

      // 创建FormData
      final formData = FormData.fromMap({
        'file': await MultipartFile.fromFile(
          filePath,
          filename: fileName,
        ),
        'type': 'report_attachment', // 附件类型
      });

      // 发送上传请求
      final response = await _dioClient.post(
        '/upload/file',
        data: formData,
        onSendProgress: (sent, total) {
          if (onProgress != null && total > 0) {
            final progress = sent / total;
            onProgress(progress);
          }
        },
      );

      // 解析响应
      if (response['resultCode'] == 200) {
        final data = response['data'];
        return data['fileUrl'] ?? data['url'] ?? '';
      } else {
        throw Exception(response['resultMsg'] ?? '上传失败');
      }
    } on DioException catch (e) {
      if (e.type == DioExceptionType.cancel) {
        throw Exception('上传已取消');
      } else if (e.type == DioExceptionType.connectionTimeout ||
                 e.type == DioExceptionType.sendTimeout ||
                 e.type == DioExceptionType.receiveTimeout) {
        throw Exception('上传超时，请检查网络连接');
      } else {
        throw Exception('上传失败: ${e.message}');
      }
    } catch (e) {
      throw Exception('上传失败: $e');
    }
  }

  /// 删除服务器上的文件
  /// 
  /// [fileUrl] 文件URL
  Future<bool> deleteFile(String fileUrl) async {
    try {
      final response = await _dioClient.post(
        '/upload/delete',
        data: {
          'fileUrl': fileUrl,
        },
      );

      return response['resultCode'] == 200;
    } catch (e) {
      return false;
    }
  }

  /// 获取文件信息
  /// 
  /// [fileUrl] 文件URL
  Future<Map<String, dynamic>?> getFileInfo(String fileUrl) async {
    try {
      final response = await _dioClient.post(
        '/upload/info',
        data: {
          'fileUrl': fileUrl,
        },
      );

      if (response['resultCode'] == 200) {
        return response['data'];
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// 格式化文件大小
  static String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// 检查文件类型是否支持
  static bool isSupportedFileType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    const supportedExtensions = [
      // 图片格式
      'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp',
      // 文档格式
      'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx',
      'txt', 'rtf',
      // 压缩格式
      'zip', 'rar', '7z',
    ];
    
    return supportedExtensions.contains(extension);
  }

  /// 获取文件类型图标
  static String getFileTypeIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    switch (extension) {
      case 'pdf':
        return 'assets/icons/file_pdf.png';
      case 'doc':
      case 'docx':
        return 'assets/icons/file_word.png';
      case 'xls':
      case 'xlsx':
        return 'assets/icons/file_excel.png';
      case 'ppt':
      case 'pptx':
        return 'assets/icons/file_powerpoint.png';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return 'assets/icons/file_image.png';
      case 'zip':
      case 'rar':
      case '7z':
        return 'assets/icons/file_archive.png';
      case 'txt':
      case 'rtf':
        return 'assets/icons/file_text.png';
      default:
        return 'assets/icons/file_default.png';
    }
  }
}
