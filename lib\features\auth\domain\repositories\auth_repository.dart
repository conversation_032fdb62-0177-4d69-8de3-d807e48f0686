/// -----
/// auth_repository.dart
///
/// 认证仓库接口，定义认证相关的数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/user.dart';
import 'package:flutter_demo/features/auth/domain/entities/reset_password_entity.dart';

/// 认证仓库接口
///
/// 定义认证相关的操作
abstract class AuthRepository {
  /// 登录
  ///
  /// 返回 Either<Failure, User>，表示登录成功或失败
  Future<Either<Failure, User>> login({
    required String phone,
    required String password,
  });

  /// 注册
  ///
  /// 返回 Either<Failure, bool>，表示注册成功或失败
  Future<Either<Failure, bool>> register({
    required String phone,
    required String code,
    required String password,
  });

  /// 获取当前用户
  ///
  /// 返回 Either<Failure, User?>，表示获取当前用户成功或失败
  Future<Either<Failure, User?>> getCurrentUser();

  /// 退出登录
  ///
  /// 返回 Either<Failure, bool>，表示退出登录成功或失败
  Future<Either<Failure, bool>> logout();

  /// 检查是否已登录
  ///
  /// 返回 Either<Failure, bool>，表示检查是否已登录成功或失败
  Future<Either<Failure, bool>> isLoggedIn();

  /// 发送验证码
  ///
  /// [phone] 手机号
  /// [deviceId] 设备ID
  Future<String> sendVerificationCode(String phone, {required String deviceId});

  /// 重置密码
  Future<String> resetPassword(ResetPasswordEntity entity);
  
  /// 认证用户
  ///
  /// [deptName] 部门/学校名称
  /// [userCode] 用户编号/学号/工号
  /// [userName] 用户姓名
  /// [userType] 用户类型
  /// 返回 认证结果码
  Future<int> authenticate({
    required String deptName,
    required String userCode,
    required String userName,
    required String userType,
  });
}
