# 页面日志记录使用说明

## 概述

为了方便开发调试时定位当前页面，我们提供了 `PageLoggingMixin` 和 `AdvancedPageLoggingMixin` 两个混入类，可以自动打印页面进入和离开的日志。

## 基础版本 - PageLoggingMixin

### 功能特性
- 自动打印页面进入和离开的日志
- 显示页面类名和State类名
- 计算页面停留时长
- 只在Debug模式下启用，不影响Release版本性能

### 使用方法

1. **导入混入类**
```dart
import 'package:flutter_demo/core/mixins/page_logging_mixin.dart';
```

2. **在State类中使用混入**
```dart
class _MyPageState extends State<MyPage> with PageLoggingMixin {
  @override
  void initState() {
    super.initState();
    logPageEntry(); // 调用这个方法来打印进入页面的日志
    
    // 其他初始化代码...
  }
}
```

### 输出示例
```
📱 [PAGE_ENTRY] 进入页面: StudentExemptionApplicationScreen (State: _StudentExemptionApplicationScreenState)
📱 [PAGE_ENTRY] 时间: 14:30:25.123
📱 [PAGE_EXIT] 离开页面: StudentExemptionApplicationScreen (State: _StudentExemptionApplicationScreenState)
📱 [PAGE_EXIT] 时间: 14:32:10.456
📱 [PAGE_EXIT] 停留时长: 1分45秒333毫秒
```

## 高级版本 - AdvancedPageLoggingMixin

### 功能特性
- 包含基础版本的所有功能
- 支持传递页面参数信息
- 记录页面构建次数
- 支持自定义事件日志
- 更详细的日志格式

### 使用方法

1. **导入混入类**
```dart
import 'package:flutter_demo/core/mixins/page_logging_mixin.dart';
```

2. **在State类中使用混入**
```dart
class _MyPageState extends State<MyPage> with AdvancedPageLoggingMixin {
  @override
  void initState() {
    super.initState();
    
    // 记录页面进入，可以传递参数信息
    logPageEntry(params: {
      'userId': widget.userId,
      'mode': widget.mode,
    });
    
    // 其他初始化代码...
  }

  @override
  Widget build(BuildContext context) {
    logBuild(); // 记录构建日志
    
    return Scaffold(
      // 页面内容...
    );
  }

  void _onButtonPressed() {
    // 记录自定义事件
    logEvent('按钮点击', data: {'buttonType': 'submit'});
  }
}
```

### 输出示例
```
🚀 [PAGE_ENTRY] ==========================================
🚀 [PAGE_ENTRY] 页面: StudentExemptionApplicationScreen
🚀 [PAGE_ENTRY] State类: _StudentExemptionApplicationScreenState
🚀 [PAGE_ENTRY] 时间: 14:30:25.123
🚀 [PAGE_ENTRY] 参数: {userId: 12345, mode: edit}
🚀 [PAGE_ENTRY] ==========================================
🔄 [PAGE_BUILD] StudentExemptionApplicationScreen - 第1次构建
⚡ [PAGE_EVENT] StudentExemptionApplicationScreen - 按钮点击
⚡ [PAGE_EVENT] 数据: {buttonType: submit}
🏁 [PAGE_EXIT] ==========================================
🏁 [PAGE_EXIT] 页面: StudentExemptionApplicationScreen
🏁 [PAGE_EXIT] State类: _StudentExemptionApplicationScreenState
🏁 [PAGE_EXIT] 时间: 14:32:10.456
🏁 [PAGE_EXIT] 总构建次数: 3
🏁 [PAGE_EXIT] 停留时长: 1分45秒333毫秒
🏁 [PAGE_EXIT] ==========================================
```

## 快速应用到现有页面

### 方法1：批量添加（推荐）

可以使用IDE的查找替换功能快速添加到多个页面：

1. **查找**：`class _(.+)State extends State<(.+)> {`
2. **替换**：`class _$1State extends State<$2> with PageLoggingMixin {`

然后在每个页面的 `initState` 方法开头添加 `logPageEntry();`

### 方法2：手动添加

对于重要的页面，可以手动添加并使用高级版本：

```dart
// 1. 添加导入
import 'package:flutter_demo/core/mixins/page_logging_mixin.dart';

// 2. 修改State类声明
class _MyPageState extends State<MyPage> with AdvancedPageLoggingMixin {

// 3. 在initState中添加日志记录
@override
void initState() {
  super.initState();
  logPageEntry(params: {'key': 'value'}); // 可选参数
  // 其他代码...
}
```

## 配置选项

### 启用/禁用日志
日志记录默认只在Debug模式下启用。如果需要修改这个行为，可以编辑 `page_logging_mixin.dart` 文件中的 `_isLoggingEnabled` 常量。

### 自定义日志格式
可以修改混入类中的 `_formatTime` 和 `_formatDuration` 方法来自定义时间和持续时间的显示格式。

## 注意事项

1. **性能影响**：日志记录只在Debug模式下启用，不会影响Release版本的性能
2. **自动清理**：页面离开日志会在 `dispose` 方法中自动记录，无需手动调用
3. **线程安全**：所有日志记录都使用 `debugPrint`，是线程安全的
4. **内存占用**：混入类只保存必要的时间信息，内存占用极小

## 示例页面

参考 `lib/features/plan/presentation/screens/student_exemption_application_screen.dart` 查看完整的使用示例。
