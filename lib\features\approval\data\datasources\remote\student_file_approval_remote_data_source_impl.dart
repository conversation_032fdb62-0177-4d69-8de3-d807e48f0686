/// -----
/// student_file_approval_remote_data_source_impl.dart
///
/// 学生文件审批远程数据源实现
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../../../core/network/dio_client.dart';
import '../../../../../core/utils/logger.dart';
import '../../../../../core/error/exceptions/server_exception.dart';
import '../../models/student_file_approval_model.dart';
import 'student_file_approval_remote_data_source.dart';

/// 学生文件审批远程数据源实现
class StudentFileApprovalRemoteDataSourceImpl implements StudentFileApprovalRemoteDataSource {
  final DioClient _dioClient;

  StudentFileApprovalRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<StudentFileApprovalModel>> getStudentFileApprovalList(
    String planId,
    int type,
  ) async {
    try {
      Logger.info('StudentFileApprovalRemoteDataSource', '开始获取学生文件审批列表，planId: $planId, type: $type');
      
      final response = await _dioClient.get(
        'internshipservice/v1/internship/teacher/file/require/list',
        queryParameters: {
          'planId': planId,
          'type': type,
        },
      );

      Logger.debug('StudentFileApprovalRemoteDataSource', '获取学生文件审批列表响应: $response');

      if (response is List) {
        final studentFileApprovalList = response
            .map((json) => StudentFileApprovalModel.fromJson(json as Map<String, dynamic>))
            .toList();

        Logger.info('StudentFileApprovalRemoteDataSource', '成功获取${studentFileApprovalList.length}个学生文件审批项');
        return studentFileApprovalList;
      } else {
        Logger.warning('StudentFileApprovalRemoteDataSource', '响应数据格式不正确');
        return [];
      }
    } catch (e) {
      Logger.error('StudentFileApprovalRemoteDataSource', '获取学生文件审批列表失败: $e');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取学生文件审批列表失败: $e');
    }
  }
}
