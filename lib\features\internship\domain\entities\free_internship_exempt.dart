/// -----
/// free_internship_exempt.dart
///
/// 免实习申请实体类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 免实习申请实体类
/// 
/// 表示一个免实习申请的核心业务数据
class FreeInternshipExempt extends Equatable {
  /// 免实习记录ID
  final int id;
  
  /// 学生ID
  final int studentId;
  
  /// 学生姓名
  final String studentName;
  
  /// 学生头像URL
  final String avatar;
  
  /// 实习计划ID
  final int planId;
  
  /// 免实习理由
  final String reason;
  
  /// 证明材料文件URL
  final String fileUrl;
  
  /// 状态（0=待审批，1=已通过，2=驳回）
  final int status;
  
  /// 审核人ID(老师ID)
  final int? reviewId;
  
  /// 审核人
  final String? reviewPerson;
  
  /// 审核人角色
  final String? reviewRole;
  
  /// 审核意见
  final String? reviewOpinion;
  
  /// 创建时间
  final int createTime;
  
  /// 更新时间
  final int updateTime;
  
  /// 创建人
  final String createPerson;
  
  /// 更新人
  final String updatePerson;
  
  /// 删除标志（0=正常，1=删除）
  final int dr;

  const FreeInternshipExempt({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.avatar,
    required this.planId,
    required this.reason,
    required this.fileUrl,
    required this.status,
    this.reviewId,
    this.reviewPerson,
    this.reviewRole,
    this.reviewOpinion,
    required this.createTime,
    required this.updateTime,
    required this.createPerson,
    required this.updatePerson,
    required this.dr,
  });

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case 0:
        return '待审批';
      case 1:
        return '已通过';
      case 2:
        return '已驳回';
      default:
        return '未知状态';
    }
  }

  /// 是否为待审批状态
  bool get isPending => status == 0;

  /// 是否为已通过状态
  bool get isApproved => status == 1;

  /// 是否为已驳回状态
  bool get isRejected => status == 2;

  @override
  List<Object?> get props => [
        id,
        studentId,
        studentName,
        avatar,
        planId,
        reason,
        fileUrl,
        status,
        reviewId,
        reviewPerson,
        reviewRole,
        reviewOpinion,
        createTime,
        updateTime,
        createPerson,
        updatePerson,
        dr,
      ];
}
