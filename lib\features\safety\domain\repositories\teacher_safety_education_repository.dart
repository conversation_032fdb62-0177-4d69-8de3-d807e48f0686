/// -----
/// teacher_safety_education_repository.dart
/// 
/// 教师端安全教育考试仓库接口
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures/failure.dart';

import '../models/teacher_safety_education_response.dart';

/// 教师端安全教育考试仓库接口
/// 
/// 定义获取教师端安全教育考试数据的方法
abstract class TeacherSafetyEducationRepository {
  /// 获取教师端安全教育考试数据
  /// 
  /// [planId] 实习计划ID
  /// 
  /// 返回 [Right] 包含教师端安全教育考试数据响应，或 [Left] 包含失败信息
  Future<Either<Failure, TeacherSafetyEducationResponse>> getTeacherSafetyEducationData(String planId);
}
