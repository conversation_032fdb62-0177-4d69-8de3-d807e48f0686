/// 安全教育考试模型
///
/// 包含考试分数和试题列表
///
/// <AUTHOR>
/// @date 2025-04-30
/// @version 1.0
class SafetyExam {
  /// 考试分数
  final int score;
  
  /// 试题列表
  final List<ExamQuestion> questions;

  SafetyExam({
    required this.score,
    required this.questions,
  });

  /// 从JSON映射创建SafetyExam实例
  factory SafetyExam.fromJson(Map<String, dynamic> json) {
    return SafetyExam(
      score: json['score'],
      questions: (json['questions'] as List)
          .map((question) => ExamQuestion.fromJson(question))
          .toList(),
    );
  }

  /// 将SafetyExam实例转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'score': score,
      'questions': questions.map((question) => question.toJson()).toList(),
    };
  }
}

/// 考试试题模型
///
/// 包含试题ID、题目内容、正确答案、学生答案和选项列表
///
/// <AUTHOR>
/// @date 2025-04-30
/// @version 1.0
class ExamQuestion {
  /// 试题ID
  final int id;
  
  /// 题目内容
  final String question;
  
  /// 正确答案
  final String correctAnswer;
  
  /// 学生答案
  final String studentAnswer;
  
  /// 选项列表
  final List<ExamOption> options;

  ExamQuestion({
    required this.id,
    required this.question,
    required this.correctAnswer,
    required this.studentAnswer,
    required this.options,
  });

  /// 从JSON映射创建ExamQuestion实例
  factory ExamQuestion.fromJson(Map<String, dynamic> json) {
    return ExamQuestion(
      id: json['id'],
      question: json['question'],
      correctAnswer: json['correctAnswer'],
      studentAnswer: json['studentAnswer'],
      options: (json['options'] as List)
          .map((option) => ExamOption.fromJson(option))
          .toList(),
    );
  }

  /// 将ExamQuestion实例转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'question': question,
      'correctAnswer': correctAnswer,
      'studentAnswer': studentAnswer,
      'options': options.map((option) => option.toJson()).toList(),
    };
  }

  /// 判断学生答案是否正确
  bool get isCorrect => correctAnswer == studentAnswer;
}

/// 试题选项模型
///
/// 包含选项键和值
///
/// <AUTHOR>
/// @date 2025-04-30
/// @version 1.0
class ExamOption {
  /// 选项键（如A、B、C、D）
  final String key;
  
  /// 选项值（选项内容）
  final String value;

  ExamOption({
    required this.key,
    required this.value,
  });

  /// 从JSON映射创建ExamOption实例
  factory ExamOption.fromJson(Map<String, dynamic> json) {
    return ExamOption(
      key: json['key'],
      value: json['value'],
    );
  }

  /// 将ExamOption实例转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'key': key,
      'value': value,
    };
  }
}
