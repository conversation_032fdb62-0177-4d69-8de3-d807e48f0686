/// -----------------------------------------------------------------------------
/// get_internship_plan_list_usecase.dart
///
/// 获取实习计划列表用例
/// 用于获取当前用户的所有实习计划列表
///
/// <AUTHOR>
/// @date 2025-06-16
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/internship_plan_list_item.dart';
import '../repositories/internship_plan_repository.dart';

/// 获取实习计划列表用例
/// 
/// 用于获取当前用户（老师和学生）的所有实习计划列表
class GetInternshipPlanListUseCase implements UseCase<List<InternshipPlanListItem>, NoParams> {
  final InternshipPlanRepository repository;

  GetInternshipPlanListUseCase(this.repository);

  @override
  Future<Either<Failure, List<InternshipPlanListItem>>> call(NoParams params) async {
    return await repository.getInternshipPlanList();
  }
}
