/// -----
/// exemption_records_data_source.dart
///
/// 免实习记录数据源接口
/// 定义获取免实习记录的抽象方法
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../models/exemption_record_model.dart';

/// 免实习记录数据源接口
///
/// 定义获取免实习记录数据的抽象方法
abstract class ExemptionRecordsDataSource {
  /// 获取学生免实习记录
  ///
  /// [planId] 实习计划ID
  /// 返回免实习记录，如果没有记录则返回null
  Future<ExemptionRecordModel?> getExemptionRecord({
    required String planId,
  });
}
