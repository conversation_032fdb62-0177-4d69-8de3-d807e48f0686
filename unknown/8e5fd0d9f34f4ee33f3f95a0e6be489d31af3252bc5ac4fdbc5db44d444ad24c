/// -----
/// get_file_detail_usecase.dart
///
/// 获取文件详情用例，处理文件详情获取的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../../data/models/file_detail_model.dart';
import '../repositories/file_upload_repository.dart';

/// 获取文件详情用例
class GetFileDetailUseCase implements UseCase<FileDetailModel, GetFileDetailParams> {
  final FileUploadRepository repository;

  GetFileDetailUseCase(this.repository);

  @override
  Future<Either<Failure, FileDetailModel>> call(GetFileDetailParams params) async {
    return await repository.getFileDetail(fileId: params.fileId);
  }
}

/// 获取文件详情参数
class GetFileDetailParams {
  /// 文件ID
  final String fileId;

  GetFileDetailParams({
    required this.fileId,
  });
}
