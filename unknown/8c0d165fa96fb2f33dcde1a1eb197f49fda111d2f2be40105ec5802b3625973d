/// -----
/// file_upload_bloc.dart
/// 
/// 文件上传BLoC，处理文件上传相关的状态管理
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/file_requirement.dart';
import '../../domain/usecases/get_file_requirements_usecase.dart';
import '../../domain/usecases/upload_file_usecase.dart';
import '../../domain/usecases/get_file_detail_usecase.dart';
import 'file_upload_event.dart';
import 'file_upload_state.dart';

/// 文件上传BLoC
///
/// 处理文件上传相关的状态管理
class FileUploadBloc extends Bloc<FileUploadEvent, FileUploadState> {
  FileUploadBloc(
    this._getFileRequirementsUseCase,
    this._uploadFileUseCase,
    this._getFileDetailUseCase,
  ) : super(FileUploadInitial()) {
    on<LoadFileRequirementsEvent>(_onLoadFileRequirements);
    on<RefreshFileRequirementsEvent>(_onRefreshFileRequirements);
    on<SelectFileEvent>(_onSelectFile);
    on<StartUploadFileEvent>(_onStartUploadFile);
    on<RemoveSelectedFileEvent>(_onRemoveSelectedFile);
    on<ResetUploadStateEvent>(_onResetUploadState);
    on<LoadFileDetailEvent>(_onLoadFileDetail);
  }

  final GetFileRequirementsUseCase _getFileRequirementsUseCase;
  final UploadFileUseCase _uploadFileUseCase;
  final GetFileDetailUseCase _getFileDetailUseCase;

  /// 处理加载文件要求列表事件
  Future<void> _onLoadFileRequirements(
    LoadFileRequirementsEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    emit(const FileUploadLoading(isFirstFetch: true));

    final result = await _getFileRequirementsUseCase(
      GetFileRequirementsParams(planId: event.planId),
    );

    result.fold(
      (failure) => emit(FileUploadFailure(message: failure.message)),
      (fileRequirements) => emit(FileUploadSuccess(fileRequirements: fileRequirements)),
    );
  }

  /// 处理刷新文件要求列表事件
  Future<void> _onRefreshFileRequirements(
    RefreshFileRequirementsEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    final currentState = state;
    List<FileRequirement> oldFileRequirements = [];

    if (currentState is FileUploadSuccess) {
      oldFileRequirements = currentState.fileRequirements;
    }

    emit(FileUploadLoading(oldFileRequirements: oldFileRequirements));

    final result = await _getFileRequirementsUseCase(
      GetFileRequirementsParams(planId: event.planId),
    );

    result.fold(
      (failure) => emit(FileUploadRefreshFailure(
        message: failure.message,
        fileRequirements: oldFileRequirements,
      )),
      (fileRequirements) => emit(FileUploadRefreshSuccess(
        fileRequirements: fileRequirements,
      )),
    );
  }

  /// 处理选择文件事件
  Future<void> _onSelectFile(
    SelectFileEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    final currentState = state;
    List<FileRequirement> fileRequirements = [];

    if (currentState is FileUploadSuccess) {
      fileRequirements = currentState.fileRequirements;
    }

    emit(FileSelectedState(
      fileRequirements: fileRequirements,
      filePath: event.filePath,
      fileName: event.fileName,
      fileSize: event.fileSize,
    ));
  }

  /// 处理开始上传文件事件
  Future<void> _onStartUploadFile(
    StartUploadFileEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    final currentState = state;
    List<FileRequirement> fileRequirements = [];

    if (currentState is FileSelectedState) {
      fileRequirements = currentState.fileRequirements;
    } else if (currentState is FileUploadSuccess) {
      fileRequirements = currentState.fileRequirements;
    }

    // 发出上传中状态
    emit(FileUploadingState(
      fileRequirements: fileRequirements,
      fileName: event.fileName,
      progress: 0.0,
      stepDescription: '准备上传...',
    ));

    final result = await _uploadFileUseCase(
      UploadFileParams(
        filePath: event.filePath,
        fileName: event.fileName,
        fileType: event.fileType,
        planId: event.planId,
        fileCode: event.fileCode,
        onProgress: (progress, stepDescription) {
          emit(FileUploadingState(
            fileRequirements: fileRequirements,
            fileName: event.fileName,
            progress: progress,
            stepDescription: stepDescription,
          ));
        },
      ),
    );

    result.fold(
      (failure) => emit(FileUploadFailureState(
        fileRequirements: fileRequirements,
        fileName: event.fileName,
        message: failure.message,
      )),
      (uploadResult) => emit(FileUploadSuccessState(
        fileRequirements: fileRequirements,
        fileName: uploadResult.fileName,
        message: '文件上传成功',
      )),
    );
  }

  /// 处理移除选中文件事件
  Future<void> _onRemoveSelectedFile(
    RemoveSelectedFileEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    final currentState = state;

    if (currentState is FileSelectedState) {
      emit(FileUploadSuccess(fileRequirements: currentState.fileRequirements));
    }
  }

  /// 处理重置上传状态事件
  Future<void> _onResetUploadState(
    ResetUploadStateEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    final currentState = state;

    if (currentState is FileUploadSuccessState ||
        currentState is FileUploadFailureState ||
        currentState is FileUploadingState) {
      List<FileRequirement> fileRequirements = [];

      if (currentState is FileUploadSuccessState) {
        fileRequirements = currentState.fileRequirements;
      } else if (currentState is FileUploadFailureState) {
        fileRequirements = currentState.fileRequirements;
      } else if (currentState is FileUploadingState) {
        fileRequirements = currentState.fileRequirements;
      }

      emit(FileUploadSuccess(fileRequirements: fileRequirements));
    }
  }

  /// 处理加载文件详情事件
  Future<void> _onLoadFileDetail(
    LoadFileDetailEvent event,
    Emitter<FileUploadState> emit,
  ) async {
    final currentState = state;
    List<FileRequirement> fileRequirements = [];

    if (currentState is FileUploadSuccess) {
      fileRequirements = currentState.fileRequirements;
    } else if (currentState is FileDetailLoadedState) {
      fileRequirements = currentState.fileRequirements;
    }

    // 发出加载中状态
    emit(FileDetailLoadingState(fileRequirements: fileRequirements));

    final result = await _getFileDetailUseCase(
      GetFileDetailParams(fileId: event.fileId),
    );

    result.fold(
      (failure) => emit(FileDetailFailureState(
        fileRequirements: fileRequirements,
        message: failure.message,
      )),
      (fileDetail) => emit(FileDetailLoadedState(
        fileRequirements: fileRequirements,
        fileDetail: fileDetail,
      )),
    );
  }
}
