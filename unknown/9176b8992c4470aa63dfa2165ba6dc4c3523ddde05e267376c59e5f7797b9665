/// -----
/// file_upload_repository_impl.dart
/// 
/// 文件上传仓库实现类，实现文件上传相关的数据操作
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/exceptions/network_exception.dart';
import '../../../../core/error/exceptions/server_exception.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/network/network_info.dart';
import '../../domain/entities/file_requirement.dart';
import '../../domain/repositories/file_upload_repository.dart';
import '../../domain/usecases/upload_file_usecase.dart';
import '../datasources/remote/file_upload_remote_data_source.dart';
import '../models/file_detail_model.dart';

/// 文件上传仓库实现类
/// 
/// 实现文件上传相关的数据操作
class FileUploadRepositoryImpl implements FileUploadRepository {
  final FileUploadRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  FileUploadRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<FileRequirement>>> getFileRequirements({
    required String planId,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final fileRequirementModels = await _remoteDataSource.getFileRequirements(
          planId: planId,
        );
        
        // 将模型转换为实体
        final fileRequirements = fileRequirementModels
            .map((model) => model.toEntity())
            .toList();
            
        return Right(fileRequirements);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取文件要求列表失败: $e'));
      }
    } else {
      return Left(NetworkFailure('网络连接失败，请检查网络设置'));
    }
  }

  @override
  Future<Either<Failure, UploadFileResult>> uploadFile({
    required String filePath,
    required String fileName,
    required String fileType,
    required int planId,
    required int fileCode,
    Function(double progress, String stepDescription)? onProgress,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        // 第一步：上传文件到文件服务器
        onProgress?.call(0.0, '正在上传文件...');

        final uploadResponse = await _remoteDataSource.uploadFileToServer(
          filePath: filePath,
          fileName: fileName,
          onProgress: (progress) {
            onProgress?.call(progress * 0.7, '正在上传文件...');
          },
        );

        final uploadedFileName = uploadResponse['fileName'] as String;
        final fileUrl = uploadResponse['fileUrl'] as String;

        // 第二步：保存文件信息到后端
        onProgress?.call(0.7, '正在保存文件信息...');

        final saveResultId = await _remoteDataSource.saveFileInfo(
          fileCode: fileCode,
          fileName: uploadedFileName,
          fileType: fileType,
          fileUrl: fileUrl,
          planId: planId,
        );

        onProgress?.call(1.0, '上传完成');

        return Right(UploadFileResult(
          fileName: uploadedFileName,
          fileUrl: fileUrl,
          saveResultId: saveResultId,
        ));

      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('文件上传失败: $e'));
      }
    } else {
      return Left(NetworkFailure('网络连接失败，请检查网络设置'));
    }
  }

  @override
  Future<Either<Failure, FileDetailModel>> getFileDetail({
    required String fileId,
  }) async {
    if (await _networkInfo.isConnected) {
      try {
        final response = await _remoteDataSource.getFileDetail(fileId: fileId);
        final fileDetail = FileDetailModel.fromJson(response);
        return Right(fileDetail);
      } on ServerException catch (e) {
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        return Left(NetworkFailure(e.message));
      } catch (e) {
        return Left(ServerFailure('获取文件详情失败: $e'));
      }
    } else {
      return Left(NetworkFailure('网络连接失败，请检查网络设置'));
    }
  }
}
