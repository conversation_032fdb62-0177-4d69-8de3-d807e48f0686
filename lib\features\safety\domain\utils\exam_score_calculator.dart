/// -----
/// exam_score_calculator.dart
/// 
/// 安全教育考试分数计算工具
///
/// <AUTHOR>
/// @date 2025-06-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../models/safety_exam_question.dart';

/// 安全教育考试分数计算器
///
/// 提供考试分数计算功能，支持单选题和多选题
class ExamScoreCalculator {
  /// 总分
  static const int totalScore = 100;

  /// 计算考试分数
  ///
  /// [questions] 题目列表
  /// [userAnswers] 用户答案映射，key为题目ID，value为用户选择的选项ID列表
  /// 
  /// 返回整数分数（0-100）
  static int calculateScore(
    List<SafetyExamQuestion> questions,
    Map<String, List<String>> userAnswers,
  ) {
    if (questions.isEmpty) {
      return 0;
    }

    int correctCount = 0;

    for (final question in questions) {
      final userAnswer = userAnswers[question.id];
      if (userAnswer == null || userAnswer.isEmpty) {
        // 用户未回答此题
        continue;
      }

      if (_isAnswerCorrect(question, userAnswer)) {
        correctCount++;
      }
    }

    // 计算分数，四舍五入到整数
    final score = (correctCount * totalScore / questions.length).round();
    return score.clamp(0, totalScore);
  }

  /// 判断用户答案是否正确（公共方法）
  ///
  /// [question] 题目
  /// [userAnswer] 用户选择的选项ID列表
  ///
  /// 对于单选题：用户选择的选项必须与正确答案完全匹配
  /// 对于多选题：用户选择的所有选项都必须是正确答案，且不能遗漏任何正确选项
  static bool isAnswerCorrect(
    SafetyExamQuestion question,
    List<String> userAnswer,
  ) {
    return _isAnswerCorrect(question, userAnswer);
  }

  /// 判断用户答案是否正确
  ///
  /// [question] 题目
  /// [userAnswer] 用户选择的选项ID列表
  ///
  /// 对于单选题：用户选择的选项必须与正确答案完全匹配
  /// 对于多选题：用户选择的所有选项都必须是正确答案，且不能遗漏任何正确选项
  static bool _isAnswerCorrect(
    SafetyExamQuestion question,
    List<String> userAnswer,
  ) {
    final correctAnswers = question.correctAnswers;
    
    if (question.isMultipleChoice) {
      // 多选题：用户答案必须与正确答案完全匹配（顺序无关）
      if (userAnswer.length != correctAnswers.length) {
        return false;
      }
      
      // 检查用户答案是否包含所有正确答案
      for (final correctAnswer in correctAnswers) {
        if (!userAnswer.contains(correctAnswer)) {
          return false;
        }
      }
      
      // 检查用户答案是否包含错误选项
      for (final answer in userAnswer) {
        if (!correctAnswers.contains(answer)) {
          return false;
        }
      }
      
      return true;
    } else {
      // 单选题：用户只能选择一个选项，且必须是正确答案
      return userAnswer.length == 1 && 
             userAnswer.first == correctAnswers.first;
    }
  }

  /// 获取题目的分值
  ///
  /// [totalQuestions] 总题目数
  /// 
  /// 返回每题的分值（可能包含小数）
  static double getQuestionScore(int totalQuestions) {
    if (totalQuestions <= 0) {
      return 0.0;
    }
    return totalScore / totalQuestions;
  }

  /// 计算答对题目数量
  ///
  /// [questions] 题目列表
  /// [userAnswers] 用户答案映射
  /// 
  /// 返回答对的题目数量
  static int getCorrectCount(
    List<SafetyExamQuestion> questions,
    Map<String, List<String>> userAnswers,
  ) {
    int correctCount = 0;

    for (final question in questions) {
      final userAnswer = userAnswers[question.id];
      if (userAnswer != null && 
          userAnswer.isNotEmpty && 
          _isAnswerCorrect(question, userAnswer)) {
        correctCount++;
      }
    }

    return correctCount;
  }

  /// 计算答错题目数量
  ///
  /// [questions] 题目列表
  /// [userAnswers] 用户答案映射
  /// 
  /// 返回答错的题目数量
  static int getIncorrectCount(
    List<SafetyExamQuestion> questions,
    Map<String, List<String>> userAnswers,
  ) {
    int incorrectCount = 0;

    for (final question in questions) {
      final userAnswer = userAnswers[question.id];
      if (userAnswer != null && 
          userAnswer.isNotEmpty && 
          !_isAnswerCorrect(question, userAnswer)) {
        incorrectCount++;
      }
    }

    return incorrectCount;
  }

  /// 获取考试结果摘要
  ///
  /// [questions] 题目列表
  /// [userAnswers] 用户答案映射
  /// 
  /// 返回考试结果摘要
  static ExamResultSummary getExamSummary(
    List<SafetyExamQuestion> questions,
    Map<String, List<String>> userAnswers,
  ) {
    final totalQuestions = questions.length;
    final correctCount = getCorrectCount(questions, userAnswers);
    final incorrectCount = getIncorrectCount(questions, userAnswers);
    final unansweredCount = totalQuestions - correctCount - incorrectCount;
    final score = calculateScore(questions, userAnswers);

    return ExamResultSummary(
      totalQuestions: totalQuestions,
      correctCount: correctCount,
      incorrectCount: incorrectCount,
      unansweredCount: unansweredCount,
      score: score,
    );
  }
}

/// 考试结果摘要
class ExamResultSummary {
  /// 总题目数
  final int totalQuestions;
  
  /// 答对题目数
  final int correctCount;
  
  /// 答错题目数
  final int incorrectCount;
  
  /// 未回答题目数
  final int unansweredCount;
  
  /// 总分数
  final int score;

  const ExamResultSummary({
    required this.totalQuestions,
    required this.correctCount,
    required this.incorrectCount,
    required this.unansweredCount,
    required this.score,
  });

  /// 正确率（百分比）
  double get accuracyRate {
    if (totalQuestions == 0) return 0.0;
    return (correctCount / totalQuestions) * 100;
  }

  /// 是否及格（60分及以上）
  bool get isPassed => score >= 60;
}
