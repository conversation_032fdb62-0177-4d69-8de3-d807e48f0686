/// -----
/// text_field.dart
///
/// 文本输入字段组件
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/form_field_item.dart';

/// 文本输入字段组件
class TextInputField extends StatelessWidget {
  /// 字段标签
  final String label;
  
  /// 当前值
  final String value;
  
  /// 占位符文本
  final String placeholder;
  
  /// 值变化回调
  final Function(String) onChanged;
  
  /// 是否显示分隔线
  final bool showDivider;
  
  /// 键盘类型
  final TextInputType keyboardType;

  const TextInputField({
    super.key,
    required this.label,
    required this.value,
    this.placeholder = '请输入',
    required this.onChanged,
    this.showDivider = true,
    this.keyboardType = TextInputType.text,
  });

  @override
  Widget build(BuildContext context) {
    return FormFieldItem(
      label: label,
      value: value,
      type: FormFieldType.input,
      showDivider: showDivider,
      onChanged: onChanged,
    );
  }
}
