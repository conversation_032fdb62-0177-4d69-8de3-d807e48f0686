/// -----
/// web_responsive_file_upload.dart
///
/// Web响应式文件上传组件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

/// Web响应式文件上传组件
class WebResponsiveFileUpload extends StatefulWidget {
  /// 文件选择回调
  final Function(String filePath, String fileName, int fileSize)? onFileSelected;
  /// 文件移除回调
  final VoidCallback? onFileRemoved;
  /// 是否正在上传
  final bool isUploading;
  /// 上传进度 (0.0 - 1.0)
  final double uploadProgress;
  /// 上传步骤描述
  final String? stepDescription;
  /// 选中的文件信息
  final File? selectedFile;

  const WebResponsiveFileUpload({
    Key? key,
    this.onFileSelected,
    this.onFileRemoved,
    this.isUploading = false,
    this.uploadProgress = 0.0,
    this.stepDescription,
    this.selectedFile,
  }) : super(key: key);

  @override
  State<WebResponsiveFileUpload> createState() => _WebResponsiveFileUploadState();
}

class _WebResponsiveFileUploadState extends State<WebResponsiveFileUpload> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: _getMargin(),
      child: _buildFileUploadArea(),
    );
  }

  /// 获取边距
  EdgeInsets _getMargin() {
    if (kIsWeb) {
      return const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0);
    }
    return EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h);
  }

  /// 构建文件上传区域
  Widget _buildFileUploadArea() {
    return Container(
      padding: _getPadding(),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: _getBorderRadius(),
        boxShadow: kIsWeb ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Column(
        children: [
          // 如果有选中的文件，显示文件信息
          if (widget.selectedFile != null) ...[
            _buildSelectedFileItem(),
            SizedBox(height: _getSpacing()),
          ],

          // 如果正在上传，显示上传进度
          if (widget.isUploading) ...[
            _buildUploadProgress(),
            SizedBox(height: _getSpacing()),
          ],

          // 如果没有选中文件或上传完成，显示选择按钮
          if (widget.selectedFile == null && !widget.isUploading)
            _buildSelectFileButton(),

          SizedBox(height: _getSpacing()),

          // 上传提示
          Text(
            '目前仅支持上传PDF格式文件，每个文件大小不超过100M。',
            style: TextStyle(
              fontSize: _getFontSize(small: true),
              color: AppTheme.black999,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建选中的文件项
  Widget _buildSelectedFileItem() {
    if (widget.selectedFile == null) return const SizedBox.shrink();

    final file = widget.selectedFile!;
    final fileName = file.path.split('/').last;

    return Container(
      padding: _getPadding(small: true),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: _getBorderRadius(small: true),
        border: Border.all(color: const Color(0xFFE5E5E5)),
      ),
      child: Row(
        children: [
          // PDF图标
          Icon(
            Icons.picture_as_pdf,
            size: _getIconSize(),
            color: Colors.red,
          ),
          SizedBox(width: _getSpacing(small: true)),

          // 文件信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: TextStyle(
                    fontSize: _getFontSize(),
                    color: AppTheme.black333,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                FutureBuilder<int>(
                  future: file.length(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      final sizeInMB = snapshot.data! / (1024 * 1024);
                      return Text(
                        '${sizeInMB.toStringAsFixed(1)} MB',
                        style: TextStyle(
                          fontSize: _getFontSize(small: true),
                          color: AppTheme.black999,
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),

          // 删除按钮
          if (!widget.isUploading)
            GestureDetector(
              onTap: widget.onFileRemoved,
              child: Container(
                padding: _getPadding(small: true),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: _getBorderRadius(small: true),
                ),
                child: Icon(
                  Icons.delete_outline,
                  size: _getIconSize(small: true),
                  color: Colors.red,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建上传进度
  Widget _buildUploadProgress() {
    return Column(
      children: [
        // 进度条
        LinearProgressIndicator(
          value: widget.uploadProgress,
          backgroundColor: const Color(0xFFE5E5E5),
          valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),
        SizedBox(height: _getSpacing(small: true)),

        // 进度文本
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.stepDescription ?? '上传中...',
              style: TextStyle(
                fontSize: _getFontSize(small: true),
                color: AppTheme.black666,
              ),
            ),
            Text(
              '${(widget.uploadProgress * 100).toInt()}%',
              style: TextStyle(
                fontSize: _getFontSize(small: true),
                color: AppTheme.black666,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建选择文件按钮
  Widget _buildSelectFileButton() {
    return GestureDetector(
      onTap: _pickPDFFile,
      child: Container(
        height: _getButtonHeight(),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: _getBorderRadius(),
          border: Border.all(
            color: const Color(0xFFE5E5E5),
            width: 2,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: _getIconSize(),
                color: AppTheme.primaryColor,
              ),
              SizedBox(height: _getSpacing(small: true)),
              Text(
                '点击选择PDF文件',
                style: TextStyle(
                  fontSize: _getFontSize(),
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 选择PDF文件
  Future<void> _pickPDFFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final fileName = result.files.first.name;
        final fileSize = await file.length();

        // 检查文件大小（100MB限制）
        if (fileSize > 100 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('文件大小不能超过100MB')),
            );
          }
          return;
        }

        // 触发文件选择回调
        widget.onFileSelected?.call(file.path, fileName, fileSize);
      }
    } catch (e) {
      debugPrint('Error picking PDF file: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('选择文件失败，请重试')),
        );
      }
    }
  }

  // 响应式尺寸方法
  EdgeInsets _getPadding({bool small = false}) {
    if (kIsWeb) {
      return small ? const EdgeInsets.all(12.0) : const EdgeInsets.all(24.0);
    }
    return small ? EdgeInsets.all(12.w) : EdgeInsets.all(30.w);
  }

  BorderRadius _getBorderRadius({bool small = false}) {
    if (kIsWeb) {
      return small ? BorderRadius.circular(8.0) : BorderRadius.circular(16.0);
    }
    return small ? BorderRadius.circular(8.r) : BorderRadius.circular(20.r);
  }

  double _getSpacing({bool small = false}) {
    if (kIsWeb) {
      return small ? 8.0 : 16.0;
    }
    return small ? 8.h : 20.h;
  }

  double _getFontSize({bool small = false}) {
    if (kIsWeb) {
      return small ? 12.0 : 16.0;
    }
    return small ? 24.sp : 28.sp;
  }

  double _getIconSize({bool small = false}) {
    if (kIsWeb) {
      return small ? 20.0 : 32.0;
    }
    return small ? 20.w : 40.w;
  }

  double _getButtonHeight() {
    if (kIsWeb) {
      return 120.0;
    }
    return 120.h;
  }
}
