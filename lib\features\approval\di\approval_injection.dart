/// -----
/// approval_injection.dart
///
/// 审批模块依赖注入配置
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import '../../../core/network/dio_client.dart';
import '../../../core/network/network_info.dart';
import '../data/datasources/remote/file_approval_remote_data_source.dart';
import '../data/datasources/remote/file_approval_remote_data_source_impl.dart';
import '../data/datasources/remote/student_file_approval_remote_data_source.dart';
import '../data/datasources/remote/student_file_approval_remote_data_source_impl.dart';
import '../data/repositories/file_approval_repository_impl.dart';
import '../data/repositories/student_file_approval_repository_impl.dart';
import '../domain/repositories/file_approval_repository.dart';
import '../domain/repositories/student_file_approval_repository.dart';
import '../domain/usecases/get_file_approval_list_usecase.dart';
import '../domain/usecases/get_student_file_approval_list_usecase.dart';
import '../presentation/bloc/file_approval_list/file_approval_list_bloc.dart';
import '../presentation/bloc/student_file_approval_list/student_file_approval_list_bloc.dart';

final getIt = GetIt.instance;

/// 初始化审批模块依赖
///
/// 注册审批模块的数据源、仓库、用例和BLoC
Future<void> setupApprovalDependencies() async {
  // 文件审批列表数据源
  getIt.registerLazySingleton<FileApprovalRemoteDataSource>(
    () => FileApprovalRemoteDataSourceImpl(getIt<DioClient>()),
  );

  // 学生文件审批列表数据源
  getIt.registerLazySingleton<StudentFileApprovalRemoteDataSource>(
    () => StudentFileApprovalRemoteDataSourceImpl(getIt<DioClient>()),
  );

  // 文件审批列表仓库
  getIt.registerLazySingleton<FileApprovalRepository>(
    () => FileApprovalRepositoryImpl(
      getIt<FileApprovalRemoteDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  // 学生文件审批列表仓库
  getIt.registerLazySingleton<StudentFileApprovalRepository>(
    () => StudentFileApprovalRepositoryImpl(
      getIt<StudentFileApprovalRemoteDataSource>(),
      getIt<NetworkInfo>(),
    ),
  );

  // 文件审批列表用例
  getIt.registerFactory<GetFileApprovalListUseCase>(
    () => GetFileApprovalListUseCase(getIt<FileApprovalRepository>()),
  );

  // 学生文件审批列表用例
  getIt.registerFactory<GetStudentFileApprovalListUseCase>(
    () => GetStudentFileApprovalListUseCase(getIt<StudentFileApprovalRepository>()),
  );

  // 文件审批列表BLoC
  getIt.registerFactory<FileApprovalListBloc>(
    () => FileApprovalListBloc(
      getFileApprovalListUseCase: getIt<GetFileApprovalListUseCase>(),
    ),
  );

  // 学生文件审批列表BLoC
  getIt.registerFactory<StudentFileApprovalListBloc>(
    () => StudentFileApprovalListBloc(
      getStudentFileApprovalListUseCase: getIt<GetStudentFileApprovalListUseCase>(),
    ),
  );
}
