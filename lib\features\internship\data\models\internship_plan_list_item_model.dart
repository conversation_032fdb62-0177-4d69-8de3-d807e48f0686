/// -----------------------------------------------------------------------------
/// internship_plan_list_item_model.dart
///
/// 实习计划列表项数据模型
/// 用于API数据的序列化和反序列化
///
/// <AUTHOR>
/// @date 2025-06-16
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:equatable/equatable.dart';
import '../../domain/entities/internship_plan_list_item.dart';

/// 实习计划列表项数据模型
/// 
/// 用于API数据的序列化和反序列化
class InternshipPlanListItemModel extends Equatable {
  /// 实习计划ID
  final String planId;
  
  /// 实习计划名称
  final String planName;
  
  /// 计划编号
  final String planCode;
  
  /// 学年，例如：2024-2025
  final String academicYear;
  
  /// 实习年级，例如：2025
  final String grade;
  
  /// 类型（1=岗位实习, 2=认识实习, 3=其他实习, 4=学徒制, 5=综合实训, 6=工学交替）
  final int planType;
  
  /// 实习形式（1=校外实习, 2=校内真实职业场景, 3=虚拟仿真）
  final int practiceMode;
  
  /// 学期，例如：2025-2026实习学年第一学期/第二学期
  final String semester;
  
  /// 学生ID
  final String studentId;
  
  /// 创建时间（时间戳）
  final int createTime;
  
  /// 状态（0=待启用，1=进行中, 2=已结束）
  final int planStatus;

  const InternshipPlanListItemModel({
    required this.planId,
    required this.planName,
    required this.planCode,
    required this.academicYear,
    required this.grade,
    required this.planType,
    required this.practiceMode,
    required this.semester,
    required this.studentId,
    required this.createTime,
    required this.planStatus,
  });

  /// 从JSON创建模型实例
  factory InternshipPlanListItemModel.fromJson(Map<String, dynamic> json) {
    return InternshipPlanListItemModel(
      planId: json['planId']?.toString() ?? '',
      planName: json['planName']?.toString() ?? '',
      planCode: json['planCode']?.toString() ?? '',
      academicYear: json['academicYear']?.toString() ?? '',
      grade: json['grade']?.toString() ?? '',
      planType: json['planType'] as int? ?? 1,
      practiceMode: json['practiceMode'] as int? ?? 1,
      semester: json['semester']?.toString() ?? '',
      studentId: json['studentId']?.toString() ?? '',
      createTime: json['createTime'] as int? ?? 0,
      planStatus: json['planStatus'] as int? ?? 0,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'planId': planId,
      'planName': planName,
      'planCode': planCode,
      'academicYear': academicYear,
      'grade': grade,
      'planType': planType,
      'practiceMode': practiceMode,
      'semester': semester,
      'studentId': studentId,
      'createTime': createTime,
      'planStatus': planStatus,
    };
  }

  /// 转换为实体
  InternshipPlanListItem toEntity() {
    return InternshipPlanListItem(
      planId: planId,
      planName: planName,
      planCode: planCode,
      academicYear: academicYear,
      grade: grade,
      planType: planType,
      practiceMode: practiceMode,
      semester: semester,
      studentId: studentId,
      createTime: createTime,
      planStatus: planStatus,
    );
  }

  /// 从实体创建模型
  factory InternshipPlanListItemModel.fromEntity(InternshipPlanListItem entity) {
    return InternshipPlanListItemModel(
      planId: entity.planId,
      planName: entity.planName,
      planCode: entity.planCode,
      academicYear: entity.academicYear,
      grade: entity.grade,
      planType: entity.planType,
      practiceMode: entity.practiceMode,
      semester: entity.semester,
      studentId: entity.studentId,
      createTime: entity.createTime,
      planStatus: entity.planStatus,
    );
  }

  @override
  List<Object?> get props => [
    planId,
    planName,
    planCode,
    academicYear,
    grade,
    planType,
    practiceMode,
    semester,
    studentId,
    createTime,
    planStatus,
  ];
}
