/// -----
/// report_write_bloc.dart
/// 
/// 写报告页面的BLoC状态管理
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/services/file_picker_service.dart';
import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_state.dart';

class ReportWriteBloc extends Bloc<ReportWriteEvent, ReportWriteState> {
  ReportWriteBloc() : super(ReportWriteInitial()) {
    on<InitializeReportWriteEvent>(_onInitializeReportWrite);
    on<SelectCourseEvent>(_onSelectCourse);
    on<UpdateReportContentEvent>(_onUpdateReportContent);
    on<GenerateAIContentEvent>(_onGenerateAIContent);
    on<SelectFileEvent>(_onSelectFile);
    on<AddAttachmentEvent>(_onAddAttachment);
    on<RemoveAttachmentEvent>(_onRemoveAttachment);
    on<UploadAttachmentEvent>(_onUploadAttachment);
    on<SaveDraftEvent>(_onSaveDraft);
    on<SubmitReportEvent>(_onSubmitReport);
    on<LoadHistoryReportsEvent>(_onLoadHistoryReports);
  }

  /// 初始化写报告页面
  Future<void> _onInitializeReportWrite(
    InitializeReportWriteEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    emit(ReportWriteLoading());

    try {
      // 模拟加载课程列表
      await Future.delayed(const Duration(milliseconds: 500));

      final availableCourses = [
        '2021级市场销售2023-2024实习学年第二学期岗位实习',
        '2021级市场销售2023-2024实习学年第一学期岗位实习',
        '2022级市场销售2023-2024实习学年第二学期岗位实习',
      ];

      String initialContent = '';
      bool hasAIContent = false;

      // 根据报告类型设置初始内容
      if (event.reportType != ReportType.daily) {
        // 非日报类型，生成AI内容
        initialContent = _generateAIContent(event.reportType);
        hasAIContent = true;
      }

      emit(ReportWriteLoaded(
        reportType: event.reportType,
        reportId: event.reportId,
        selectedCourseId: '1',
        selectedCourseName: availableCourses[0],
        availableCourses: availableCourses,
        content: initialContent,
        attachments: [],
        hasAIContent: hasAIContent,
      ));
    } catch (e) {
      emit(ReportWriteError('初始化失败: ${e.toString()}'));
    }
  }

  /// 选择课程
  Future<void> _onSelectCourse(
    SelectCourseEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      emit(currentState.copyWith(
        selectedCourseId: event.courseId,
        selectedCourseName: event.courseName,
      ));
    }
  }

  /// 更新报告内容
  Future<void> _onUpdateReportContent(
    UpdateReportContentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      emit(currentState.copyWith(content: event.content));
    }
  }

  /// 生成AI内容
  Future<void> _onGenerateAIContent(
    GenerateAIContentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      
      emit(ReportWriteGeneratingAI(
        reportType: currentState.reportType,
        reportId: currentState.reportId,
        selectedCourseId: currentState.selectedCourseId,
        selectedCourseName: currentState.selectedCourseName,
        availableCourses: currentState.availableCourses,
        content: currentState.content,
        attachments: currentState.attachments,
        hasAIContent: currentState.hasAIContent,
        isDraft: currentState.isDraft,
      ));

      try {
        // 模拟AI生成内容
        await Future.delayed(const Duration(seconds: 2));
        
        final aiContent = _generateAIContent(event.reportType);
        
        emit(currentState.copyWith(
          content: aiContent,
          hasAIContent: true,
        ));
      } catch (e) {
        emit(ReportWriteError('AI内容生成失败: ${e.toString()}'));
      }
    }
  }

  /// 选择文件
  Future<void> _onSelectFile(
    SelectFileEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;

      // 检查是否已达到文件上传限制
      if (currentState.attachments.length >= 1) {
        emit(ReportWriteError('最多只能上传1个附件'));
        return;
      }

      try {
        // 这里可以实现文件选择逻辑
        // 由于文件选择在UI层处理，这里主要用于状态管理
        // 实际的文件选择会通过AddAttachmentEvent添加
      } catch (e) {
        emit(ReportWriteError('文件选择失败: ${e.toString()}'));
      }
    }
  }

  /// 添加附件
  Future<void> _onAddAttachment(
    AddAttachmentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;

      // 检查文件数量限制
      if (currentState.attachments.length >= 1) {
        emit(ReportWriteError('最多只能上传1个附件'));
        return;
      }

      final newAttachment = AttachmentModel(
        filePath: event.filePath,
        fileName: event.fileName,
        fileSize: event.fileSize,
      );

      final updatedAttachments = [...currentState.attachments, newAttachment];
      emit(currentState.copyWith(attachments: updatedAttachments));

      // 自动开始上传
      add(UploadAttachmentEvent(
        filePath: event.filePath,
        fileName: event.fileName,
      ));
    }
  }

  /// 上传附件到服务器
  Future<void> _onUploadAttachment(
    UploadAttachmentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;

      // 发出上传中状态
      emit(ReportWriteUploadingAttachment(
        reportType: currentState.reportType,
        reportId: currentState.reportId,
        selectedCourseId: currentState.selectedCourseId,
        selectedCourseName: currentState.selectedCourseName,
        availableCourses: currentState.availableCourses,
        content: currentState.content,
        attachments: currentState.attachments,
        hasAIContent: currentState.hasAIContent,
        isSubmitting: currentState.isSubmitting,
        isDraft: currentState.isDraft,
        uploadingFileName: event.fileName,
        uploadProgress: 0.0,
      ));

      try {
        // 模拟上传进度
        for (double progress = 0.0; progress <= 1.0; progress += 0.1) {
          await Future.delayed(const Duration(milliseconds: 200));

          if (state is ReportWriteUploadingAttachment) {
            emit(ReportWriteUploadingAttachment(
              reportType: currentState.reportType,
              reportId: currentState.reportId,
              selectedCourseId: currentState.selectedCourseId,
              selectedCourseName: currentState.selectedCourseName,
              availableCourses: currentState.availableCourses,
              content: currentState.content,
              attachments: currentState.attachments,
              hasAIContent: currentState.hasAIContent,
              isSubmitting: currentState.isSubmitting,
              isDraft: currentState.isDraft,
              uploadingFileName: event.fileName,
              uploadProgress: progress,
            ));
          }
        }

        // 上传成功，更新附件状态
        final updatedAttachments = currentState.attachments.map((attachment) {
          if (attachment.filePath == event.filePath) {
            return attachment.copyWith(
              isUploaded: true,
              serverUrl: 'https://example.com/uploads/${event.fileName}',
            );
          }
          return attachment;
        }).toList();

        emit(currentState.copyWith(attachments: updatedAttachments));
        emit(ReportWriteAttachmentUploadSuccess(
          message: '附件上传成功',
          attachment: updatedAttachments.firstWhere(
            (a) => a.filePath == event.filePath,
          ),
        ));

        // 返回到正常状态
        emit(currentState.copyWith(attachments: updatedAttachments));

      } catch (e) {
        emit(ReportWriteAttachmentUploadError('附件上传失败: ${e.toString()}'));
        // 返回到之前的状态
        emit(currentState);
      }
    }
  }

  /// 删除附件
  Future<void> _onRemoveAttachment(
    RemoveAttachmentEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      final updatedAttachments = [...currentState.attachments];
      if (event.index >= 0 && event.index < updatedAttachments.length) {
        updatedAttachments.removeAt(event.index);
        emit(currentState.copyWith(attachments: updatedAttachments));
      }
    }
  }

  /// 保存草稿
  Future<void> _onSaveDraft(
    SaveDraftEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      
      try {
        // 模拟保存草稿
        await Future.delayed(const Duration(seconds: 1));
        
        emit(ReportWriteSaveDraftSuccess('草稿保存成功'));
        
        // 返回到加载状态，标记为草稿
        emit(currentState.copyWith(isDraft: true));
      } catch (e) {
        emit(ReportWriteError('保存草稿失败: ${e.toString()}'));
      }
    }
  }

  /// 提交报告
  Future<void> _onSubmitReport(
    SubmitReportEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    if (state is ReportWriteLoaded) {
      final currentState = state as ReportWriteLoaded;
      
      // 验证内容
      if (currentState.content.trim().isEmpty) {
        emit(ReportWriteError('请填写报告内容'));
        return;
      }
      
      emit(ReportWriteSubmitting(
        reportType: currentState.reportType,
        reportId: currentState.reportId,
        selectedCourseId: currentState.selectedCourseId,
        selectedCourseName: currentState.selectedCourseName,
        availableCourses: currentState.availableCourses,
        content: currentState.content,
        attachments: currentState.attachments,
        hasAIContent: currentState.hasAIContent,
        isDraft: currentState.isDraft,
      ));

      try {
        // 模拟提交报告
        await Future.delayed(const Duration(seconds: 2));
        
        emit(ReportWriteSubmitSuccess('报告提交成功'));
      } catch (e) {
        emit(ReportWriteError('提交失败: ${e.toString()}'));
      }
    }
  }

  /// 加载历史报告
  Future<void> _onLoadHistoryReports(
    LoadHistoryReportsEvent event,
    Emitter<ReportWriteState> emit,
  ) async {
    try {
      // 模拟加载历史报告
      await Future.delayed(const Duration(seconds: 1));
      
      // 这里应该调用仓库层获取历史报告
      emit(ReportWriteHistoryLoaded([]));
    } catch (e) {
      emit(ReportWriteError('加载历史报告失败: ${e.toString()}'));
    }
  }

  /// 生成AI内容（模拟）
  String _generateAIContent(ReportType reportType) {
    switch (reportType) {
      case ReportType.weekly:
        return '本周完成的任务主要有学生端的签到，成绩查看等，工作中应该积极思考整个业务流程，我觉得部分功能需求还需要进一步研究讨论，经过上周的实习，我对我所负责的业务了解更深入的了解！';
      case ReportType.monthly:
        return '本月主要完成了实习管理系统的核心功能开发，包括学生签到、成绩管理、报告提交等模块。通过这个月的实习，我对软件开发流程有了更深入的理解，技术能力得到了显著提升。';
      case ReportType.summary:
        return '通过这次实习，我深刻体会到了理论与实践相结合的重要性。在实际工作中，不仅要掌握扎实的专业知识，还要具备良好的沟通协调能力和团队合作精神。这次实习为我今后的职业发展奠定了坚实的基础。';
      default:
        return '';
    }
  }
}
