/// -----
/// module_navigation_service.dart
///
/// 模块导航服务，负责处理模块点击导航逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/features/home/<USER>/module_item.dart';
import 'package:go_router/go_router.dart';

/// 模块导航服务
///
/// 负责处理模块点击导航逻辑，使用go_router进行导航
class ModuleNavigationService {
  /// 处理模块点击导航
  ///
  /// [context] 上下文
  /// [item] 模块项
  /// [userType] 用户类型
  static void navigateToModule(BuildContext context, ModuleItem item, String? userType) {
    debugPrint('ModuleNavigationService: 导航到模块 ${item.title}, 路由名称: ${item.routeName}, 路由路径: ${item.route}, 用户类型: $userType');

    context.push(item.route);

    // 记录分析事件
    _logAnalyticsEvent('module_click', {
      'title': item.title,
      'route': item.route,
      'routeName': item.routeName,
      'userType': userType.toString()
    });
  }

  /// 记录分析事件
  static void _logAnalyticsEvent(String eventName, Map<String, dynamic> parameters) {
    debugPrint('Analytics Event: $eventName, Parameters: $parameters');
  }
}

