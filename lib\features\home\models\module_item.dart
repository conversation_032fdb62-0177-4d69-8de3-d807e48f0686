import 'package:flutter/material.dart';

class ModuleItem {
  final String icon;
  final String title;
  final int badgeCount;
  final String? count;
  final String route;
  final String routeName;  // 确保有这个字段

  ModuleItem({
    required this.icon,
    required this.title,
    required this.route,
    required this.routeName,  // 确保构造函数包含这个参数
    this.badgeCount = 0,
    this.count,
  });

  factory ModuleItem.fromJson(Map<String, dynamic> json) {
    return ModuleItem(
      icon: json['icon'] ?? '',
      title: json['title'] ?? '',
      badgeCount: json['badgeCount'] ?? 0,
      count: json['count'],
      route: json['route'] ?? '',
      routeName: json['route_name'] ?? '',  // 确保从JSON中解析route_name字段
    );
  }

  /// 判断图标是否为图片路径
  bool get isImagePath => icon.startsWith('assets/');

  IconData getIconData() {
    // 如果是图片路径，返回一个默认图标（实际上不会使用）
    if (isImagePath) {
      return Icons.image;
    }

    // 否则根据图标名称返回对应的图标
    switch (icon) {
      case 'chat_bubble_outline':
        return Icons.chat_bubble_outline;
      case 'assignment_outlined':
        return Icons.assignment_outlined;
      case 'lightbulb_outline':
        return Icons.lightbulb_outline;
      case 'event_note_outlined':
        return Icons.event_note_outlined;
      case 'calendar_today_outlined':
        return Icons.calendar_today_outlined;
      case 'security_outlined':
        return Icons.security_outlined;
      case 'event_available':
        return Icons.event_available;
      case 'do_not_touch':
        return Icons.do_not_touch;
      case 'warning_outline':
      case 'warning_amber_outlined':
        return Icons.warning_amber_outlined;
      case 'add_circle_outline':
        return Icons.add_circle_outline;
      case 'location_on_outlined':
        return Icons.location_on_outlined;
      case 'work_outline':
        return Icons.work_outline;
      case 'date_range_outlined':
        return Icons.date_range_outlined;
      case 'calendar_month_outlined':
        return Icons.calendar_month_outlined;
      case 'summarize_outlined':
        return Icons.summarize_outlined;
      case 'school_outlined':
        return Icons.school_outlined;
      case 'grade_outlined':
        return Icons.grade_outlined;
      case 'health_and_safety_outlined':
        return Icons.health_and_safety_outlined;
      case 'star_outline':
        return Icons.star_outline;
      case 'chat_outlined':
        return Icons.chat_outlined;
      case 'mail_outline':
        return Icons.mail_outline;
      case 'internship_list':
      case 'list_alt_outlined':
        return Icons.list_alt_outlined;
      case 'people_outline':
        return Icons.people_outline;
      case 'transform_outlined':
        return Icons.transform_outlined;
      case 'edit_outlined':
        return Icons.edit_outlined;
      case 'notification_icon':
        return Icons.notifications_outlined;
      default:
        return Icons.circle_outlined;
    }
  }
}
