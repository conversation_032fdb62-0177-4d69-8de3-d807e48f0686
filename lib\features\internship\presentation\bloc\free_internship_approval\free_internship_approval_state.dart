/// -----
/// free_internship_approval_state.dart
///
/// 免实习申请审批BLoC状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 免实习申请审批BLoC状态基类
abstract class FreeInternshipApprovalState extends Equatable {
  const FreeInternshipApprovalState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class FreeInternshipApprovalInitial extends FreeInternshipApprovalState {}

/// 审批中状态
class FreeInternshipApprovalLoading extends FreeInternshipApprovalState {}

/// 审批成功状态
class FreeInternshipApprovalSuccess extends FreeInternshipApprovalState {
  const FreeInternshipApprovalSuccess({
    required this.message,
  });

  /// 成功消息
  final String message;

  @override
  List<Object> get props => [message];
}

/// 审批失败状态
class FreeInternshipApprovalError extends FreeInternshipApprovalState {
  const FreeInternshipApprovalError({
    required this.message,
  });

  /// 错误消息
  final String message;

  @override
  List<Object> get props => [message];
}
