/// -----
/// internship_to_job_detail_screen.dart
/// 
/// 实习转就业申请详情页面，展示已审批的申请详细信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/company_info_form.dart';
import 'package:flutter_demo/core/widgets/job_position_info.dart';

class InternshipToJobDetailScreen extends StatefulWidget {
  const InternshipToJobDetailScreen({Key? key}) : super(key: key);

  @override
  State<InternshipToJobDetailScreen> createState() => _InternshipToJobDetailScreenState();
}

class _InternshipToJobDetailScreenState extends State<InternshipToJobDetailScreen> {
  // 模拟企业信息数据
  final CompanyInfo _companyInfo = CompanyInfo(
    name: '掌淘网络科技（上海）有限公司',
    creditCode: '91310000332546552F',
    size: '100-499人',
    type: '民营（私营）企业',
    industry: '软件和信息技术服务业',
    location: '海外/其他/其他',
    address: '缅甸',
    contactPerson: '易民',
    contactPhone: '18627171276',
    email: '<EMAIL>',
    zipCode: '<EMAIL>',
    leaveReason: '更好的选择',
  );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '就业申请',
        centerTitle: true,
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户头像和姓名
            _buildUserHeader(),

            // 企业信息
            CompanyInfoForm(
              companyInfo: _companyInfo,
              onCompanyInfoChanged: (info) {
                // 仅用于查看，不需要处理更改
              },
              showLeaveReason: true,
              showVerifiedIcon: true,
            ),

            const SizedBox(height: 10),

            // 岗位信息
            _buildJobPosition(),
            
            const SizedBox(height: 10),

            // 附件照片
            _buildAttachments(),
            
            const SizedBox(height: 10),
            
            // 审批状态
            _buildApprovalStatus(),
            
            const SizedBox(height: 80),
          ],
        ),
      ),
      // 驳回按钮
      floatingActionButton: SizedBox(
        width: MediaQuery.of(context).size.width * 0.9,
        child: FloatingActionButton.extended(
          onPressed: () {
            _showRejectDialog(context);
          },
          backgroundColor: Colors.red,
          label: const Text('驳回'),
          icon: const Icon(Icons.close),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  // 用户头像和姓名
  Widget _buildUserHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: Colors.grey[300],
            child: const Text(
              '刘备',
              style: TextStyle(
                fontSize: 18,
                color: Colors.black87,
              ),
            ),
          ),
          const SizedBox(width: 16),
          const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '刘备',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建岗位信息
  Widget _buildJobPosition() {
    return JobPositionInfo(
      jobType: '普通劳动合同就业',
      jobAgreement: '普通劳动合同就业',
      department: '客服',
      position: '网络客服',
      positionCategory: '技术人员',
      isProfessionalMatch: true,
      trainingFee: '3000',
      agreementNumber: '585758',
    );
  }

  // 附件照片
  Widget _buildAttachments() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '附件照片',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Icon(
                Icons.image,
                size: 50,
                color: Colors.grey[400],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 审批状态
  Widget _buildApprovalStatus() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '审批状态',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildApprovalItem(
            '刘备老师（班主任）',
            '审批通过',
            DateTime.now().subtract(const Duration(days: 2)),
            isApproved: true,
          ),
        ],
      ),
    );
  }

  // 审批项
  Widget _buildApprovalItem(
    String name,
    String status,
    DateTime date, {
    bool isApproved = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: Colors.grey[300],
          child: const Icon(
            Icons.person,
            color: Colors.grey,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                name,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                status,
                style: TextStyle(
                  fontSize: 14,
                  color: isApproved ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                '${date.year}.${date.month.toString().padLeft(2, '0')}.${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 显示驳回对话框
  void _showRejectDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('请输入驳回理由'),
        content: TextField(
          maxLines: 3,
          decoration: InputDecoration(
            hintText: '请输入驳回理由...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // 返回上一页
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('已驳回申请'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
} 