根据实习计划ID获取实习申请列表-老师端
接口：/v1/internship/apply/teacher/list
GET
入参：planId 实习计划ID 和 type （0:待审批，1:已经审批）



返回结果：
```json
{
  "data": [
    {
      "avatar": "string",
      "companyName": "string",
      "createTime": 0,
      "id": 0,
      "jobDept": "string",
      "jobName": "string",
      "studentId": 0,
      "studentName": "string"
    }
  ],
  "resultCode": "string",
  "resultMsg": "string"
}
```
返回参数说明：
{
  avatar	string 头像
  companyName	string 企业名称
  createTime	integer($int64) 创建时间
  id	integer($int64) 实习申请ID
  jobDept	string 岗位部门
  jobName	string 岗位名称
  studentId	integer($int64) 学生ID
  studentName	string 学生姓名
}