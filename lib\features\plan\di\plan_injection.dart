/// -----
/// plan_injection.dart
///
/// 实习计划模块依赖注入
/// 注册实习计划模块的数据源、仓库、用例和BLoC
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// 第三方库
import 'package:get_it/get_it.dart';

// 项目内部库
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/features/plan/data/datasources/exemption_application_data_source.dart';
import 'package:flutter_demo/features/plan/data/datasources/remote_exemption_application_data_source.dart';
import 'package:flutter_demo/features/plan/data/repositories/exemption_application_repository_impl.dart';
import 'package:flutter_demo/features/plan/domain/repositories/exemption_application_repository.dart';
import 'package:flutter_demo/features/plan/domain/usecases/submit_exemption_application_usecase.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/exemption_application_bloc.dart';

/// 全局依赖注入容器
final getIt = GetIt.instance;

/// 初始化实习计划模块依赖
///
/// 注册实习计划模块的数据源、仓库、用例和BLoC
Future<void> setupPlanDependencies() async {
  // 数据源
  getIt.registerLazySingleton<ExemptionApplicationDataSource>(
    () => RemoteExemptionApplicationDataSource(
      dioClient: getIt<DioClient>(),
    ),
  );

  // 仓库
  getIt.registerLazySingleton<ExemptionApplicationRepository>(
    () => ExemptionApplicationRepositoryImpl(
      dataSource: getIt<ExemptionApplicationDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
    ),
  );

  // 用例
  getIt.registerLazySingleton<SubmitExemptionApplicationUseCase>(
    () => SubmitExemptionApplicationUseCase(
      repository: getIt<ExemptionApplicationRepository>(),
    ),
  );

  // BLoC - 使用工厂模式，每次使用时创建新实例
  getIt.registerFactory<ExemptionApplicationBloc>(
    () => ExemptionApplicationBloc(
      submitExemptionApplicationUseCase: getIt<SubmitExemptionApplicationUseCase>(),
    ),
  );
}
