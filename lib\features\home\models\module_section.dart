import 'package:flutter_demo/features/home/<USER>/module_item.dart';

class ModuleSection {
  final String title;
  final List<ModuleItem> items;

  ModuleSection({
    required this.title,
    required this.items,
  });

  factory ModuleSection.fromJson(Map<String, dynamic> json) {
    final List<ModuleItem> moduleItems = (json['items'] as List)
        .map((itemJson) => ModuleItem.fromJson(itemJson))
        .toList();

    return ModuleSection(
      title: json['title'] ?? '',
      items: moduleItems,
    );
  }
}
