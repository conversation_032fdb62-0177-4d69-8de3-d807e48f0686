/// -----
/// free_internship_application_item.dart
///
/// 免实习申请项组件，用于在列表中展示免实习申请项
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/data/models/free_internship_application_model.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class FreeInternshipApplicationItem extends StatelessWidget {
  /// 申请数据
  final FreeInternshipApplicationModel item;

  /// 是否为待审批状态
  final bool isPending;

  /// 点击回调
  final VoidCallback? onTap;

  /// 查看按钮点击回调
  final VoidCallback? onViewTap;

  /// 附件点击回调
  final Function(AttachmentInfo)? onAttachmentTap;

  const FreeInternshipApplicationItem({
    Key? key,
    required this.item,
    this.isPending = false,
    this.onTap,
    this.onViewTap,
    this.onAttachmentTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final status = item.status;
    final statusColor = _getStatusColor(status);

    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 学生信息行
              Row(
                children: [
                  // 头像
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey[300],
                    ),
                    child: ClipOval(
                      child: item.studentAvatar.isNotEmpty
                          ? Image.network(
                              item.studentAvatar,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  SvgPicture.asset(
                                'assets/images/default_avatar.svg',
                                fit: BoxFit.cover,
                              ),
                            )
                          : SvgPicture.asset(
                              'assets/images/default_avatar.svg',
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 学生姓名
                  Text(
                    item.studentName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  // 状态标签
                  Text(
                    isPending ? '待审批' : status,
                    style: TextStyle(
                      fontSize: 14,
                      color: isPending ? AppTheme.primaryColor : statusColor,
                    ),
                  ),
                ],
              ),

              // 添加分割线
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
              ),

              // 学生去向
              _buildInfoRow('学生去向', item.studentDestination),

              const SizedBox(height: 8),

              // 证明文件
              _buildAttachmentRow('证明文件', item.attachments),

              // 添加分割线
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
              ),

              // 申请时间和查看按钮
              Row(
                children: [
                  Text(
                    '申请时间：${item.applyDate}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  // 查看按钮
                  InkWell(
                    onTap: onViewTap,
                    child: const Row(
                      children: [
                        Text(
                          '查看',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: AppTheme.primaryColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // 附件行
  Widget _buildAttachmentRow(String label, List<AttachmentInfo> attachments) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: attachments.isNotEmpty
              ? Align(
                  alignment: Alignment.centerLeft,
                  child: InkWell(
                    onTap: () {
                      if (onAttachmentTap != null) {
                        onAttachmentTap!(attachments.first);
                      }
                    },
                    child: Container(
                      width: 98,
                      height: 98,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8), // 使用更明显的圆角
                        border: Border.all(color: Colors.grey[300]!, width: 1), // 添加边框使边界更清晰
                      ),
                      clipBehavior: Clip.antiAlias, // 使用antiAlias获得更平滑的圆角
                      child: Image.asset(
                        'assets/images/certificate_sample.png',
                        width: 98,
                        height: 98,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Image.network(
                          'https://pic1.zhimg.com/v2-a58fa2ab84be291418da2652805f8270_b.jpg',
                          width: 98,
                          height: 98,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Container(
                            width: 98,
                            height: 98,
                            color: Colors.grey[200],
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.insert_drive_file,
                                    size: 36,
                                    color: Colors.grey,
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    attachments.first.name,
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: AppTheme.primaryColor,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              : const Text(
                  '无',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
        ),
      ],
    );
  }

  // 根据状态获取颜色
  Color _getStatusColor(String status) {
    switch (status) {
      case '待审批':
        return AppTheme.primaryColor;
      case '已通过':
        return Colors.grey;
      case '已驳回':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
