/// -----
/// checkin_warning_screen.dart
/// 
/// 签到预警页面，用于显示学生签到异常记录列表
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 签到异常类型枚举
enum CheckinAnomalyType {
  /// 跨省
  crossProvince,
  /// 跨市
  crossCity,
  /// 跨区
  crossDistrict,
}

/// 签到异常记录模型
class CheckinAnomalyRecord {
  final String studentName;
  final String phoneNumber;
  final String checkinTime;
  final String checkinLocation;
  final CheckinAnomalyType anomalyType;
  final String avatarUrl;

  const CheckinAnomalyRecord({
    required this.studentName,
    required this.phoneNumber,
    required this.checkinTime,
    required this.checkinLocation,
    required this.anomalyType,
    this.avatarUrl = '',
  });
}

class CheckinWarningScreen extends StatelessWidget {
  const CheckinWarningScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 模拟数据
    final List<CheckinAnomalyRecord> anomalyRecords = [
      const CheckinAnomalyRecord(
        studentName: '李成儒',
        phoneNumber: '13569874562',
        checkinTime: '2025-06-11  20:01',
        checkinLocation: '江西省九江市黄石港区黄石大道58号',
        anomalyType: CheckinAnomalyType.crossProvince,
        avatarUrl: AppConstants.avatar1,
      ),
      const CheckinAnomalyRecord(
        studentName: '张牧之',
        phoneNumber: '13569874562',
        checkinTime: '2025-06-11  20:01',
        checkinLocation: '湖北省鄂州市黄石港区黄石大道58...',
        anomalyType: CheckinAnomalyType.crossCity,
        avatarUrl: AppConstants.avatar2,
      ),
      const CheckinAnomalyRecord(
        studentName: '刘玄德',
        phoneNumber: '13569874562',
        checkinTime: '2025-06-11  20:01',
        checkinLocation: '湖北省黄石市黄石港区黄石大道58号',
        anomalyType: CheckinAnomalyType.crossDistrict,
        avatarUrl: AppConstants.avatar3, // 空URL，将显示默认头像
      ),
    ];

    return Scaffold(
      backgroundColor: const Color(0xFFF8F8F8),
      appBar: const CustomAppBar(
        title: '签到预警',
        centerTitle: true,
        showBackButton: true,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 统计信息
          _buildStatisticsSection(anomalyRecords.length),
          
          // 异常记录列表
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 25.w),
              itemCount: anomalyRecords.length,
              itemBuilder: (context, index) {
                return _buildAnomalyRecordItem(anomalyRecords[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计信息区域
  Widget _buildStatisticsSection(int count) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
      child: Text(
        '共${count}条异常记录',
        style: TextStyle(
          fontSize: 28.sp,
          color: const Color(0xFF666666),
        ),
      ),
    );
  }

  /// 构建异常记录列表项
  Widget _buildAnomalyRecordItem(CheckinAnomalyRecord record) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      padding: EdgeInsets.all(30.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧头像
          _buildAvatar(record.studentName, avatarUrl: record.avatarUrl),
          
          SizedBox(width: 24.w),
          
          // 中间信息
          Expanded(
            child: _buildStudentInfo(record),
          ),
          
          SizedBox(width: 20.w),
          
          // 右侧状态标签
          _buildAnomalyTag(record.anomalyType),
        ],
      ),
    );
  }

  /// 构建头像
  Widget _buildAvatar(String studentName, {String? avatarUrl}) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2165F6),
        shape: BoxShape.circle,
      ),
      child: ClipOval(
        child: avatarUrl != null && avatarUrl.isNotEmpty
            ? Image.network(
                avatarUrl,
                width: 88.w,
                height: 88.w,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  // 网络图片加载失败时显示默认头像
                  return _buildDefaultAvatar(studentName);
                },
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) {
                    return child;
                  }
                  // 加载中显示默认头像
                  return _buildDefaultAvatar(studentName);
                },
              )
            : _buildDefaultAvatar(studentName),
      ),
    );
  }

  /// 构建默认头像（显示姓名首字母）
  Widget _buildDefaultAvatar(String studentName) {
    return Container(
      width: 88.w,
      height: 88.w,
      decoration: const BoxDecoration(
        color: Color(0xFF2165F6),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          studentName.isNotEmpty ? studentName.substring(0, 1) : '',
          style: TextStyle(
            fontSize: 32.sp,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// 构建学生信息
  Widget _buildStudentInfo(CheckinAnomalyRecord record) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 姓名和电话
        Row(
          children: [
            Text(
              record.studentName,
              style: TextStyle(
                fontSize: 32.sp,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF333333),
              ),
            ),
            SizedBox(width: 20.w),
            Icon(
              Icons.phone,
              size: 28.w,
              color: const Color(0xFF666666),
            ),
            SizedBox(width: 8.w),
            Text(
              record.phoneNumber,
              style: TextStyle(
                fontSize: 28.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ],
        ),
        
        SizedBox(height: 16.h),
        
        // 签到时间
        Text(
          '签到时间：${record.checkinTime}',
          style: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFF999999),
          ),
        ),
        
        SizedBox(height: 12.h),
        
        // 签到地点
        Text(
          '签到地点：${record.checkinLocation}',
          style: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFF999999),
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  /// 构建异常类型标签
  Widget _buildAnomalyTag(CheckinAnomalyType type) {
    String text;
    Color backgroundColor;
    
    switch (type) {
      case CheckinAnomalyType.crossProvince:
        text = '跨省';
        backgroundColor = const Color(0xFFFF4747);
        break;
      case CheckinAnomalyType.crossCity:
        text = '跨市';
        backgroundColor = const Color(0xFFFF8A00);
        break;
      case CheckinAnomalyType.crossDistrict:
        text = '跨区';
        backgroundColor = const Color(0xFFFFB800);
        break;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 24.sp,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
