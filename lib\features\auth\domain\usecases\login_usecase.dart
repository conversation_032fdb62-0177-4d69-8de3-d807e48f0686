import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// 登录用例
///
/// 处理用户登录的业务逻辑
class LoginUseCase {
  final AuthRepository _repository;

  LoginUseCase(this._repository);

  /// 调用登录用例
  ///
  /// 参数：[params] 登录参数
  /// 返回：Either<Failure, User>，表示登录成功或失败
  Future<Either<Failure, User>> call(LoginParams params) async {
    return await _repository.login(
      phone: params.phone,
      password: params.password,
    );
  }
}

/// 登录参数
///
/// 包含登录所需的参数
class LoginParams extends Equatable {
  final String phone;
  final String password;

  const LoginParams({
    required this.phone,
    required this.password,
  });

  @override
  List<Object> get props => [phone, password];
}
