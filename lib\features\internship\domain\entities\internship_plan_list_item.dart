/// -----------------------------------------------------------------------------
/// internship_plan_list_item.dart
///
/// 实习计划列表项实体
/// 用于表示实习计划列表中的单个项目
///
/// <AUTHOR>
/// @date 2025-06-16
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:equatable/equatable.dart';

/// 实习计划列表项实体
/// 
/// 表示实习计划列表中的单个项目，包含基本信息
class InternshipPlanListItem extends Equatable {
  /// 实习计划ID
  final String planId;
  
  /// 实习计划名称
  final String planName;
  
  /// 计划编号
  final String planCode;
  
  /// 学年，例如：2024-2025
  final String academicYear;
  
  /// 实习年级，例如：2025
  final String grade;
  
  /// 类型（1=岗位实习, 2=认识实习, 3=其他实习, 4=学徒制, 5=综合实训, 6=工学交替）
  final int planType;
  
  /// 实习形式（1=校外实习, 2=校内真实职业场景, 3=虚拟仿真）
  final int practiceMode;
  
  /// 学期，例如：2025-2026实习学年第一学期/第二学期
  final String semester;
  
  /// 学生ID
  final String studentId;
  
  /// 创建时间（时间戳）
  final int createTime;
  
  /// 状态（0=待启用，1=进行中, 2=已结束）
  final int planStatus;

  const InternshipPlanListItem({
    required this.planId,
    required this.planName,
    required this.planCode,
    required this.academicYear,
    required this.grade,
    required this.planType,
    required this.practiceMode,
    required this.semester,
    required this.studentId,
    required this.createTime,
    required this.planStatus,
  });

  @override
  List<Object?> get props => [
    planId,
    planName,
    planCode,
    academicYear,
    grade,
    planType,
    practiceMode,
    semester,
    studentId,
    createTime,
    planStatus,
  ];

  /// 获取状态文本
  String get statusText {
    switch (planStatus) {
      case 0:
        return '待启用';
      case 1:
        return '进行中';
      case 2:
        return '已结束';
      default:
        return '未知';
    }
  }

  /// 获取类型文本
  String get typeText {
    switch (planType) {
      case 1:
        return '岗位实习';
      case 2:
        return '认识实习';
      case 3:
        return '其他实习';
      case 4:
        return '学徒制';
      case 5:
        return '综合实训';
      case 6:
        return '工学交替';
      default:
        return '未知类型';
    }
  }

  /// 获取实习形式文本
  String get practiceModeText {
    switch (practiceMode) {
      case 1:
        return '校外实习';
      case 2:
        return '校内真实职业场景';
      case 3:
        return '虚拟仿真';
      default:
        return '未知形式';
    }
  }

  /// 获取显示名称（用于UI显示）
  String get displayName => planName;

  /// 获取完整描述
  String get fullDescription => '$planName ($semester)';
}
