import requests
import json

url = "http://121.196.223.94:8088/userservice/v1/user/login"
headers = {
    "Content-Type": "application/json"  # 声明发送 JSON 数据
}
data = {
    "password": "abc123",
    "phone": "13777777777"
}

try:
    response = requests.post(url, json=data, headers=headers)  # 直接使用 `json=` 参数
    response.raise_for_status()
    
    # 获取 JSON 数据并美化输出
    json_data = response.json()
    pretty_json = json.dumps(json_data, indent=4, ensure_ascii=False)  # 缩进4个空格，支持中文
    
    print("请求成功！")
    print("状态码:", response.status_code)
    print("美化后的 JSON 响应:")
    print(pretty_json)
except requests.exceptions.RequestException as e:
    print("请求失败:", e)