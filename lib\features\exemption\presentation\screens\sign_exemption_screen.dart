/// -----
/// sign_exemption_screen.dart
/// 
/// 免签申请页面，用于申请和查看实习签到免签信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/features/exemption/presentation/screens/exemption_approval_screen.dart';
import 'package:flutter_demo/features/exemption/presentation/screens/exemption_detail_screen.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';

class SignExemptionScreen extends StatefulWidget {
  const SignExemptionScreen({Key? key}) : super(key: key);

  @override
  State<SignExemptionScreen> createState() => _SignExemptionScreenState();
}

class _SignExemptionScreenState extends State<SignExemptionScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 假设待审批数量为4
    final int pendingCount = 4;

    return Scaffold(
      appBar: const CustomAppBar(
        title: '免签申请',
        centerTitle: true,
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 课程头部
          const CourseHeaderSection(
            courseName: '2021级市场销售2023-2024实习学年第二学期岗位实习',
            initialExpanded: false,
          ),
          // 自定义标签栏
          ApprovalTabBar(
            controller: _tabController,
            pendingCount: pendingCount,
            pendingText: '待审批',
            approvedText: '已审批',
          ),
          // Tab内容
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPendingExemptionList(),
                _buildApprovedExemptionList(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showExemptionApplicationForm(context);
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  // 列表Item参照审批列表样式
  Widget _buildExemptionListItem(Map<String, String> data, {bool isApproved = false, VoidCallback? onTap}) {
    final status = data['status'] ?? '';
    Color statusColor;
    switch (status) {
      case '待审批':
        statusColor = Colors.grey;
        break;
      case '已审批':
        statusColor = Colors.green;
        break;
      case '已驳回':
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }

    // 假设有头像字段，否则用默认头像
    final avatarUrl = data['avatar'] ?? '';
    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 头像+姓名+状态
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey[300],
                    ),
                    child: ClipOval(
                      child: avatarUrl.isNotEmpty
                          ? Image.network(
                              avatarUrl,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  Image.asset(
                                    'assets/images/default_avatar.png',
                                    fit: BoxFit.cover,
                                  ),
                            )
                          : Image.asset(
                              'assets/images/default_avatar.png',
                              fit: BoxFit.cover,
                            ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    data['name'] ?? '',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    status,
                    style: TextStyle(
                      fontSize: 14,
                      color: statusColor,
                    ),
                  ),
                ],
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
              ),
              _buildInfoRow('实习单位', data['company'] ?? ''),
              const SizedBox(height: 8),
              _buildInfoRow('部门/科室', data['department'] ?? ''),
              const SizedBox(height: 8),
              _buildInfoRow('起止时间', '${data['startDate'] ?? ''}-${data['endDate'] ?? ''} ${data['days'] ?? ''}'),
              const SizedBox(height: 8),
              _buildInfoRow('免签原因', data['reason'] ?? ''),
              const SizedBox(height: 16),
              Row(
                children: [
                  Text(
                    data['time'] ?? '',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  InkWell(
                    onTap: onTap,
                    child: const Row(
                      children: [
                        Text(
                          '查看',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: AppTheme.primaryColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPendingExemptionList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: 4,
      itemBuilder: (context, index) {
        final exemptionData = {
          'name': '刘备',
          'company': '深圳市腾讯计算机系统有限责任公司',
          'department': '技术部',
          'startDate': '2025.03.15',
          'endDate': '2025.03.18',
          'days': '共5天',
          'reason': '人安排出差在外地，手机无信号',
          'status': '待审批',
          'time': '2025.03.12 16:20',
          'avatar': AppConstants.avatar1
        };
        return _buildExemptionListItem(
          exemptionData,
          isApproved: false,
          onTap: () => _navigateToApprovalScreen(exemptionData),
        );
      },
    );
  }

  Widget _buildApprovedExemptionList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: 2,
      itemBuilder: (context, index) {
        final exemptionData = {
          'name': '刘备',
          'company': '深圳市腾讯计算机系统有限责任公司',
          'department': '技术部',
          'startDate': '2025.03.10',
          'endDate': '2025.03.12',
          'days': '共3天',
          'reason': '人安排出差在外地，手机无信号',
          'status': '已审批',
          'time': '2025.03.09 14:30',
          'avatar': AppConstants.avatar2
        };
        return _buildExemptionListItem(
          exemptionData,
          isApproved: true,
          onTap: () => _navigateToDetailScreen(exemptionData),
        );
      },
    );
  }

  // 跳转到审批页面
  void _navigateToApprovalScreen(Map<String, String> exemptionData) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExemptionApprovalScreen(
          name: exemptionData['name']!,
          company: exemptionData['company']!,
          department: exemptionData['department']!,
          startDate: exemptionData['startDate']!,
          endDate: exemptionData['endDate']!,
          days: exemptionData['days']!,
          reason: exemptionData['reason']!,
        ),
      ),
    );
    
    // 处理审批结果
    if (result != null) {
      final approved = result['approved'] as bool;
      if (approved) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('免签申请已通过')),
        );
      } else {
        final reason = result['reason'] as String;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('免签申请已驳回: $reason')),
        );
      }
    }
  }

  // 跳转到已审批详情页面
  void _navigateToDetailScreen(Map<String, String> exemptionData) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ExemptionDetailScreen(
          name: exemptionData['name']!,
          company: exemptionData['company']!,
          department: exemptionData['department']!,
          startDate: exemptionData['startDate']!,
          endDate: exemptionData['endDate']!,
          days: exemptionData['days']!,
          reason: exemptionData['reason']!,
          isApproved: true, // 默认已通过
          approverName: '刘备老师',
          approverRole: '班主任',
        ),
      ),
    );
  }

  void _showExemptionApplicationForm(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom,
          ),
          child: const Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '免签申请',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 16),
                // 这里可以添加免签表单内容
                Text('免签类型选择、日期选择和理由填写等表单内容将在这里实现'),
                SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }
} 