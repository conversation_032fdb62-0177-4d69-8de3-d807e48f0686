/// -----
/// free_internship_exempt_remote_data_source.dart
///
/// 免实习申请远程数据源接口
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../data/models/free_internship_exempt_model.dart';
import '../../../data/models/free_internship_approval_request_model.dart';

/// 免实习申请远程数据源接口
///
/// 定义从远程API获取免实习申请数据的方法
abstract class FreeInternshipExemptRemoteDataSource {
  /// 获取免实习申请列表
  ///
  /// [planId] 实习计划ID
  /// [type] 申请类型（0:待审批，1:已审批）
  ///
  /// 返回免实习申请列表
  Future<List<FreeInternshipExemptModel>> getFreeInternshipExemptList({
    required int planId,
    required int type,
  });

  /// 审批免实习申请
  ///
  /// [request] 审批请求数据
  ///
  /// 返回审批结果
  Future<void> approveFreeInternshipExempt(FreeInternshipApprovalRequestModel request);
}
