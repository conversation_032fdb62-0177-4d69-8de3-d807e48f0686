/// -----
/// file_detail_model.dart
/// 
/// 文件详情数据模型，用于API响应的数据映射
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 文件详情数据模型
/// 
/// 用于 /v1/internship/student/file/require/detail 接口响应的数据映射
class FileDetailModel {
  /// 文件ID
  final String id;
  
  /// 计划ID
  final String planId;
  
  /// 学生ID
  final String studentId;
  
  /// 文件名称
  final String fileName;
  
  /// 文件类型
  final String fileType;
  
  /// 文件代码
  final int fileCode;
  
  /// 文件URL
  final String fileUrl;
  
  /// 文件状态
  final int fileStatus;
  
  /// 审批人姓名
  final String? approveName;
  
  /// 教师ID
  final String? teacherId;
  
  /// 审批角色
  final String? approveRole;
  
  /// 审批角色名称
  final String? approveRoleName;
  
  /// 备注
  final String? remark;
  
  /// 创建人
  final String createPerson;
  
  /// 创建时间
  final int createTime;
  
  /// 更新人
  final String? updatePerson;
  
  /// 更新时间
  final int? updateTime;

  const FileDetailModel({
    required this.id,
    required this.planId,
    required this.studentId,
    required this.fileName,
    required this.fileType,
    required this.fileCode,
    required this.fileUrl,
    required this.fileStatus,
    this.approveName,
    this.teacherId,
    this.approveRole,
    this.approveRoleName,
    this.remark,
    required this.createPerson,
    required this.createTime,
    this.updatePerson,
    this.updateTime,
  });

  /// 从JSON创建FileDetailModel
  factory FileDetailModel.fromJson(Map<String, dynamic> json) {
    return FileDetailModel(
      id: json['id']?.toString() ?? '',
      planId: json['planId']?.toString() ?? '',
      studentId: json['studentId']?.toString() ?? '',
      fileName: json['fileName']?.toString() ?? '',
      fileType: json['fileType']?.toString() ?? '',
      fileCode: json['fileCode'] ?? 0,
      fileUrl: json['fileUrl']?.toString() ?? '',
      fileStatus: json['fileStatus'] ?? 0,
      approveName: json['approveName']?.toString(),
      teacherId: json['teacherId']?.toString(),
      approveRole: json['approveRole']?.toString(),
      approveRoleName: json['approveRoleName']?.toString(),
      remark: json['remark']?.toString(),
      createPerson: json['createPerson']?.toString() ?? '',
      createTime: json['createTime'] ?? 0,
      updatePerson: json['updatePerson']?.toString(),
      updateTime: json['updateTime'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'planId': planId,
      'studentId': studentId,
      'fileName': fileName,
      'fileType': fileType,
      'fileCode': fileCode,
      'fileUrl': fileUrl,
      'fileStatus': fileStatus,
      'approveName': approveName,
      'teacherId': teacherId,
      'approveRole': approveRole,
      'approveRoleName': approveRoleName,
      'remark': remark,
      'createPerson': createPerson,
      'createTime': createTime,
      'updatePerson': updatePerson,
      'updateTime': updateTime,
    };
  }

  /// 获取格式化的创建时间
  DateTime get createDateTime => DateTime.fromMillisecondsSinceEpoch(createTime);
  
  /// 获取格式化的更新时间
  DateTime? get updateDateTime => updateTime != null 
      ? DateTime.fromMillisecondsSinceEpoch(updateTime!) 
      : null;

  /// 获取审批状态文本
  String get approvalStatusText {
    switch (fileStatus) {
      case 0:
        return '未上传';
      case 1:
        return '已上传';
      case 2:
        return '已审核';
      case 3:
        return '已驳回';
      default:
        return '未知状态';
    }
  }

  @override
  String toString() {
    return 'FileDetailModel(id: $id, fileName: $fileName, fileType: $fileType, fileStatus: $fileStatus)';
  }
}
