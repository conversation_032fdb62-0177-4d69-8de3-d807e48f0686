/// -----
/// leave_approval_screen.dart
/// 
/// 请假审批页面，用于审批待处理的请假申请
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';

class LeaveApprovalScreen extends StatefulWidget {
  // final String name;
  // final String leaveType;
  // final String startDate;
  // final String endDate;
  // final String days;
  // final String reason;

  const LeaveApprovalScreen({
    Key? key,
    // required this.name,
    // required this.leaveType,
    // required this.startDate,
    // required this.endDate,
    // required this.days,
    // required this.reason,
  }) : super(key: key);

  @override
  State<LeaveApprovalScreen> createState() => _LeaveApprovalScreenState();
}

class _LeaveApprovalScreenState extends State<LeaveApprovalScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final int pendingCount = 2; // 示例
    return Scaffold(
      appBar: const CustomAppBar(
        title: '请假申请',
        centerTitle: true,
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 课程头部
          const CourseHeaderSection(
            courseName: '2021级市场销售2023-2024实习学年第二学期岗位实习',
            initialExpanded: false,
          ),
          // 自定义标签栏
          ApprovalTabBar(
            controller: _tabController,
            pendingCount: pendingCount,
            pendingText: '待审批',
            approvedText: '已审批',
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildLeaveList(isPending: true),
                _buildLeaveList(isPending: false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeaveList({required bool isPending}) {
    // 示例数据
    final List<Map<String, String>> leaveList = [
      {
        'name': '刘备',
        'avatar': AppConstants.avatar1,
        'leaveType': '事假',
        'startDate': '2025.03.10',
        'endDate': '2025.03.12',
        'days': '2天',
        'reason': '家中有事',
        'status': isPending ? '待审批' : '已审批',
        'time': '2025.03.09 14:30',
      },
      {
        'name': '关羽',
        'avatar': AppConstants.avatar2,
        'leaveType': '病假',
        'startDate': '2025.03.15',
        'endDate': '2025.03.16',
        'days': '1天',
        'reason': '感冒发烧',
        'status': isPending ? '待审批' : '已审批',
        'time': '2025.03.14 10:20',
      },
    ];
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: leaveList.length,
      itemBuilder: (context, index) {
        final data = leaveList[index];
        return _buildLeaveListItem(data);
      },
    );
  }

  Widget _buildLeaveListItem(Map<String, String> data) {
    final status = data['status'] ?? '';
    Color statusColor;
    switch (status) {
      case '待审批':
        statusColor = Colors.grey;
        break;
      case '已审批':
        statusColor = Colors.green;
        break;
      case '已驳回':
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }
    final avatarUrl = data['avatar'] ?? '';
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[300],
                  ),
                  child: ClipOval(
                    child: avatarUrl.isNotEmpty
                        ? Image.network(
                            avatarUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                Image.asset(
                                  'assets/images/default_avatar.png',
                                  fit: BoxFit.cover,
                                ),
                          )
                        : Image.asset(
                            'assets/images/default_avatar.png',
                            fit: BoxFit.cover,
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  data['name'] ?? '',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  status,
                  style: TextStyle(
                    fontSize: 14,
                    color: statusColor,
                  ),
                ),
              ],
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
            ),
            _buildInfoRow('请假类型', data['leaveType'] ?? ''),
            const SizedBox(height: 8),
            _buildInfoRow('起止时间', '${data['startDate'] ?? ''}-${data['endDate'] ?? ''} ${data['days'] ?? ''}'),
            const SizedBox(height: 8),
            _buildInfoRow('请假理由', data['reason'] ?? ''),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  data['time'] ?? '',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                InkWell(
                  onTap: () {
                    // TODO: 跳转到详情或审批页面
                  },
                  child: const Row(
                    children: [
                      Text(
                        '查看',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppTheme.primaryColor,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }
} 