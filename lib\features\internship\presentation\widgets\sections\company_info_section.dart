/// -----
/// company_info_section.dart
///
/// 企业信息部分组件
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/data/models/company_info.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_event.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/text_field.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/dropdown_field.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/location_field.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 企业信息部分组件
class CompanyInfoSection extends StatelessWidget {
  /// 企业信息
  final CompanyInfo companyInfo;

  const CompanyInfoSection({
    super.key,
    required this.companyInfo,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Text(
            '企业信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
        ),

        // 企业名称
        TextInputField(
          label: '企业名称',
          value: companyInfo.name,
          placeholder: '请输入企业名称',
          onChanged: (value) => _updateCompanyInfo(context, 'name', value),
        ),

        // 统一社会信用代码
        TextInputField(
          label: '统一社会信用代码',
          value: companyInfo.creditCode,
          placeholder: '请输入统一社会信用代码',
          onChanged: (value) => _updateCompanyInfo(context, 'creditCode', value),
        ),

        // 企业规模
        DropdownField(
          label: '企业规模',
          value: companyInfo.size,
          items: const [
            '50人以下',
            '50-200人',
            '200-500人',
            '500-1000人',
            '1000人以上'
          ],
          placeholder: '请选择企业规模',
          onChanged: (value) => _updateCompanyInfo(context, 'size', value),
        ),

        // 企业性质
        DropdownField(
          label: '企业性质',
          value: companyInfo.type,
          items: const ['国有企业', '民营企业', '外资企业', '合资企业', '事业单位', '其他'],
          placeholder: '请选择企业性质',
          onChanged: (value) => _updateCompanyInfo(context, 'type', value),
        ),

        // 所属行业
        DropdownField(
          label: '所属行业',
          value: companyInfo.industry,
          items: const ['IT/互联网', '金融', '教育', '医疗', '制造业', '服务业', '其他'],
          placeholder: '请选择所属行业',
          onChanged: (value) => _updateCompanyInfo(context, 'industry', value),
        ),

        // 企业所在地
        LocationField(
          label: '企业所在地',
          value: companyInfo.location,
          placeholder: '请选择省市区',
          onChanged: (value) => context.read<InternshipApplicationBloc>().add(
            SelectLocationEvent('companyLocation', value),
          ),
        ),

        // 详细地址
        TextInputField(
          label: '详细地址',
          value: companyInfo.address,
          placeholder: '请输入详细地址',
          onChanged: (value) => _updateCompanyInfo(context, 'address', value),
        ),

        // 联系人
        TextInputField(
          label: '联系人',
          value: companyInfo.contactPerson,
          placeholder: '请输入联系人',
          onChanged: (value) => _updateCompanyInfo(context, 'contactPerson', value),
        ),

        // 联系人电话
        TextInputField(
          label: '联系人电话',
          value: companyInfo.contactPhone,
          placeholder: '请输入联系人电话',
          keyboardType: TextInputType.phone,
          onChanged: (value) => _updateCompanyInfo(context, 'contactPhone', value),
        ),

        // 电子邮箱
        TextInputField(
          label: '电子邮箱',
          value: companyInfo.email,
          placeholder: '请输入电子邮箱',
          keyboardType: TextInputType.emailAddress,
          showDivider: false,
          onChanged: (value) => _updateCompanyInfo(context, 'email', value),
        ),
      ],
    );
  }

  /// 更新企业信息
  void _updateCompanyInfo(BuildContext context, String field, String value) {
    context.read<InternshipApplicationBloc>().add(
      UpdateCompanyInfoEvent(field, value),
    );
  }
}
