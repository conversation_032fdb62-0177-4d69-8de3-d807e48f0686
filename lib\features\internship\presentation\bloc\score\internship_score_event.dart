/// -----
/// internship_score_event.dart
/// 
/// 实习成绩事件类，定义与实习成绩相关的所有事件
///
/// <AUTHOR>
/// @date 2025-05-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习成绩事件基类
abstract class InternshipScoreEvent extends Equatable {
  const InternshipScoreEvent();

  @override
  List<Object> get props => [];
}

/// 加载实习成绩列表事件
class LoadInternshipScoreEvent extends InternshipScoreEvent {
  /// 课程ID
  final String courseId;
  
  /// 是否强制刷新
  final bool forceRefresh;

  const LoadInternshipScoreEvent({
    required this.courseId,
    this.forceRefresh = false,
  });

  @override
  List<Object> get props => [courseId, forceRefresh];
}

/// 切换标签页事件
class SwitchInternshipScoreTabEvent extends InternshipScoreEvent {
  /// 标签索引，0表示待评分，1表示已评分
  final int tabIndex;

  const SwitchInternshipScoreTabEvent({required this.tabIndex});

  @override
  List<Object> get props => [tabIndex];
}

/// 评分事件
class RateInternshipScoreEvent extends InternshipScoreEvent {
  /// 学生ID
  final String studentId;
  
  /// 课程ID
  final String courseId;
  
  /// 分数
  final int score;

  const RateInternshipScoreEvent({
    required this.studentId,
    required this.courseId,
    required this.score,
  });

  @override
  List<Object> get props => [studentId, courseId, score];
}

/// 切换课程事件
class ChangeCourseEvent extends InternshipScoreEvent {
  /// 新的课程ID
  final String courseId;
  
  /// 新的课程名称
  final String courseName;

  const ChangeCourseEvent({
    required this.courseId,
    required this.courseName,
  });

  @override
  List<Object> get props => [courseId, courseName];
}
