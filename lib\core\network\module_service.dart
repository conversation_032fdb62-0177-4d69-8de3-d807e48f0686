import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter_demo/features/home/<USER>/module_section.dart';
import 'package:flutter_demo/features/auth/data/models/role_type.dart';

class ModuleService {
  /// 根据角色加载对应的模块数据
  ///
  /// [roleType] 用户角色类型，默认为学生角色
  Future<Map<String, ModuleSection>> loadModules({RoleType roleType = RoleType.student}) async {
    try {
      // 根据角色类型选择不同的JSON文件
      String jsonPath;
      switch (roleType) {
        case RoleType.student:
          jsonPath = 'assets/data/student_modules.json';
          break;
        case RoleType.teacher:
          jsonPath = 'assets/data/teacher_modules.json';
          break;
        case RoleType.hr:
          jsonPath = 'assets/data/hr_modules.json';
          break;
      }

      // 加载JSON文件
      final String response = await rootBundle.loadString(jsonPath);
      final Map<String, dynamic> jsonData = json.decode(response);

      // 解析JSON数据
      Map<String, ModuleSection> sections = {};

      jsonData.forEach((key, value) {
        sections[key] = ModuleSection.fromJson(value);
      });

      return sections;
    } catch (e) {
      print('Error loading modules: $e');
      return {};
    }
  }
}