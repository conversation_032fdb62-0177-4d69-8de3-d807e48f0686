/// -----
/// file_upload_item.dart
/// 
/// 文件上传项数据模型，定义文件上传的状态和相关信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';

/// 文件上传状态枚举
enum FileUploadStatus {
  /// 未上传
  notUploaded,
  /// 已上传（审核中）
  uploaded,
  /// 审核通过
  approved,
  /// 审核未通过（已驳回）
  rejected,
}

/// 审批人信息
class ApprovalInfo {
  /// 审批人姓名
  final String name;
  /// 审批人职位
  final String position;
  /// 审批人头像
  final String avatar;
  /// 审批状态
  final FileUploadStatus status;
  /// 审批意见/驳回原因
  final String? comment;

  const ApprovalInfo({
    required this.name,
    required this.position,
    required this.avatar,
    required this.status,
    this.comment,
  });
}

/// 上传文件信息
class UploadedFile {
  /// 文件名
  final String fileName;
  /// 文件路径/URL
  final String filePath;
  /// 文件预览图
  final String? previewImage;
  /// 上传时间
  final DateTime uploadTime;

  const UploadedFile({
    required this.fileName,
    required this.filePath,
    this.previewImage,
    required this.uploadTime,
  });
}

/// 文件上传项模型
class FileUploadItem {
  /// 文件类型ID
  final String id;
  /// 文件类型名称
  final String fileName;
  /// 文件类型图标
  final String iconData;
  /// 图标颜色
  final Color iconColor;
  /// 上传状态
  final FileUploadStatus status;
  /// 状态背景色
  final Color statusColor;
  /// 状态文字颜色
  final Color statusTextColor;
  /// 模板下载链接
  final String? templateUrl;
  /// 文件描述
  final String? description;
  /// 已上传的文件列表
  final List<UploadedFile> uploadedFiles;
  /// 审批信息
  final ApprovalInfo? approvalInfo;
  /// 学年学期信息
  final String courseInfo;
  /// 计划ID
  final String planId;
  /// 文件类型
  final String fileType;
  /// 文件代码
  final int fileCode;

  const FileUploadItem({
    required this.id,
    required this.fileName,
    required this.iconData,
    required this.iconColor,
    required this.status,
    required this.statusColor,
    required this.statusTextColor,
    required this.courseInfo,
    required this.planId,
    required this.fileType,
    required this.fileCode,
    this.templateUrl,
    this.description,
    this.uploadedFiles = const [],
    this.approvalInfo,
  });

  /// 获取状态文本
  String get statusText {
    switch (status) {
      case FileUploadStatus.notUploaded:
        return '未上传';
      case FileUploadStatus.uploaded:
        return '已上传';
      case FileUploadStatus.approved:
        return '已通过';
      case FileUploadStatus.rejected:
        return '已驳回';
    }
  }

  /// 复制并更新状态
  FileUploadItem copyWith({
    String? id,
    String? fileName,
    String? iconData,
    Color? iconColor,
    FileUploadStatus? status,
    Color? statusColor,
    Color? statusTextColor,
    String? templateUrl,
    String? description,
    List<UploadedFile>? uploadedFiles,
    ApprovalInfo? approvalInfo,
    String? courseInfo,
    String? planId,
    String? fileType,
    int? fileCode,
  }) {
    return FileUploadItem(
      id: id ?? this.id,
      fileName: fileName ?? this.fileName,
      iconData: iconData ?? this.iconData,
      iconColor: iconColor ?? this.iconColor,
      status: status ?? this.status,
      statusColor: statusColor ?? this.statusColor,
      statusTextColor: statusTextColor ?? this.statusTextColor,
      courseInfo: courseInfo ?? this.courseInfo,
      planId: planId ?? this.planId,
      fileType: fileType ?? this.fileType,
      fileCode: fileCode ?? this.fileCode,
      templateUrl: templateUrl ?? this.templateUrl,
      description: description ?? this.description,
      uploadedFiles: uploadedFiles ?? this.uploadedFiles,
      approvalInfo: approvalInfo ?? this.approvalInfo,
    );
  }
}
