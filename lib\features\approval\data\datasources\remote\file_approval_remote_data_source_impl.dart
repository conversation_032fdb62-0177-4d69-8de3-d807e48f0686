/// -----
/// file_approval_remote_data_source_impl.dart
///
/// 文件审批远程数据源实现
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions/server_exception.dart';

import '../../../../../core/network/dio_client.dart';
import '../../../../../core/utils/logger.dart';
import '../../models/file_approval_list_item_model.dart';
import 'file_approval_remote_data_source.dart';

/// 文件审批远程数据源实现
class FileApprovalRemoteDataSourceImpl implements FileApprovalRemoteDataSource {
  final DioClient _dioClient;

  FileApprovalRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<FileApprovalListItemModel>> getFileApprovalList(String planId) async {
    try {
      Logger.info('FileApprovalRemoteDataSource', '开始获取文件审批列表，planId: $planId');
      
      final response = await _dioClient.get(
        'internshipservice/v1/internship/teacher/file/approveList',
        queryParameters: {
          'planId': planId,
        },
      );

      Logger.debug('FileApprovalRemoteDataSource', '获取文件审批列表响应: $response');

      if (response is List) {
        final fileApprovalList = response
            .map((json) => FileApprovalListItemModel.fromJson(json as Map<String, dynamic>))
            .toList();

        Logger.info('FileApprovalRemoteDataSource', '成功获取${fileApprovalList.length}个文件审批项');
        return fileApprovalList;
      } else {
        Logger.warning('FileApprovalRemoteDataSource', '响应数据格式不正确');
        return [];
      }
    } catch (e) {
      Logger.error('FileApprovalRemoteDataSource', '获取文件审批列表失败: $e');
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取文件审批列表失败: $e');
    }
  }
}
