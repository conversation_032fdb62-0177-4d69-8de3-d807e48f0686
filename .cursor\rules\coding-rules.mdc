---
description: 
globs: 
alwaysApply: false
---
 # 编码规范

## 命名约定
- **类名**: 使用 PascalCase (例如: `ContentRepository`)
- **变量和方法**: 使用 camelCase (例如: `getVideoList()`)
- **常量**: 使用 UPPER_SNAKE_CASE (例如: `MAX_RETRY_COUNT`)
- **私有成员**: 使用下划线前缀 (例如: `_apiClient`)
- **文件名**: 使用 snake_case (例如: `content_repository.dart`)

## 分层命名规范
- **实体**: 使用领域名称 (例如: `ContentItem`)
- **模型**: 使用领域名称加 Model 后缀 (例如: `ContentItemModel`)
- **仓库接口**: 使用领域名称加 Repository 后缀 (例如: `ContentRepository`)
- **仓库实现**: 使用领域名称加 RepositoryImpl 后缀 (例如: `ContentRepositoryImpl`)
- **数据源**: 使用领域名称加 DataSource 后缀 (例如: `ContentRemoteDataSource`)
- **用例**: 使用动词加领域名称 (例如: `GetContentDetail`)

## BLoC 模式规范
- 每个功能模块应有自己的 BLoC
- BLoC 应分为三个文件: `xxx_bloc.dart`, `xxx_event.dart`, `xxx_state.dart`
- 状态应该是不可变的(immutable)，继承自 Equatable
- 事件应该是明确的动作，继承自 Equatable
- BLoC 应该在 DI 容器中注册为工厂

## 错误处理规范
- 使用 Either<Failure, T> 类型处理错误
- 定义清晰的异常类型和失败类型
- 在数据源层抛出异常
- 在仓库层捕获异常并转换为失败
- 在用例层返回 Either<Failure, T> 类型
- 在 BLoC 层处理失败并更新状态

## 日志记录规范
- 使用统一的日志工具类
- 遵循六个日志级别：VERBOSE、DEBUG、INFO、WARNING、ERROR、FATAL
- 统一的日志格式：`[级别] [时间] [标签] [消息] [可选:异常信息] [可选:堆栈跟踪]`
- 根据环境配置日志行为
- 敏感信息脱敏处理

## 提交规范
提交信息应遵循以下格式:
```
<type>(<scope>): <subject>

<body>
```

类型(type)包括:
- feat: 新功能
- fix: 错误修复
- docs: 文档更改
- style: 代码风格更改
- refactor: 代码重构
- test: 添加测试
- chore: 构建过程或辅助工具的变动 