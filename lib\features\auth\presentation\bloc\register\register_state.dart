import 'package:equatable/equatable.dart';

/// 注册状态基类
///
/// 所有注册相关的状态都应继承此类
abstract class RegisterState extends Equatable {
  const RegisterState();

  @override
  List<Object?> get props => [];
}

/// 注册初始状态
///
/// 表示注册流程的初始状态
class RegisterInitialState extends RegisterState {}

/// 注册加载状态
///
/// 表示正在进行注册操作
class RegisterLoadingState extends RegisterState {}

/// 注册成功状态
///
/// 表示注册操作成功
class RegisterSuccessState extends RegisterState {}

/// 注册失败状态
///
/// 表示注册操作失败
class RegisterFailureState extends RegisterState {
  final String message;

  const RegisterFailureState(this.message);

  @override
  List<Object?> get props => [message];
}

/// 验证码发送中状态
///
/// 表示正在发送验证码
class VerificationCodeSendingState extends RegisterState {}

/// 验证码发送成功状态
///
/// 表示验证码发送成功
class VerificationCodeSentState extends RegisterState {
  final int countdownTime;

  const VerificationCodeSentState(this.countdownTime);

  @override
  List<Object?> get props => [countdownTime];
}

/// 验证码发送失败状态
///
/// 表示验证码发送失败
class VerificationCodeFailureState extends RegisterState {
  final String message;

  const VerificationCodeFailureState(this.message);

  @override
  List<Object?> get props => [message];
}
