/// -----
/// internship_application_event.dart
///
/// 实习申请页面事件定义
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习申请事件基类
abstract class InternshipApplicationEvent extends Equatable {
  const InternshipApplicationEvent();

  @override
  List<Object?> get props => [];
}

/// 初始化事件
class InitializeApplicationEvent extends InternshipApplicationEvent {
  final String planId;

  const InitializeApplicationEvent(this.planId);

  @override
  List<Object?> get props => [planId];
}

/// 更新企业信息事件
class UpdateCompanyInfoEvent extends InternshipApplicationEvent {
  final String field;
  final String value;

  const UpdateCompanyInfoEvent(this.field, this.value);

  @override
  List<Object?> get props => [field, value];
}

/// 更新岗位信息事件
class UpdatePositionInfoEvent extends InternshipApplicationEvent {
  final String field;
  final String value;

  const UpdatePositionInfoEvent(this.field, this.value);

  @override
  List<Object?> get props => [field, value];
}

/// 更新实习信息事件
class UpdateInternshipInfoEvent extends InternshipApplicationEvent {
  final String field;
  final String value;

  const UpdateInternshipInfoEvent(this.field, this.value);

  @override
  List<Object?> get props => [field, value];
}

/// 选择日期事件
class SelectDateEvent extends InternshipApplicationEvent {
  final String field;
  final DateTime date;

  const SelectDateEvent(this.field, this.date);

  @override
  List<Object?> get props => [field, date];
}

/// 选择地址事件
class SelectLocationEvent extends InternshipApplicationEvent {
  final String field;
  final String location;

  const SelectLocationEvent(this.field, this.location);

  @override
  List<Object?> get props => [field, location];
}

/// 提交申请事件
class SubmitApplicationEvent extends InternshipApplicationEvent {
  const SubmitApplicationEvent();
}

/// 验证表单事件
class ValidateFormEvent extends InternshipApplicationEvent {
  const ValidateFormEvent();
}
