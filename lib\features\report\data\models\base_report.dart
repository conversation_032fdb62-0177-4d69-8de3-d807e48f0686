/// -----
/// base_report.dart
/// 
/// 报告基类，定义所有报告通用的属性
/// 
/// <AUTHOR>
/// @date 2025-06-17
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter_demo/features/report/core/enums/report_enums.dart';

abstract class BaseReport {
  final String id;
  final String userId;
  final String userName;
  final String courseName;
  final String? title;
  final DateTime createdAt;
  final ReportStatus status;
  final bool isLate;
  final int? rating;
  final String? teacherComment;
  final String? teacherName;
  final DateTime? commentTime;
  final String? infoTitle;
  final String? contentTitle;

  const BaseReport({
    required this.id,
    required this.userId,
    required this.userName,
    required this.courseName,
    this.title,
    required this.createdAt,
    required this.status,
    this.isLate = false,
    this.rating,
    this.teacherComment,
    this.teacherName,
    this.commentTime,
    this.infoTitle,
    this.contentTitle,
  });

  String get content;

  Map<String, dynamic> toJson();

  bool canEdit() => status == ReportStatus.draft || status == ReportStatus.rejected;
  bool canSubmit() => status == ReportStatus.draft || status == ReportStatus.rejected;
  bool canDelete() => status == ReportStatus.draft;
}