/// -----
/// step_connector.dart
///
/// 企业评分流程步骤连接线组件，用于连接相邻的步骤项
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 步骤连接线组件
/// 
/// 用于在企业评分流程步骤之间显示虚线连接
class StepConnector extends StatelessWidget {
  /// 连接线高度
  final double height;

  const StepConnector({
    Key? key,
    this.height = 40.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 60.w,
      height: height.h,
      child: Center(
        child: CustomPaint(
          size: Size(2.w, height.h),
          painter: DashedLinePainter(),
        ),
      ),
    );
  }
}

/// 虚线绘制器
class DashedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFCCCCCC)
      ..strokeWidth = 2.w
      ..style = PaintingStyle.stroke;

    const dashHeight = 8.0;
    const dashSpace = 4.0;
    double startY = 0;

    while (startY < size.height) {
      canvas.drawLine(
        Offset(size.width / 2, startY),
        Offset(size.width / 2, startY + dashHeight),
        paint,
      );
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
