/// -----------------------------------------------------------------------------
/// course_header_section.dart
///
/// 可复用的课程头部组件，展示课程名称，支持展开/收起功能和课程选择
///
/// <AUTHOR>
/// @date 2025-04-10
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/widgets/course_selector_dialog.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_plan_list_item.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_state.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/plan_list_global/plan_list_global_event.dart';

class CourseHeaderSection extends StatefulWidget {
  /// 当前选中的课程名称（可选，如果不提供则从全局状态获取）
  final String? courseName;

  /// 初始是否展开（已废弃，现在统一使用弹框选择）
  @Deprecated('现在统一使用弹框选择，不再需要展开/收起功能')
  final bool initialExpanded;

  /// 可选的课程列表，如果提供则显示课程选择功能（已废弃，现在从全局状态获取）
  @Deprecated('使用全局实习计划状态，不再需要手动传入课程列表')
  final List<String>? availableCourses;

  /// 课程变更回调（已废弃，现在通过全局状态管理）
  @Deprecated('使用全局实习计划状态，不再需要手动处理课程变更')
  final Function(String newCourse)? onCourseChanged;

  const CourseHeaderSection({
    Key? key,
    this.courseName,
    @Deprecated('现在统一使用弹框选择') this.initialExpanded = false,
    @Deprecated('使用全局实习计划状态') this.availableCourses,
    @Deprecated('使用全局实习计划状态') this.onCourseChanged,
  }) : super(key: key);

  @override
  State<CourseHeaderSection> createState() => _CourseHeaderSectionState();
}

class _CourseHeaderSectionState extends State<CourseHeaderSection> {
  @override
  void initState() {
    super.initState();
  }

  /// 显示课程选择弹框
  void _showCourseSelector(BuildContext context, List<InternshipPlanListItem> plans, String? currentPlanId) {
    if (plans.isEmpty) {
      return;
    }

    final int currentIndex = currentPlanId != null
        ? plans.indexWhere((plan) => plan.planId == currentPlanId)
        : 0;

    showCourseSelectorDialog(
      context: context,
      plans: plans,
      selectedIndex: currentIndex >= 0 ? currentIndex : 0,
      onPlanSelected: (index, plan) {
        final planListBloc = GetIt.instance<PlanListGlobalBloc>();
        planListBloc.add(SelectCurrentPlanEvent(plan.planId));
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PlanListGlobalBloc, PlanListGlobalState>(
      bloc: GetIt.instance<PlanListGlobalBloc>(),
      builder: (context, state) {
        // 确定显示的课程名称
        String displayName;
        bool hasCourseSelector = false;
        List<InternshipPlanListItem> plans = [];
        String? currentPlanId;

        if (state is PlanListGlobalLoadedState) {
          plans = state.plans;
          currentPlanId = state.currentPlanId;
          hasCourseSelector = plans.isNotEmpty; // 只要有计划就显示选择器

          if (widget.courseName != null) {
            // 如果手动指定了课程名称，优先使用
            displayName = widget.courseName!;
          } else if (state.currentPlan != null) {
            // 使用当前选中的计划名称
            displayName = state.currentPlan!.displayName;
          } else if (plans.isNotEmpty) {
            // 使用第一个计划的名称
            displayName = plans.first.displayName;
          } else {
            displayName = '暂无实习计划';
          }
        } else if (widget.courseName != null) {
          // 如果全局状态还未加载，但手动指定了课程名称
          displayName = widget.courseName!;
        } else {
          // 默认显示
          displayName = '加载中...';
        }

        return Container(
          color: Colors.white,
          child: Column(
            children: [
              InkWell(
                onTap: hasCourseSelector
                    ? () => _showCourseSelector(context, plans, currentPlanId)
                    : null, // 如果没有计划则禁用点击
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          displayName,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Icon(
                        hasCourseSelector
                            ? Icons.arrow_drop_down
                            : Icons.arrow_drop_down, // 统一使用下拉箭头图标
                        color: hasCourseSelector ? Colors.grey : Colors.grey[400], // 没有计划时图标颜色更浅
                      ),
                    ],
                  ),
                ),
              ),
              // 移除展开/收起功能，统一使用弹框选择
            ],
          ),
        );
      },
    );
  }
}