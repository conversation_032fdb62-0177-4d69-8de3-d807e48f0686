# 圆环进度组件

这是一套灵活、可配置的圆环进度组件，支持多种样式和动画效果。

## 组件架构

### 1. BaseProgressRing - 基础圆环组件
纯圆环绘制组件，只负责绘制圆环进度，不包含任何文字内容。

**特性：**
- 支持自定义颜色、尺寸、线宽
- 支持自定义起始角度
- 支持圆形端点样式
- 支持动画效果
- 使用 ScreenUtil 进行尺寸适配

**使用示例：**
```dart
BaseProgressRing(
  progress: 0.75,
  size: 120,
  strokeWidth: 10,
  progressColor: Color(0xFF2979FF),
  backgroundColor: Color(0xFFEEEEEE),
  enableAnimation: true,
)
```

### 2. ProgressRingContent - 内容显示组件
只负责显示中心文字内容，可以独立使用或与圆环组件组合使用。

**特性：**
- 支持主要文本和副标题
- 支持自定义文本样式
- 支持文本动画效果
- 预定义多种文本样式

**使用示例：**
```dart
ProgressRingContent(
  mainText: '85',
  subtitle: '总评分',
  mainTextStyle: ProgressRingContentStyles.scoreMainTextStyle,
  subtitleStyle: ProgressRingContentStyles.standardSubtitleStyle,
  enableAnimation: true,
)
```

### 3. UnifiedProgressRing - 统一圆环组件
组合了圆环绘制和内容显示功能，提供完整的圆环进度展示。

**特性：**
- 结合圆环和文字显示
- 高度可配置
- 支持动画效果
- 适用于大多数使用场景

**使用示例：**
```dart
UnifiedProgressRing(
  progress: 0.85,
  mainText: '85',
  subtitle: '校内老师总评分',
  size: 160,
  strokeWidth: 10,
  progressColor: Color(0xFF2979FF),
  enableAnimation: true,
)
```

## 预定义样式

### 1. 评分样式
适用于成绩评分显示：
```dart
UnifiedProgressRingStyles.score(
  progress: 0.85,
  score: '85',
  title: '校内老师总评分',
  enableAnimation: true,
)
```

### 2. 签到样式
适用于签到天数显示：
```dart
UnifiedProgressRingStyles.signIn(
  progress: 0.5,
  signedDays: 15,
  totalDays: 30,
  label: '签到天数',
  enableAnimation: true,
)
```

### 3. 百分比样式
适用于百分比进度显示：
```dart
UnifiedProgressRingStyles.percentage(
  progress: 0.75,
  title: '完成率',
  enableAnimation: true,
)
```

### 4. 计数样式
适用于简单计数显示：
```dart
UnifiedProgressRingStyles.count(
  progress: 0.6,
  count: '30',
  title: '任务完成',
  enableAnimation: true,
)
```

## 文本样式预设

### ProgressRingContentStyles 类提供以下预设样式：

- `scoreMainTextStyle` - 分数样式（80.sp，粗体，主色调）
- `countMainTextStyle` - 计数样式（28.sp，粗体，黑色）
- `percentageMainTextStyle` - 百分比样式（60.sp，粗体，主色调）
- `standardSubtitleStyle` - 标准副标题样式（24.sp，灰色）
- `smallSubtitleStyle` - 小号副标题样式（20.sp，灰色）

## 动画支持

所有组件都支持动画效果：

- **圆环动画**：进度值从 0 渐变到目标值
- **文字动画**：淡入 + 缩放效果
- **动画时长**：可自定义，默认 800ms
- **动画曲线**：圆环使用 easeInOut，文字使用 elasticOut

## 向后兼容性

原有的 `ScoreCircleWidget` 和 `SignProgressRing` 组件已经重构为基于新的统一组件，保持了完全的向后兼容性。

### ScoreCircleWidget 重构
```dart
// 原有用法保持不变
ScoreCircleWidget(
  score: 85,
  maxScore: 100,
  title: '校内老师总评分',
  enableAnimation: true, // 新增动画支持
)
```

### SignProgressRing 重构
```dart
// 原有用法保持不变
SignProgressRing(
  signedDays: 15,
  totalDays: 30,
  label: '签到天数',
  enableAnimation: true, // 新增动画支持
)
```

## 使用建议

1. **独立使用圆环**：当只需要圆环进度指示器时，使用 `BaseProgressRing`
2. **独立使用文字**：当只需要格式化文字显示时，使用 `ProgressRingContent`
3. **完整功能**：大多数情况下使用 `UnifiedProgressRing` 或预定义样式
4. **动画效果**：在用户交互或数据更新时启用动画以提升用户体验
5. **样式一致性**：使用预定义样式保持应用内的视觉一致性

## 性能优化

- 使用 `CustomPainter` 进行高效的圆环绘制
- 动画控制器在组件销毁时自动释放
- 支持 `shouldRepaint` 优化，避免不必要的重绘
- 使用 ScreenUtil 确保在不同屏幕尺寸下的一致性

## 扩展性

组件设计遵循单一职责原则，易于扩展：

- 可以轻松添加新的预定义样式
- 可以创建自定义的文本样式
- 可以扩展动画效果
- 可以添加新的圆环绘制模式（如渐变色、多段进度等）
