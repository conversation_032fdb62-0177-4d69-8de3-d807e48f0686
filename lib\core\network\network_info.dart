/// -----
/// network_info.dart
///
/// 网络信息工具类，用于检查网络连接状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/foundation.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

/// 网络信息工具类
///
/// 用于检查网络连接状态
abstract class NetworkInfo {
  /// 检查是否有网络连接
  Future<bool> get isConnected;
}

/// 网络信息实现类
class NetworkInfoImpl implements NetworkInfo {
  final InternetConnectionChecker? connectionChecker;

  NetworkInfoImpl(this.connectionChecker);

  @override
  Future<bool> get isConnected async {
    if (kIsWeb) {
      // Web平台：假设总是有网络连接
      // 在Web环境中，如果页面能加载，通常意味着有网络连接
      return true;
    } else {
      // 移动平台：使用InternetConnectionChecker
      return connectionChecker?.hasConnection ?? Future.value(true);
    }
  }
}