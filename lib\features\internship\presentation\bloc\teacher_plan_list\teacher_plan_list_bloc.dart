/// -----
/// teacher_plan_list_bloc.dart
/// 
/// 教师实习计划列表BLoC
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/error/failures/auth_failure.dart';
import '../../../../../core/usecases/usecase.dart';
import '../../../../../core/utils/logger.dart';
import '../../../domain/usecases/get_teacher_internship_plans_usecase.dart';
import 'teacher_plan_list_event.dart';
import 'teacher_plan_list_state.dart';

/// 教师实习计划列表BLoC
/// 
/// 处理教师实习计划列表相关的业务逻辑
class TeacherPlanListBloc extends Bloc<TeacherPlanListEvent, TeacherPlanListState> {
  final GetTeacherInternshipPlansUseCase _getTeacherInternshipPlansUseCase;

  TeacherPlanListBloc({
    required GetTeacherInternshipPlansUseCase getTeacherInternshipPlansUseCase,
  })  : _getTeacherInternshipPlansUseCase = getTeacherInternshipPlansUseCase,
        super(const TeacherPlanListInitialState()) {
    on<LoadTeacherPlanListEvent>(_onLoadTeacherPlanList);
    on<RefreshTeacherPlanListEvent>(_onRefreshTeacherPlanList);
  }

  /// 处理加载教师实习计划列表事件
  Future<void> _onLoadTeacherPlanList(
    LoadTeacherPlanListEvent event,
    Emitter<TeacherPlanListState> emit,
  ) async {
    Logger.info('TeacherPlanListBloc', '开始加载教师实习计划列表');
    emit(const TeacherPlanListLoadingState());

    final result = await _getTeacherInternshipPlansUseCase(const NoParams());

    result.fold(
      (failure) {
        Logger.error('TeacherPlanListBloc', '加载教师实习计划列表失败: ${failure.message}');

        // 检查是否是认证失败
        if (failure is UnauthorizedFailure || failure is ForbiddenFailure) {
          emit(TeacherPlanListAuthFailureState(failure.message));
        } else {
          emit(TeacherPlanListErrorState(failure.message));
        }
      },
      (plans) {
        Logger.info('TeacherPlanListBloc', '成功加载${plans.length}个实习计划');
        emit(TeacherPlanListLoadedState(plans));
      },
    );
  }

  /// 处理刷新教师实习计划列表事件
  Future<void> _onRefreshTeacherPlanList(
    RefreshTeacherPlanListEvent event,
    Emitter<TeacherPlanListState> emit,
  ) async {
    Logger.info('TeacherPlanListBloc', '开始刷新教师实习计划列表');

    final result = await _getTeacherInternshipPlansUseCase(const NoParams());

    result.fold(
      (failure) {
        Logger.error('TeacherPlanListBloc', '刷新教师实习计划列表失败: ${failure.message}');

        // 检查是否是认证失败
        if (failure is UnauthorizedFailure || failure is ForbiddenFailure) {
          emit(TeacherPlanListAuthFailureState(failure.message));
        } else {
          emit(TeacherPlanListRefreshErrorState(failure.message));
        }
      },
      (plans) {
        Logger.info('TeacherPlanListBloc', '成功刷新${plans.length}个实习计划');
        emit(TeacherPlanListRefreshSuccessState(plans));
      },
    );
  }
}
