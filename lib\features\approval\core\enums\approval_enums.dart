/// -----
/// approval_enums.dart
/// 
/// 审批相关的枚举定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 审批状态枚举
enum ApprovalStatus {
  /// 待审批
  pending,
  /// 已通过
  approved,
  /// 已驳回
  rejected,
}

/// 审批状态扩展方法
extension ApprovalStatusExtension on ApprovalStatus {
  /// 获取状态显示文本
  String get displayName {
    switch (this) {
      case ApprovalStatus.pending:
        return '待审批';
      case ApprovalStatus.approved:
        return '已通过';
      case ApprovalStatus.rejected:
        return '已驳回';
    }
  }

  /// 获取状态颜色
  String get colorHex {
    switch (this) {
      case ApprovalStatus.pending:
        return '#FFA500'; // 橙色
      case ApprovalStatus.approved:
        return '#00C851'; // 绿色
      case ApprovalStatus.rejected:
        return '#FF4444'; // 红色
    }
  }
}
