/// -----
/// internship_score_item.dart
/// 
/// 实习成绩列表项组件，用于展示学生实习成绩信息
///
/// <AUTHOR>
/// @date 2025-05-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_score_detail_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 实习成绩列表项组件
///
/// 用于展示学生的实习成绩信息，包括头像、姓名、电话和成绩状态
/// 支持待评分和已评分两种状态
class InternshipScoreItem extends StatelessWidget {
  /// 学生姓名
  final String studentName;
  
  /// 学生电话
  final String phoneNumber;
  
  /// 学生头像URL
  final String avatarUrl;
  
  /// 成绩状态文本
  final String scoreStatus;
  
  /// 成绩分数，如果为null则表示未评分
  final int? score;
  
  /// 评分按钮点击回调
  final VoidCallback? onRatePressed;
  
  /// 是否为待评分状态
  final bool isPending;

  const InternshipScoreItem({
    Key? key,
    required this.studentName,
    required this.phoneNumber,
    required this.avatarUrl,
    required this.scoreStatus,
    this.score,
    this.onRatePressed,
    this.isPending = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 12,right: 12,top:12, bottom: 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20.r),
          onTap: () {
            // 跳转到实习成绩详情页
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => const InternshipScoreDetailScreen(
                  studentId: '1',
                  courseId: '1',
                  courseName: '2021级市场销售2023-2024实习学年第二学期岗位实习',
                ),
              ),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // 学生头像
                CircleAvatar(
                  radius: 44.r,
                  backgroundImage: NetworkImage(avatarUrl),
                ),
                const SizedBox(width: 12),
                
                // 学生信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 学生姓名和电话
                      Row(
                        children: [
                          Text(
                            studentName,
                            style: TextStyle(
                              fontSize: 28.sp,
                              fontWeight: FontWeight.bold,
                              color: AppTheme.black333,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.phone,
                            size: 12,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            phoneNumber,
                            style: TextStyle(
                              fontSize: 23.sp,
                              color: AppTheme.black666,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      
                      // 成绩状态
                      Text(
                        isPending ? scoreStatus : '实习考核成绩: $score分',
                        style: TextStyle(
                          fontSize: 14,
                          color: isPending ? AppTheme.black999 : const Color(0xFF67C23A),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 评分按钮
                if (isPending && onRatePressed != null)
                  OutlinedButton(
                    onPressed: onRatePressed,
                    style: OutlinedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      foregroundColor: AppTheme.primaryColor,
                      side: BorderSide(
                        color: AppTheme.primaryColor,
                        width: 2.w,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6.r),
                      ),
                      padding: EdgeInsets.zero,
                      minimumSize: Size(120.w, 48.h),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: Text(
                      '去评分',
                      style: TextStyle(
                        fontSize: 22.sp,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
