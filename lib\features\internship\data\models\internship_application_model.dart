import 'package:flutter_demo/core/constants/constants.dart';

/// -----
/// internship_application_model.dart
/// 
/// 实习申请数据模型，用于存储实习申请的数据
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

class InternshipApplicationModel {
  final String id;
  final String studentName;
  final String studentAvatar;
  final String companyName;
  final String department;
  final String position;
  final String applyDate;
  final String status; // 待审批、已通过、已驳回
  
  InternshipApplicationModel({
    required this.id,
    required this.studentName,
    required this.studentAvatar,
    required this.companyName,
    required this.department,
    required this.position,
    required this.applyDate,
    required this.status,
  });
  
  // 从JSON创建模型
  factory InternshipApplicationModel.fromJson(Map<String, dynamic> json) {
    return InternshipApplicationModel(
      id: json['id'],
      studentName: json['studentName'],
      studentAvatar: json['studentAvatar'],
      companyName: json['companyName'],
      department: json['department'],
      position: json['position'],
      applyDate: json['applyDate'],
      status: json['status'],
    );
  }
  
  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentName': studentName,
      'studentAvatar': studentAvatar,
      'companyName': companyName,
      'department': department,
      'position': position,
      'applyDate': applyDate,
      'status': status,
    };
  }
  
  // 获取示例数据
  static List<InternshipApplicationModel> getSampleData() {
    return [
      InternshipApplicationModel(
        id: '1',
        studentName: '李成儒',
        studentAvatar: AppConstants.avatar1,
        companyName: '湖北省武汉市九州通中药材电子商务有限公司',
        department: '研发部',
        position: '前端工程师',
        applyDate: '2025-04-23 22:12',
        status: '待审批',
      ),
      InternshipApplicationModel(
        id: '2',
        studentName: '陈诚',
        studentAvatar: AppConstants.avatar2,
        companyName: '合肥家家帮信息技术股份有限公司',
        department: '企管部',
        position: '财务专员',
        applyDate: '2025-04-23 22:12',
        status: '待审批',
      ),
      InternshipApplicationModel(
        id: '3',
        studentName: '张洪涛',
        studentAvatar: AppConstants.avatar3,
        companyName: '武汉市谦通信息技术有限公司',
        department: '产品部',
        position: 'UI设计师',
        applyDate: '2025-04-23 22:12',
        status: '待审批',
      ),
      InternshipApplicationModel(
        id: '4',
        studentName: '王小明',
        studentAvatar: AppConstants.avatar4,
        companyName: '深圳市腾讯计算机系统有限公司',
        department: '技术部',
        position: '后端开发工程师',
        applyDate: '2025-04-23 22:12',
        status: '待审批',
      ),
      InternshipApplicationModel(
        id: '5',
        studentName: '李成儒',
        studentAvatar: AppConstants.avatar1,
        companyName: '湖北省武汉市九州通中药材电子商务有限公司',
        department: '研发部',
        position: '前端工程师',
        applyDate: '2025-04-23 22:12',
        status: '已通过',
      ),
      InternshipApplicationModel(
        id: '6',
        studentName: '陈诚',
        studentAvatar: AppConstants.avatar2,
        companyName: '合肥家家帮信息技术股份有限公司',
        department: '企管部',
        position: '财务专员',
        applyDate: '2025-04-23 22:12',
        status: '已驳回',
      ),
      InternshipApplicationModel(
        id: '7',
        studentName: '张洪涛',
        studentAvatar: AppConstants.avatar3,
        companyName: '武汉市谦通信息技术有限公司',
        department: '产品部',
        position: 'UI设计师',
        applyDate: '2025-04-23 22:12',
        status: '已驳回',
      ),
    ];
  }
}
