/// -----
/// internship_info_form.dart
/// 
/// 实习信息表单组件，用于展示和编辑实习基本信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';

class InternshipInfo {
  String startTime;
  String endTime;
  String internshipStyle;
  String professionalMatch;
  String stipend;
  String accommodationType;
  String accommodationAddress;
  String accommodationRegion;
  bool providesMeals;
  bool hasSpecialCircumstances;

  InternshipInfo({
    required this.startTime,
    required this.endTime,
    required this.internshipStyle,
    required this.professionalMatch,
    required this.stipend,
    required this.accommodationType,
    required this.accommodationAddress,
    required this.accommodationRegion,
    this.providesMeals = false,
    this.hasSpecialCircumstances = false,
  });
}

class InternshipInfoForm extends StatelessWidget {
  final InternshipInfo internshipInfo;
  final Function(InternshipInfo) onInternshipInfoChanged;
  final bool readOnly;
  final bool showChangeHighlight;

  const InternshipInfoForm({
    Key? key,
    required this.internshipInfo,
    required this.onInternshipInfoChanged,
    this.readOnly = true,
    this.showChangeHighlight = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('实习信息'),
          
          // 岗位开始时间
          _buildInfoRow(
            '岗位开始时间',
            internshipInfo.startTime,
            onChanged: (value) {
              internshipInfo.startTime = value;
              onInternshipInfoChanged(internshipInfo);
            },
          ),
          
          _buildDivider(),
          
          // 岗位结束时间
          _buildInfoRow(
            '岗位结束时间',
            internshipInfo.endTime,
            onChanged: (value) {
              internshipInfo.endTime = value;
              onInternshipInfoChanged(internshipInfo);
            },
          ),
          
          _buildDivider(),
          
          // 实习方式
          _buildInfoRow(
            '实习方式',
            internshipInfo.internshipStyle,
            onChanged: (value) {
              internshipInfo.internshipStyle = value;
              onInternshipInfoChanged(internshipInfo);
            },
          ),
          
          _buildDivider(),
          
          // 专业匹配
          _buildInfoRow(
            '专业匹配',
            internshipInfo.professionalMatch,
            onChanged: (value) {
              internshipInfo.professionalMatch = value;
              onInternshipInfoChanged(internshipInfo);
            },
          ),
          
          _buildDivider(),
          
          // 实习薪酬
          _buildInfoRow(
            '实习薪酬',
            internshipInfo.stipend,
            onChanged: (value) {
              internshipInfo.stipend = value;
              onInternshipInfoChanged(internshipInfo);
            },
          ),
          
          _buildDivider(),
          
          // 住宿类型
          _buildInfoRow(
            '住宿类型',
            internshipInfo.accommodationType,
            onChanged: (value) {
              internshipInfo.accommodationType = value;
              onInternshipInfoChanged(internshipInfo);
            },
          ),
          
          _buildDivider(),
          
          // 住宿区域
          _buildInfoRow(
            '住宿区域',
            internshipInfo.accommodationRegion,
            onChanged: (value) {
              internshipInfo.accommodationRegion = value;
              onInternshipInfoChanged(internshipInfo);
            },
          ),
          
          _buildDivider(),
          
          // 住宿详细地址
          _buildInfoRow(
            '住宿详细地址',
            internshipInfo.accommodationAddress,
            onChanged: (value) {
              internshipInfo.accommodationAddress = value;
              onInternshipInfoChanged(internshipInfo);
            },
          ),
          
          _buildDivider(),
          
          // 是否提供伙食
          _buildBooleanRow(
            '是否提供伙食',
            internshipInfo.providesMeals,
            onChanged: (value) {
              if (value != null) {
                internshipInfo.providesMeals = value;
                onInternshipInfoChanged(internshipInfo);
              }
            },
          ),
          
          _buildDivider(),
          
          // 实习工作是否有特殊情况
          _buildBooleanRow(
            '实习工作是否有特殊情况',
            internshipInfo.hasSpecialCircumstances,
            onChanged: (value) {
              if (value != null) {
                internshipInfo.hasSpecialCircumstances = value;
                onInternshipInfoChanged(internshipInfo);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    Function(String)? onChanged,
    bool highlight = false,
  }) {
    final isChanged = showChangeHighlight && highlight;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: readOnly
                ? Text(
                    value,
                    style: TextStyle(
                      fontSize: 14,
                      color: isChanged ? Colors.red : Colors.black87,
                    ),
                  )
                : TextField(
                    controller: TextEditingController(text: value),
                    onChanged: onChanged,
                    style: TextStyle(
                      fontSize: 14,
                      color: isChanged ? Colors.red : Colors.black87,
                    ),
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      isDense: true,
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildBooleanRow(
    String label,
    bool value, {
    Function(bool?)? onChanged,
    bool highlight = false,
  }) {
    final isChanged = showChangeHighlight && highlight;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: readOnly
                ? Text(
                    value ? '是' : '否',
                    style: TextStyle(
                      fontSize: 14,
                      color: isChanged ? Colors.red : Colors.black87,
                    ),
                  )
                : Row(
                    children: [
                      Radio<bool>(
                        value: true,
                        groupValue: value,
                        onChanged: onChanged,
                      ),
                      const Text('是'),
                      const SizedBox(width: 20),
                      Radio<bool>(
                        value: false,
                        groupValue: value,
                        onChanged: onChanged,
                      ),
                      const Text('否'),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 0.5,
      color: Colors.grey[300],
      indent: 16,
      endIndent: 16,
    );
  }
} 