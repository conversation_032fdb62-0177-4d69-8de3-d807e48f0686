/// -----
/// exemption_application_state.dart
///
/// 免实习申请BLoC状态定义
/// 定义免实习申请相关的UI状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// 第三方库
import 'package:equatable/equatable.dart';

/// 免实习申请状态基类
///
/// 所有免实习申请相关的状态都继承自此类
abstract class ExemptionApplicationState extends Equatable {
  const ExemptionApplicationState();

  @override
  List<Object?> get props => [];
}

/// 免实习申请初始状态
///
/// BLoC的初始状态
class ExemptionApplicationInitialState extends ExemptionApplicationState {
  const ExemptionApplicationInitialState();
}

/// 免实习申请提交中状态
///
/// 正在提交申请时的状态
class ExemptionApplicationSubmittingState extends ExemptionApplicationState {
  const ExemptionApplicationSubmittingState();
}

/// 免实习申请提交成功状态
///
/// 申请提交成功时的状态
class ExemptionApplicationSubmittedState extends ExemptionApplicationState {
  /// 成功消息
  final String message;
  
  /// 申请记录ID
  final int applicationId;

  const ExemptionApplicationSubmittedState({
    required this.message,
    required this.applicationId,
  });

  @override
  List<Object?> get props => [message, applicationId];
}

/// 免实习申请提交失败状态
///
/// 申请提交失败时的状态
class ExemptionApplicationFailureState extends ExemptionApplicationState {
  /// 错误消息
  final String message;

  const ExemptionApplicationFailureState({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}
