---
description: 
globs: 
alwaysApply: true
---
---
description: 要求为所有新创建的文件添加标准文件头注释，包括文件功能描述、作者和版权信息
globs: **/*.*
---
- 每个新创建的文件需要添加文件注释，说明该文件的主要功能和用途
- 文件注释应放在文件的最开头，使用相应语言的注释标记
- 所有文件必须包含作者信息：Mr.Wang
- 所有文件必须包含版权声明：Copyright © 2025
- 文件头注释模板应参考以下格式（根据不同语言的注释格式进行调整）：

```dart
/// -----------------------------------------------------------------------------
/// [文件名]
/// 
/// [文件功能描述]
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----------------------------------------------------------------------------
```

```javascript
/**
 * -----------------------------------------------------------------------------
 * [文件名]
 * 
 * [文件功能描述]
 * 
 * <AUTHOR>
 * @version 1.0
 * @copyright Copyright © 2025 亿硕教育
 * -----------------------------------------------------------------------------
 */
```

```python
# -----------------------------------------------------------------------------
# [文件名]
# 
# [文件功能描述]
# 
# <AUTHOR>
# @version 1.0
# @copyright Copyright © 2025 亿硕教育
# -----------------------------------------------------------------------------
``` 