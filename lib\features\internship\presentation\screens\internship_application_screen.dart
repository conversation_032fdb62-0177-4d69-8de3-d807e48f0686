/// -----
/// internship_application_screen.dart
///
/// 学生端实习申请页面，用于学生提交实习申请信息，包括企业信息、岗位信息、实习信息
/// 重构后使用BLoC状态管理和组件化设计
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_state.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/sections/company_info_section.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/sections/position_info_section.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/sections/internship_info_section.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/application_submit_button.dart';

/// 实习申请页面
///
/// 学生提交实习申请信息，包括企业信息、岗位信息、实习信息
/// 重构后使用BLoC状态管理和组件化设计
///
/// 使用示例：
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(
///     builder: (context) => BlocProvider(
///       create: (context) => InternshipApplicationBloc()
///         ..add(InitializeApplicationEvent('123')),
///       child: const InternshipApplicationScreen(planId: '123'),
///     ),
///   ),
/// );
/// ```
class InternshipApplicationScreen extends StatelessWidget {
  /// 实习计划ID
  ///
  /// 用于关联实习申请与实习计划
  final String planId;

  /// 创建实习申请页面
  ///
  /// @param key 小部件的键
  /// @param planId 实习计划ID，不能为空
  const InternshipApplicationScreen({
    super.key,
    required this.planId,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<InternshipApplicationBloc, InternshipApplicationState>(
      listener: (context, state) {
        // 处理错误消息
        if (state.errorMessage != null) {
          AppSnackBar.showError(context, state.errorMessage!);
          // 清除消息
          context.read<InternshipApplicationBloc>().add(const ValidateFormEvent());
        }

        // 处理成功消息
        if (state.successMessage != null) {
          AppSnackBar.showSuccess(context, state.successMessage!);
          // 可以在这里添加导航逻辑，比如返回上一页
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        appBar: const CustomAppBar(
          title: '实习申请',
          backgroundColor: Colors.white,
        ),
        body: BlocBuilder<InternshipApplicationBloc, InternshipApplicationState>(
          builder: (context, state) {
            if (state.isLoading) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }

            return Column(
              children: [
                Expanded(
                  child: ListView(
                    children: [
                      // 企业信息部分
                      CompanyInfoSection(companyInfo: state.companyInfo),

                      // 岗位信息部分
                      PositionInfoSection(positionInfo: state.positionInfo),

                      // 实习信息部分
                      InternshipInfoSection(internshipInfo: state.internshipInfo),
                    ],
                  ),
                ),

                // 提交按钮
                const ApplicationSubmitButton(),
              ],
            );
          },
        ),
      ),
    );
  }
}
