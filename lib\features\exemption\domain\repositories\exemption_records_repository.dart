/// -----
/// exemption_records_repository.dart
///
/// 免实习记录仓库接口
/// 定义免实习记录相关的业务操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures/failure.dart';
import '../../data/models/exemption_record_model.dart';

/// 免实习记录仓库接口
///
/// 定义免实习记录相关的业务操作方法
abstract class ExemptionRecordsRepository {
  /// 获取学生免实习记录
  ///
  /// [planId] 实习计划ID
  /// 返回Either类型，Left为失败信息，Right为免实习记录（可能为null）
  Future<Either<Failure, ExemptionRecordModel?>> getExemptionRecord({
    required String planId,
  });
}
