/// -----
/// exemption_application_request_model.dart
///
/// 免实习申请请求数据模型
/// 用于向服务器提交免实习申请的数据结构
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/foundation.dart';

/// 免实习申请请求数据模型
///
/// 根据API接口定义的请求参数结构
@immutable
class ExemptionApplicationRequestModel {
  /// 实习计划ID
  final int planId;

  /// 免实习理由
  final String reason;

  /// 证明材料文件URL
  final String fileUrl;

  const ExemptionApplicationRequestModel({
    required this.planId,
    required this.reason,
    required this.fileUrl,
  });

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'planId': planId,
      'reason': reason,
      'fileUrl': fileUrl,
    };
  }

  /// 从JSON创建实例
  factory ExemptionApplicationRequestModel.fromJson(Map<String, dynamic> json) {
    return ExemptionApplicationRequestModel(
      planId: json['planId'] ?? 0,
      reason: json['reason'] ?? '',
      fileUrl: json['fileUrl'] ?? '',
    );
  }

  /// 创建副本
  ExemptionApplicationRequestModel copyWith({
    int? planId,
    String? reason,
    String? fileUrl,
  }) {
    return ExemptionApplicationRequestModel(
      planId: planId ?? this.planId,
      reason: reason ?? this.reason,
      fileUrl: fileUrl ?? this.fileUrl,
    );
  }

  @override
  String toString() {
    return 'ExemptionApplicationRequestModel(planId: $planId, reason: $reason, fileUrl: $fileUrl)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    return other is ExemptionApplicationRequestModel &&
        other.planId == planId &&
        other.reason == reason &&
        other.fileUrl == fileUrl;
  }

  @override
  int get hashCode {
    return planId.hashCode ^ reason.hashCode ^ fileUrl.hashCode;
  }
}
