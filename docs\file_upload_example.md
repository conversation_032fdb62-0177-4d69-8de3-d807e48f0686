# 文件上传功能使用示例

## 基本使用

### 1. 在页面中使用AttachmentUploader组件

```dart
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/report/presentation/widgets/attachment_uploader.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_bloc.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_event.dart';
import 'package:flutter_demo/features/report/presentation/bloc/write/report_write_state.dart';

class MyUploadPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('文件上传示例')),
      body: BlocConsumer<ReportWriteBloc, ReportWriteState>(
        listener: (context, state) {
          if (state is ReportWriteAttachmentUploadSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('文件上传成功')),
            );
          } else if (state is ReportWriteAttachmentUploadError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('上传失败: ${state.message}')),
            );
          }
        },
        builder: (context, state) {
          if (state is ReportWriteLoaded) {
            return AttachmentUploader(
              attachments: state.attachments,
              onFileSelected: (filePath, fileName, fileSize) {
                // 添加文件到BLoC
                context.read<ReportWriteBloc>().add(AddAttachmentEvent(
                  filePath: filePath,
                  fileName: fileName,
                  fileSize: fileSize,
                ));
              },
              onRemoveFile: (index) {
                // 删除文件
                context.read<ReportWriteBloc>().add(RemoveAttachmentEvent(index));
              },
              isUploading: state is ReportWriteUploadingAttachment,
              uploadProgress: state is ReportWriteUploadingAttachment 
                  ? state.uploadProgress 
                  : 0.0,
              uploadingFileName: state is ReportWriteUploadingAttachment 
                  ? state.uploadingFileName 
                  : null,
              maxFiles: 1, // 限制文件数量
            );
          }
          return Center(child: CircularProgressIndicator());
        },
      ),
    );
  }
}
```

### 2. 初始化BLoC

```dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ReportWriteBloc()..add(
        InitializeReportWriteEvent(
          reportType: ReportType.weekly,
          reportId: null,
        ),
      ),
      child: MyUploadPage(),
    );
  }
}
```

## 高级用法

### 1. 自定义文件验证

```dart
// 在FilePickerService中添加自定义验证
Future<ValidationResult> customValidateFile(String filePath, String fileName) async {
  final result = await validateFile(filePath, fileName);
  
  if (!result.isValid) {
    return result;
  }
  
  // 添加自定义验证逻辑
  if (fileName.contains('temp')) {
    return ValidationResult(
      isValid: false,
      error: '不允许上传临时文件',
    );
  }
  
  return result;
}
```

### 2. 自定义上传服务

```dart
class CustomFileUploadService extends FileUploadService {
  CustomFileUploadService(super.dioClient);
  
  @override
  Future<String> uploadFile({
    required String filePath,
    required String fileName,
    Function(double progress)? onProgress,
  }) async {
    // 添加自定义上传逻辑
    print('开始上传文件: $fileName');
    
    final result = await super.uploadFile(
      filePath: filePath,
      fileName: fileName,
      onProgress: onProgress,
    );
    
    print('文件上传完成: $result');
    return result;
  }
}
```

### 3. 批量文件上传

```dart
class BatchUploadWidget extends StatefulWidget {
  @override
  _BatchUploadWidgetState createState() => _BatchUploadWidgetState();
}

class _BatchUploadWidgetState extends State<BatchUploadWidget> {
  final List<AttachmentModel> _attachments = [];
  
  void _addMultipleFiles() async {
    final filePickerService = FilePickerService();
    
    // 选择多个文件
    final result = await filePickerService.pickAnyFile();
    if (result != null) {
      for (final file in result.files) {
        if (file.path != null) {
          final validation = await filePickerService.validateFile(
            file.path!,
            file.name,
          );
          
          if (validation.isValid) {
            setState(() {
              _attachments.add(AttachmentModel(
                filePath: file.path!,
                fileName: file.name,
                fileSize: validation.fileSize,
              ));
            });
          }
        }
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ElevatedButton(
          onPressed: _addMultipleFiles,
          child: Text('选择多个文件'),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: _attachments.length,
            itemBuilder: (context, index) {
              final attachment = _attachments[index];
              return ListTile(
                title: Text(attachment.fileName),
                subtitle: Text(attachment.fileSize ?? ''),
                trailing: IconButton(
                  icon: Icon(Icons.delete),
                  onPressed: () {
                    setState(() {
                      _attachments.removeAt(index);
                    });
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
```

## 错误处理

### 1. 网络错误处理

```dart
BlocListener<ReportWriteBloc, ReportWriteState>(
  listener: (context, state) {
    if (state is ReportWriteAttachmentUploadError) {
      String message = '上传失败';
      
      if (state.message.contains('网络')) {
        message = '网络连接失败，请检查网络设置';
      } else if (state.message.contains('大小')) {
        message = '文件大小超过限制，请选择较小的文件';
      } else if (state.message.contains('类型')) {
        message = '不支持的文件类型';
      }
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          action: SnackBarAction(
            label: '重试',
            onPressed: () {
              // 重试上传逻辑
            },
          ),
        ),
      );
    }
  },
  child: YourWidget(),
)
```

### 2. 权限错误处理

```dart
Future<void> _handlePermissionError() async {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text('权限不足'),
      content: Text('需要存储权限才能选择文件，请在设置中开启权限。'),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('取消'),
        ),
        TextButton(
          onPressed: () {
            Navigator.pop(context);
            // 打开应用设置
            // openAppSettings();
          },
          child: Text('去设置'),
        ),
      ],
    ),
  );
}
```

## 性能优化

### 1. 图片压缩

```dart
Future<String> _compressImage(String imagePath) async {
  final file = File(imagePath);
  final bytes = await file.readAsBytes();
  
  // 使用image包进行压缩
  final image = img.decodeImage(bytes);
  if (image != null) {
    final compressed = img.encodeJpg(image, quality: 85);
    
    final compressedFile = File('${imagePath}_compressed.jpg');
    await compressedFile.writeAsBytes(compressed);
    
    return compressedFile.path;
  }
  
  return imagePath;
}
```

### 2. 缓存管理

```dart
class FileCache {
  static final Map<String, String> _cache = {};
  
  static String? getCachedUrl(String filePath) {
    return _cache[filePath];
  }
  
  static void setCachedUrl(String filePath, String url) {
    _cache[filePath] = url;
  }
  
  static void clearCache() {
    _cache.clear();
  }
}
```

## 总结

文件上传功能提供了完整的解决方案，包括：

1. **易用的UI组件**：AttachmentUploader提供了美观的上传界面
2. **完整的状态管理**：使用BLoC管理上传状态和进度
3. **灵活的配置**：支持文件类型限制、大小限制等
4. **错误处理**：完善的错误提示和处理机制
5. **扩展性**：易于扩展和自定义

通过这些示例，你可以快速集成文件上传功能到你的应用中。
