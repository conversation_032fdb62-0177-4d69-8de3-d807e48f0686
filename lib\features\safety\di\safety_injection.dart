/// -----
/// safety_injection.dart
/// 
/// 安全教育模块依赖注入
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/features/safety/data/datasources/safety_exam_data_source.dart';
import 'package:flutter_demo/features/safety/data/datasources/remote_safety_exam_data_source.dart';
import 'package:flutter_demo/features/safety/data/repositories/safety_exam_repository_impl.dart';
import 'package:flutter_demo/features/safety/domain/repositories/safety_exam_repository.dart';
import 'package:flutter_demo/features/safety/domain/usecases/get_exam_questions_usecase.dart';
import 'package:flutter_demo/features/safety/domain/usecases/save_exam_record_usecase.dart';
import 'package:flutter_demo/features/safety/domain/usecases/get_exam_detail_usecase.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/safety_exam_bloc.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/exam_detail/exam_detail_bloc.dart';

// 教师端安全教育考试相关导入
import 'package:flutter_demo/features/safety/data/datasources/remote/teacher_safety_education_remote_data_source.dart';
import 'package:flutter_demo/features/safety/data/datasources/remote/teacher_safety_education_remote_data_source_impl.dart';
import 'package:flutter_demo/features/safety/data/repositories/teacher_safety_education_repository_impl.dart';
import 'package:flutter_demo/features/safety/domain/repositories/teacher_safety_education_repository.dart';
import 'package:flutter_demo/features/safety/domain/usecases/get_teacher_safety_education_data_usecase.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/teacher_safety_education/teacher_safety_education_bloc.dart';

/// 获取依赖注入实例
final GetIt getIt = GetIt.instance;

/// 注册安全教育模块依赖
void initSafetyInjection() {
  // 数据源
  getIt.registerLazySingleton<SafetyExamDataSource>(
    () => RemoteSafetyExamDataSource(
      dioClient: getIt<DioClient>(),
    ),
  );

  // 仓库
  getIt.registerLazySingleton<SafetyExamRepository>(
    () => SafetyExamRepositoryImpl(
      dataSource: getIt<SafetyExamDataSource>(),
    ),
  );

  // 用例
  getIt.registerLazySingleton(
    () => GetExamQuestionsUseCase(
      repository: getIt<SafetyExamRepository>(),
    ),
  );

  getIt.registerLazySingleton(
    () => SaveExamRecordUseCase(
      repository: getIt<SafetyExamRepository>(),
    ),
  );

  getIt.registerLazySingleton(
    () => GetExamDetailUseCase(
      repository: getIt<SafetyExamRepository>(),
    ),
  );

  // BLoC
  getIt.registerFactory(
    () => SafetyExamBloc(
      getExamQuestionsUseCase: getIt<GetExamQuestionsUseCase>(),
      saveExamRecordUseCase: getIt<SaveExamRecordUseCase>(),
    ),
  );

  getIt.registerFactory(
    () => ExamDetailBloc(
      getExamDetailUseCase: getIt<GetExamDetailUseCase>(),
    ),
  );

  // 教师端安全教育考试 - 数据源
  getIt.registerLazySingleton<TeacherSafetyEducationRemoteDataSource>(
    () => TeacherSafetyEducationRemoteDataSourceImpl(
      getIt<DioClient>(),
    ),
  );

  // 教师端安全教育考试 - 仓库
  getIt.registerLazySingleton<TeacherSafetyEducationRepository>(
    () => TeacherSafetyEducationRepositoryImpl(
      remoteDataSource: getIt<TeacherSafetyEducationRemoteDataSource>(),
      networkInfo: getIt<NetworkInfo>(),
    ),
  );

  // 教师端安全教育考试 - 用例
  getIt.registerLazySingleton(
    () => GetTeacherSafetyEducationDataUseCase(
      getIt<TeacherSafetyEducationRepository>(),
    ),
  );

  // 教师端安全教育考试 - BLoC
  getIt.registerFactory(
    () => TeacherSafetyEducationBloc(
      getTeacherSafetyEducationDataUseCase: getIt<GetTeacherSafetyEducationDataUseCase>(),
    ),
  );
}
