/// -----
/// free_internship_exempt_list_state.dart
///
/// 免实习申请列表BLoC状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../../domain/entities/free_internship_exempt.dart';

/// 免实习申请列表BLoC状态基类
abstract class FreeInternshipExemptListState extends Equatable {
  const FreeInternshipExemptListState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class FreeInternshipExemptListInitial extends FreeInternshipExemptListState {}

/// 加载中状态
class FreeInternshipExemptListLoading extends FreeInternshipExemptListState {}

/// 加载成功状态
class FreeInternshipExemptListLoaded extends FreeInternshipExemptListState {
  /// 免实习申请列表
  final List<FreeInternshipExempt> exempts;
  
  /// 实习计划ID
  final int planId;
  
  /// 申请类型（0:待审批，1:已审批）
  final int type;

  const FreeInternshipExemptListLoaded({
    required this.exempts,
    required this.planId,
    required this.type,
  });

  @override
  List<Object> get props => [exempts, planId, type];
}

/// 加载失败状态
class FreeInternshipExemptListError extends FreeInternshipExemptListState {
  /// 错误信息
  final String message;

  const FreeInternshipExemptListError({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

/// 刷新中状态
class FreeInternshipExemptListRefreshing extends FreeInternshipExemptListState {
  /// 当前的免实习申请列表（保持显示）
  final List<FreeInternshipExempt> exempts;
  
  /// 实习计划ID
  final int planId;
  
  /// 申请类型（0:待审批，1:已审批）
  final int type;

  const FreeInternshipExemptListRefreshing({
    required this.exempts,
    required this.planId,
    required this.type,
  });

  @override
  List<Object> get props => [exempts, planId, type];
}
