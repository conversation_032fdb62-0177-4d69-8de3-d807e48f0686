/// -----------------------------------------------------------------------------
///
/// 
/// 加载组件，提供统一的加载样式和动画
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

/// 加载组件
///
/// 提供统一的加载样式和动画，支持自定义消息和取消操作
class LoadingWidget extends StatelessWidget {
  /// 加载提示消息
  final String? message;

  /// 是否显示取消按钮
  final bool showCancelButton;

  /// 取消按钮点击回调
  final VoidCallback? onCancel;

  /// 加载指示器大小
  final double size;

  /// 加载指示器颜色
  final Color? color;

  /// 加载指示器粗细
  final double strokeWidth;

  /// 消息文本样式
  final TextStyle? messageStyle;

  /// 取消按钮文本
  final String cancelButtonText;

  /// 构造函数
  const LoadingWidget({
    Key? key,
    this.message,
    this.showCancelButton = false,
    this.onCancel,
    this.size = 40.0,
    this.color,
    this.strokeWidth = 4.0,
    this.messageStyle,
    this.cancelButtonText = '取消',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size,
            height: size,
            child: CircularProgressIndicator(
              strokeWidth: strokeWidth,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppTheme.primaryColor,
              ),
            ),
          ),
          if (message != null)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: Text(
                message!,
                style: messageStyle ??
                    TextStyle(
                      fontSize: 16,
                      color: Colors.grey[700],
                    ),
                textAlign: TextAlign.center,
              ),
            ),
          if (showCancelButton && onCancel != null)
            Padding(
              padding: const EdgeInsets.only(top: 16.0),
              child: TextButton(
                onPressed: onCancel,
                child: Text(
                  cancelButtonText,
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
