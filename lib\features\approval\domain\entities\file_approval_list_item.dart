/// -----
/// file_approval_list_item.dart
///
/// 文件审批列表项实体类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 文件审批列表项实体
class FileApprovalListItem extends Equatable {
  /// 描述
  final String description;
  
  /// 已经审批数量
  final int alreadyCount;
  
  /// 文件编号
  final int fileCode;
  
  /// 文件类型
  final String fileType;
  
  /// 应该审批数量
  final int shouldCount;

  const FileApprovalListItem({
    required this.description,
    required this.alreadyCount,
    required this.fileCode,
    required this.fileType,
    required this.shouldCount,
  });

  /// 获取统计文本（已审批/总数）
  String get statisticsText => '$alreadyCount/$shouldCount';

  /// 是否已完成审批
  bool get isCompleted => alreadyCount >= shouldCount;

  /// 获取进度百分比
  double get progressPercentage => shouldCount > 0 ? alreadyCount / shouldCount : 0.0;

  @override
  List<Object?> get props => [
        description,
        alreadyCount,
        fileCode,
        fileType,
        shouldCount,
      ];

  @override
  String toString() {
    return 'FileApprovalListItem{description: $description, alreadyCount: $alreadyCount, fileCode: $fileCode, fileType: $fileType, shouldCount: $shouldCount}';
  }
}
