/// -----
/// file_approval_repository.dart
///
/// 文件审批仓库接口
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/file_approval_list_item.dart';

/// 文件审批仓库接口
abstract class FileApprovalRepository {
  /// 获取文件审批列表
  /// 
  /// [planId] 实习计划ID
  /// 返回 [Either<Failure, List<FileApprovalListItem>>]
  Future<Either<Failure, List<FileApprovalListItem>>> getFileApprovalList(String planId);
}
