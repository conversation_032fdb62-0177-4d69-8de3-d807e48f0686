/// -----
/// plan_detail_event.dart
/// 
/// 实习计划详情页面事件定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习计划详情页面事件基类
abstract class PlanDetailEvent extends Equatable {
  const PlanDetailEvent();

  @override
  List<Object?> get props => [];
}

/// 加载实习计划详情事件
class LoadPlanDetailEvent extends PlanDetailEvent {
  /// 实习计划ID
  final String planId;

  const LoadPlanDetailEvent({required this.planId});

  @override
  List<Object?> get props => [planId];
}

/// 刷新实习计划详情事件
class RefreshPlanDetailEvent extends PlanDetailEvent {
  /// 实习计划ID
  final String planId;

  const RefreshPlanDetailEvent({required this.planId});

  @override
  List<Object?> get props => [planId];
}
