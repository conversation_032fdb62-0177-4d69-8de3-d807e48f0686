/// -----
/// internship_change_list_screen.dart
///
/// 实习信息变更申请列表页面，展示学生实习信息变更申请列表
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_change_detail_screen.dart';
import 'package:flutter_demo/features/internship/data/models/internship_change_model.dart';
import 'package:flutter_demo/core/constants/constants.dart';

class InternshipChangeListScreen extends StatefulWidget {
  const InternshipChangeListScreen({Key? key}) : super(key: key);

  @override
  State<InternshipChangeListScreen> createState() => _InternshipChangeListScreenState();
}

class _InternshipChangeListScreenState extends State<InternshipChangeListScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final String _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';

  // 分类后的申请列表
  late List<InternshipChangeModel> _pendingApplications;
  late List<InternshipChangeModel> _approvedApplications;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  void _loadData() {
    // 获取样例数据
    final List<InternshipChangeModel> allApplications = InternshipChangeModel.getSampleData();

    // 分类数据
    _pendingApplications = allApplications.where((app) => app.status == '待审批').toList();
    _approvedApplications = allApplications.where((app) => app.status != '待审批').toList();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: '实习信息变更申请',
        centerTitle: true,
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 课程头部
          CourseHeaderSection(
            courseName: _courseName,
            initialExpanded: false,
          ),
          // 自定义标签栏
          ApprovalTabBar(
            controller: _tabController,
            pendingCount: _pendingApplications.length,
          ),
          // 页面内容
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildChangeList(_pendingApplications),
                _buildChangeList(_approvedApplications),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChangeList(List<InternshipChangeModel> list) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: list.length,
      itemBuilder: (context, index) {
        final item = list[index];
        return _buildChangeListItem(item);
      },
    );
  }

  Widget _buildChangeListItem(InternshipChangeModel item) {
    final status = item.status;
    Color statusColor;
    switch (status) {
      case '待审批':
        statusColor = Colors.grey;
        break;
      case '已通过':
        statusColor = Colors.green;
        break;
      case '已驳回':
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }
    final avatarUrl = AppConstants.avatar1; // 可根据item.studentAvatar扩展
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[300],
                  ),
                  child: ClipOval(
                    child: avatarUrl.isNotEmpty
                        ? Image.network(
                            avatarUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                Image.asset(
                                  'assets/images/default_avatar.png',
                                  fit: BoxFit.cover,
                                ),
                          )
                        : Image.asset(
                            'assets/images/default_avatar.png',
                            fit: BoxFit.cover,
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  item.studentName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  status,
                  style: TextStyle(
                    fontSize: 14,
                    color: statusColor,
                  ),
                ),
              ],
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
            ),
            _buildInfoRow('实习单位', item.newCompany, highlight: item.oldCompany != item.newCompany),
            const SizedBox(height: 8),
            _buildInfoRow('部门/科室', '${item.oldDepartment} 变更为 ${item.newDepartment}', highlight: true),
            const SizedBox(height: 8),
            _buildInfoRow('实习岗位', '${item.oldPosition} 变更为 ${item.newPosition}', highlight: true),
            if (status == '待审批') ...[
              const SizedBox(height: 8),
              _buildInfoRow('变更理由', item.changeReason),
            ],
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  item.applyDate,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => InternshipChangeDetailScreen(changeId: item.id),
                      ),
                    ).then((_) {
                      setState(() {
                        _loadData();
                      });
                    });
                  },
                  child: const Row(
                    children: [
                      Text(
                        '查看',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppTheme.primaryColor,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool highlight = false}) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: highlight ? Colors.red : Colors.black87,
            ),
          ),
        ),
      ],
    );
  }
}