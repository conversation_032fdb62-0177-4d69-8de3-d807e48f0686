/// -----
/// internship_student_detail_model.dart
/// 
/// 实习生详情数据模型，用于存储实习生的详细信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

class InternshipStudentDetailModel {
  final String name;
  final String avatar;
  final String className;
  final String phone;
  final String schoolTeacher;
  final String schoolTeacherPhone;
  final String companyTeacher;
  final String companyTeacherPhone;
  final String emergencyContact;
  final String emergencyPhone;
  final InsuranceInfo insuranceInfo;
  final SafetyExamInfo safetyExamInfo;
  final List<AttachmentInfo> attachments;
  final SignInfo signInfo;
  final List<ReportInfo> reports;
  final List<CompanyInfo> companies;

  InternshipStudentDetailModel({
    required this.name,
    required this.avatar,
    required this.className,
    required this.phone,
    required this.schoolTeacher,
    required this.schoolTeacherPhone,
    required this.companyTeacher,
    required this.companyTeacherPhone,
    required this.emergencyContact,
    required this.emergencyPhone,
    required this.insuranceInfo,
    required this.safetyExamInfo,
    required this.attachments,
    required this.signInfo,
    required this.reports,
    required this.companies,
  });

  static InternshipStudentDetailModel getSampleData() {
    return InternshipStudentDetailModel(
      name: '刘备',
      avatar: '',
      className: '建筑工程5班',
      phone: '188 1234 5678',
      schoolTeacher: '张三',
      schoolTeacherPhone: '188 1234 5678',
      companyTeacher: '张三',
      companyTeacherPhone: '188 1234 5678',
      emergencyContact: '父亲',
      emergencyPhone: '188 1234 5678',
      insuranceInfo: InsuranceInfo(
        name: '全国职业院校学生实责任保险',
        policyNumber: '661504202341011100213',
        purchaser: '学校',
        type: '学生实习责任保险',
        startDate: '2023-07-01',
        endDate: '2024-06-30',
      ),
      safetyExamInfo: SafetyExamInfo(
        score: 80,
        hasDetail: true,
      ),
      attachments: [
        AttachmentInfo(name: '三方协议', url: ''),
        AttachmentInfo(name: '告家长通知书', url: ''),
      ],
      signInfo: SignInfo(
        totalDays: 300,
        signedDays: 53,
        supplementDays: 45,
        exemptDays: 3,
      ),
      reports: [
        ReportInfo(type: '日报', completed: 53, total: 150),
        ReportInfo(type: '周报', completed: 12, total: 26),
        ReportInfo(type: '月报', completed: 4, total: 6),
        ReportInfo(type: '实习总结', completed: 12, total: 12),
      ],
      companies: [
        CompanyInfo(
          companyName: '湖北省武汉市九州通中药材电子商务有限公司',
          companyCreditCode: '48987456365858761F',
          companyScale: '100-499人',
          companyRegion: '湖北省/武汉市/汉阳区',
          companyAddress: '王家湾汉阳大道108号',
          companyNature: '私营',
          companyIndustry: '中药材电子商务',
          companyContact: '周平樟',
          companyContactPhone: '18555632486',
        ),
        CompanyInfo(
          companyName: '湖北省武汉市九州通中药材电子商务有限公司',
          companyCreditCode: '48987456365858761F',
          companyScale: '100-499人',
          companyRegion: '湖北省/武汉市/汉阳区',
          companyAddress: '王家湾汉阳大道108号',
          companyNature: '私营',
          companyIndustry: '中药材电子商务',
          companyContact: '周平樟',
          companyContactPhone: '18555632486',
        ),
      ],
    );
  }
}

class InsuranceInfo {
  final String name;
  final String policyNumber;
  final String purchaser;
  final String type;
  final String startDate;
  final String endDate;

  InsuranceInfo({
    required this.name,
    required this.policyNumber,
    required this.purchaser,
    required this.type,
    required this.startDate,
    required this.endDate,
  });
}

class SafetyExamInfo {
  final int score;
  final bool hasDetail;

  SafetyExamInfo({
    required this.score,
    required this.hasDetail,
  });
}

class AttachmentInfo {
  final String name;
  final String url;

  AttachmentInfo({
    required this.name,
    required this.url,
  });
}

class SignInfo {
  final int totalDays;
  final int signedDays;
  final int supplementDays;
  final int exemptDays;

  SignInfo({
    required this.totalDays,
    required this.signedDays,
    required this.supplementDays,
    required this.exemptDays,
  });
}

class ReportInfo {
  final String type;
  final int completed;
  final int total;

  ReportInfo({
    required this.type,
    required this.completed,
    required this.total,
  });
}

class CompanyInfo {
  final String companyName;
  final String companyCreditCode;
  final String companyScale;
  final String companyRegion;
  final String companyAddress;
  final String companyNature;
  final String companyIndustry;
  final String companyContact;
  final String companyContactPhone;

  CompanyInfo({
    required this.companyName,
    required this.companyCreditCode,
    required this.companyScale,
    required this.companyRegion,
    required this.companyAddress,
    required this.companyNature,
    required this.companyIndustry,
    required this.companyContact,
    required this.companyContactPhone,
  });
}