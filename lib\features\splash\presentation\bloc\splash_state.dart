import 'package:equatable/equatable.dart';

/// 启动页状态基类
///
/// 所有启动页相关的状态都应继承此类
abstract class SplashState extends Equatable {
  const SplashState();

  @override
  List<Object?> get props => [];
}

/// 启动页初始状态
///
/// 表示启动页的初始状态
class SplashInitialState extends SplashState {}

/// 应用初始化中状态
///
/// 表示应用正在初始化
class AppInitializingState extends SplashState {}

/// 已认证状态
///
/// 表示用户已登录
class AuthenticatedState extends SplashState {
  final String userType;

  const AuthenticatedState({required this.userType});

  @override
  List<Object?> get props => [userType];
}

/// 未认证状态
///
/// 表示用户未登录或未完成角色认证
class UnauthenticatedState extends SplashState {}

/// 初始化失败状态
///
/// 表示应用初始化失败
class InitializationFailureState extends SplashState {
  final String message;

  const InitializationFailureState(this.message);

  @override
  List<Object?> get props => [message];
}
