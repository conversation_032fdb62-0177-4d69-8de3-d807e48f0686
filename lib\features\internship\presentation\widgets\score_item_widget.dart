/// -----
/// score_item_widget.dart
/// 
/// 评分项组件，用于展示实习成绩评分项
///
/// <AUTHOR>
/// @date 2025-05-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 评分项组件
///
/// 用于展示实习成绩评分项，包括评分项标题、权重、评分和评分按钮
class ScoreItemWidget extends StatelessWidget {
  /// 评分项标题
  final String title;

  /// 评分项权重
  final String weight;

  /// 评分项描述
  final String description;

  /// 评分项分数，如果为null则表示未评分
  final int? score;

  /// 评分项最大分数
  final int maxScore;

  /// 评分按钮点击回调
  final VoidCallback? onRatePressed;

  /// 是否可以评分
  final bool canRate;

  const ScoreItemWidget({
    Key? key,
    required this.title,
    required this.weight,
    required this.description,
    this.score,
    this.maxScore = 10,
    this.onRatePressed,
    this.canRate = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final bool isRated = score != null;

    return Container(
      margin: EdgeInsets.only(top: 20.h), // 列表项间距20px
      padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 39.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Stack(
        children: [
          // 内容区域
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和权重行
              Row(
                children: [
                  // 标题
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 30.sp,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.black333,
                    ),
                  ),
                  SizedBox(width: 20.w),
                  Container(width: 1, height: 24.h, color: AppTheme.grayE5),
                  SizedBox(width: 20.w),
                  // 权重
                  Text(
                    weight,
                    style: TextStyle(
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w500,
                      color: AppTheme.primaryColor,
                    ),
                  ),
                ],
              ),

              // 描述
              Padding(
                padding: EdgeInsets.only(top: 16.h, bottom: 0.h, right: 180.w), // 右侧留出评分按钮的宽度
                child: Text(
                  description,
                  style: TextStyle(
                    fontSize: 22.sp,
                    color: AppTheme.black666,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
          
          // 评分或评分按钮 - 固定在右侧垂直居中位置
          Positioned(
            right: 10.w, // 与父容器的右内边距一致
            top: 0,
            bottom: 0,
            child: Center(
              child: isRated
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      textBaseline: TextBaseline.ideographic,
                      children: [
                        Text(
                          '$score',
                          style: TextStyle(
                            fontSize: 72.sp,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '分',
                          style: TextStyle(
                            fontSize: 22.sp,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                      ],
                    )
                  : (canRate && onRatePressed != null)
                      ? SizedBox(
                          width: 120.w,
                          height: 48.h,
                          child: OutlinedButton(
                            onPressed: onRatePressed,
                            style: OutlinedButton.styleFrom(
                              backgroundColor: Colors.white,
                              foregroundColor: AppTheme.primaryColor,
                              side: BorderSide(
                                color: AppTheme.primaryColor,
                                width: 2.w,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              padding: EdgeInsets.zero,
                            ),
                            child: Text(
                              '请评分',
                              style: TextStyle(
                                fontSize: 24.sp,
                                fontWeight: FontWeight.normal,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ),
                        )
                      : const SizedBox.shrink(),
            ),
          ),
        ],
      ),
    );
  }
}
