/// -----------------------------------------------------------------------------
/// internship_plan_info.dart
/// 
/// 实习计划信息模型，包含实习类型、周期、学期等信息
/// 
/// <AUTHOR>
/// @date 2025-04-10
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

class InternshipPlanInfo {
  final String id;
  final String title;
  final String type;
  final String period;
  final String semester;
  final String creator;
  final String status;
  final bool canApply;
  final bool canExempt;

  InternshipPlanInfo({
    this.id = '',
    required this.title,
    required this.type,
    required this.period,
    required this.semester,
    required this.creator,
    required this.status,
    this.canApply = true,
    this.canExempt = true,
  });

  factory InternshipPlanInfo.fromJson(Map<String, dynamic> json) {
    return InternshipPlanInfo(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      type: json['type'] ?? '',
      period: json['period'] ?? '',
      semester: json['semester'] ?? '',
      creator: json['creator'] ?? '',
      status: json['status'] ?? '',
      canApply: json['canApply'] ?? true,
      canExempt: json['canExempt'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'period': period,
      'semester': semester,
      'creator': creator,
      'status': status,
      'canApply': canApply,
      'canExempt': canExempt,
    };
  }

  static List<InternshipPlanInfo> sampleDataList() {
    return [
      InternshipPlanInfo(
        id: '12349884',
        title: '2021级市场销售2023-2024实习学年第二学期岗位实习',
        type: '岗位实习',
        period: '2025,04,25-2025.08.14',
        semester: '2024-2025实习学年第一学期',
        creator: '张三',
        status: '进行中',
      ),
      InternshipPlanInfo(
        id: '12349885',
        title: '2021级市场销售2023-2024实习学年第二学期岗位实习',
        type: '岗位实习',
        period: '2025,04,25-2025.08.14',
        semester: '2024-2025实习学年第一学期',
        creator: '张三',
        status: '进行中',
      ),
      InternshipPlanInfo(
        id: '12349886',
        title: '2021级市场销售2023-2024实习学年第二学期岗位实习',
        type: '岗位实习',
        period: '2025,04,25-2025.08.14',
        semester: '2024-2025实习学年第一学期',
        creator: '张三',
        status: '进行中',
      ),
    ];
  }
} 