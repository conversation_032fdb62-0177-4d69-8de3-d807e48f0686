/// -----
/// file_type_mapper.dart
///
/// 文件类型工具类 - 负责图标映射和文件ID生成
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 文件类型信息（简化版）
class FileTypeInfo {
  final String fileId;
  final String iconPath;

  const FileTypeInfo({
    required this.fileId,
    required this.iconPath,
  });
}

/// 文件类型工具类 - 简化版
class FileTypeMapper {
  /// 图标映射规则 - 基于关键词匹配（按优先级排序）
  static const Map<String, String> _iconMappingRules = {
    // 申请表类文件 - 橙色图标（优先级最高，避免被其他规则覆盖）
    '申请表': 'assets/images/upload_orange_pdf_icon.png',
    '申请': 'assets/images/upload_orange_pdf_icon.png',

    // 保险类文件 - 紫色图标
    '保险': 'assets/images/upload_purple_pdf_icon.png',

    // 通知书类文件 - 粉色图标
    '知情同意': 'assets/images/upload_pink_pdf_icon.png',
    '家长': 'assets/images/upload_pink_pdf_icon.png',
    '通知': 'assets/images/upload_pink_pdf_icon.png',

    // 协议类文件 - 蓝色图标
    '协议': 'assets/images/upload_blue_doc_icon.png',
    '合同': 'assets/images/upload_blue_doc_icon.png',
  };

  /// 默认图标
  static const String _defaultIcon = 'assets/images/upload_blue_doc_icon.png';

  /// 根据文件类型获取文件信息
  static FileTypeInfo getFileTypeInfo(String fileType) {
    return FileTypeInfo(
      fileId: _generateFileId(fileType),
      iconPath: _getIconPath(fileType),
    );
  }

  /// 根据文件类型获取图标路径
  static String _getIconPath(String fileType) {
    // 遍历图标映射规则，查找匹配的关键词
    for (final entry in _iconMappingRules.entries) {
      if (fileType.contains(entry.key)) {
        return entry.value;
      }
    }
    // 如果没有匹配的关键词，返回默认图标
    return _defaultIcon;
  }

  /// 根据文件类型生成文件ID
  static String _generateFileId(String fileType) {
    if (fileType.isEmpty) {
      return 'unknown_file';
    }

    // 简化的文件ID生成逻辑 - 基于文件类型的哈希值
    final hash = fileType.hashCode.abs().toString();
    return 'file_type_$hash';
  }

  /// 获取所有支持的图标映射关键词
  static List<String> getSupportedKeywords() {
    return _iconMappingRules.keys.toList();
  }
}
