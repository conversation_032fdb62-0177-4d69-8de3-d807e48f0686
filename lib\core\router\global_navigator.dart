/// -----
/// global_navigator.dart
/// 
/// 全局导航器，提供无Context的导航功能
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../utils/logger.dart';
import 'route_constants.dart';

/// 全局导航器
/// 
/// 提供无需BuildContext的全局导航功能
class GlobalNavigator {
  static const String _tag = 'GlobalNavigator';
  
  /// 全局导航器键
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  /// 获取当前上下文
  static BuildContext? get currentContext => navigatorKey.currentContext;
  
  /// 跳转到登录页面
  /// 
  /// 清除导航栈并跳转到登录页面
  static Future<void> goToLogin() async {
    Logger.info(_tag, '全局跳转到登录页面');
    
    final context = currentContext;
    if (context != null && context.mounted) {
      try {
        context.go(AppRoutes.login);
        Logger.info(_tag, '成功跳转到登录页面');
      } catch (e, s) {
        Logger.error(
          _tag,
          '跳转到登录页面失败',
          exception: e,
          stackTrace: s,
        );
      }
    } else {
      Logger.warning(_tag, '无法获取有效的导航上下文');
    }
  }
  
  /// 跳转到首页
  /// 
  /// 清除导航栈并跳转到首页
  static Future<void> goToHome() async {
    Logger.info(_tag, '全局跳转到首页');
    
    final context = currentContext;
    if (context != null && context.mounted) {
      try {
        context.go(AppRoutes.home);
        Logger.info(_tag, '成功跳转到首页');
      } catch (e, s) {
        Logger.error(
          _tag,
          '跳转到首页失败',
          exception: e,
          stackTrace: s,
        );
      }
    } else {
      Logger.warning(_tag, '无法获取有效的导航上下文');
    }
  }
  
  /// 返回上一页
  /// 
  /// 如果可以返回则返回，否则跳转到首页
  static Future<void> goBack() async {
    Logger.info(_tag, '全局返回上一页');
    
    final context = currentContext;
    if (context != null && context.mounted) {
      try {
        if (context.canPop()) {
          context.pop();
          Logger.info(_tag, '成功返回上一页');
        } else {
          await goToHome();
        }
      } catch (e, s) {
        Logger.error(
          _tag,
          '返回上一页失败',
          exception: e,
          stackTrace: s,
        );
      }
    } else {
      Logger.warning(_tag, '无法获取有效的导航上下文');
    }
  }
  
  /// 显示全局SnackBar
  /// 
  /// [message] - 要显示的消息
  /// [isError] - 是否为错误消息
  static void showSnackBar(String message, {bool isError = false}) {
    Logger.info(_tag, '显示全局SnackBar: $message');
    
    final context = currentContext;
    if (context != null && context.mounted) {
      try {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(message),
            backgroundColor: isError ? Colors.red : null,
            duration: Duration(seconds: isError ? 4 : 2),
          ),
        );
        Logger.info(_tag, '成功显示SnackBar');
      } catch (e, s) {
        Logger.error(
          _tag,
          '显示SnackBar失败',
          exception: e,
          stackTrace: s,
        );
      }
    } else {
      Logger.warning(_tag, '无法获取有效的上下文显示SnackBar');
    }
  }
}
