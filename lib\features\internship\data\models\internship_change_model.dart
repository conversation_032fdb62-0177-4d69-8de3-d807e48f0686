/// -----
/// internship_change_model.dart
/// 
/// 实习信息变更申请模型，用于存储实习信息变更申请的数据
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

class InternshipChangeModel {
  final String id;
  final String studentName;
  final String studentAvatar;
  final String phoneNumber;
  final String oldCompany;
  final String newCompany;
  final String oldDepartment;
  final String newDepartment;
  final String oldPosition;
  final String newPosition;
  final String changeReason;
  final String applyDate;
  final String status;
  final List<Map<String, String>> attachments;

  InternshipChangeModel({
    required this.id,
    required this.studentName,
    required this.studentAvatar,
    required this.phoneNumber,
    required this.oldCompany,
    required this.newCompany,
    required this.oldDepartment,
    required this.newDepartment,
    required this.oldPosition,
    required this.newPosition,
    required this.changeReason,
    required this.applyDate,
    this.status = '待审批',
    this.attachments = const [],
  });

  factory InternshipChangeModel.fromJson(Map<String, dynamic> json) {
    return InternshipChangeModel(
      id: json['id'] ?? '',
      studentName: json['studentName'] ?? '',
      studentAvatar: json['studentAvatar'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      oldCompany: json['oldCompany'] ?? '',
      newCompany: json['newCompany'] ?? '',
      oldDepartment: json['oldDepartment'] ?? '',
      newDepartment: json['newDepartment'] ?? '',
      oldPosition: json['oldPosition'] ?? '',
      newPosition: json['newPosition'] ?? '',
      changeReason: json['changeReason'] ?? '',
      applyDate: json['applyDate'] ?? '',
      status: json['status'] ?? '待审批',
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((e) => Map<String, String>.from(e as Map))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentName': studentName,
      'studentAvatar': studentAvatar,
      'phoneNumber': phoneNumber,
      'oldCompany': oldCompany,
      'newCompany': newCompany,
      'oldDepartment': oldDepartment,
      'newDepartment': newDepartment,
      'oldPosition': oldPosition,
      'newPosition': newPosition,
      'changeReason': changeReason,
      'applyDate': applyDate,
      'status': status,
      'attachments': attachments,
    };
  }

  static List<InternshipChangeModel> getSampleData() {
    return [
      InternshipChangeModel(
        id: '1',
        studentName: '刘备',
        studentAvatar: '',
        phoneNumber: '18373612345',
        oldCompany: '深圳市腾讯计算机系统有限责任公司',
        newCompany: '深圳市腾讯计算机系统有限责任公司',
        oldDepartment: '技术部',
        newDepartment: '产品部',
        oldPosition: 'java开发工程师',
        newPosition: '产品经理',
        changeReason: '更好的选择',
        applyDate: '2025.03.12 16:20',
        status: '待审批',
        attachments: [
          {'name': '三方协议', 'url': ''},
          {'name': '告家长通知书', 'url': ''},
        ],
      ),
      InternshipChangeModel(
        id: '2',
        studentName: '刘备',
        studentAvatar: '',
        phoneNumber: '18373612345',
        oldCompany: '深圳市腾讯计算机系统有限责任公司',
        newCompany: '深圳市腾讯计算机系统有限责任公司',
        oldDepartment: '技术部',
        newDepartment: '产品部',
        oldPosition: 'java开发工程师',
        newPosition: '产品经理',
        changeReason: '更好的选择',
        applyDate: '2025.03.12 16:20',
        status: '待审批',
        attachments: [
          {'name': '三方协议', 'url': ''},
        ],
      ),
      InternshipChangeModel(
        id: '3',
        studentName: '刘备',
        studentAvatar: '',
        phoneNumber: '18373612345',
        oldCompany: '深圳市腾讯计算机系统有限责任公司',
        newCompany: '深圳市腾讯计算机系统有限责任公司',
        oldDepartment: '技术部',
        newDepartment: '产品部',
        oldPosition: 'java开发工程师',
        newPosition: '产品经理',
        changeReason: '更好的选择',
        applyDate: '2025.03.12 16:20',
        status: '待审批',
        attachments: [],
      ),
      InternshipChangeModel(
        id: '4',
        studentName: '刘备',
        studentAvatar: '',
        phoneNumber: '18373612345',
        oldCompany: '深圳市腾讯计算机系统有限责任公司',
        newCompany: '深圳市腾讯计算机系统有限责任公司',
        oldDepartment: '技术部',
        newDepartment: '产品部',
        oldPosition: 'java开发工程师',
        newPosition: '产品经理',
        changeReason: '更好的选择',
        applyDate: '2025.03.10 14:30',
        status: '已通过',
        attachments: [],
      ),
      InternshipChangeModel(
        id: '5',
        studentName: '关羽',
        studentAvatar: '',
        phoneNumber: '18373698765',
        oldCompany: '阿里巴巴（中国）有限公司',
        newCompany: '阿里巴巴（中国）有限公司',
        oldDepartment: '运维部',
        newDepartment: '技术部',
        oldPosition: '运维工程师',
        newPosition: '后端开发',
        changeReason: '个人发展需求',
        applyDate: '2025.03.09 10:15',
        status: '已驳回',
        attachments: [],
      ),
    ];
  }
} 