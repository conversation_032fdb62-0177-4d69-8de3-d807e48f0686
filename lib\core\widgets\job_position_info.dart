/// -----
/// job_position_info.dart
/// 
/// 岗位信息组件，用于显示就业岗位相关的信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';

class JobPositionInfo extends StatelessWidget {
  final String jobType;
  final String jobAgreement;
  final String department;
  final String position;
  final double? salary;
  final String? trainingFee;
  final String? positionCategory;
  final bool isProfessionalMatch;
  final String? agreementNumber;

  const JobPositionInfo({
    Key? key,
    required this.jobType,
    required this.jobAgreement,
    required this.department,
    required this.position,
    this.salary,
    this.trainingFee,
    this.positionCategory,
    this.isProfessionalMatch = true,
    this.agreementNumber,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('岗位信息'),
          _buildInfoRow('就业类别', jobType),
          _buildDivider(),
          _buildInfoRow('就业协议', jobAgreement),
          _buildDivider(),
          _buildInfoRow('部门', department),
          _buildDivider(),
          _buildInfoRow('岗位名称', position),
          if (salary != null) ...[
            _buildDivider(),
            _buildInfoRow('实习薪酬', salary.toString()),
          ],
          if (trainingFee != null) ...[
            _buildDivider(),
            _buildInfoRow('实习补贴', trainingFee!),
          ],
          if (positionCategory != null) ...[
            _buildDivider(),
            _buildInfoRow('岗位类别', positionCategory!),
          ],
          _buildDivider(),
          _buildInfoRow('专业匹配', isProfessionalMatch ? '是' : '否'),
          if (agreementNumber != null) ...[
            _buildDivider(),
            _buildInfoRow('协议书编号', agreementNumber!),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 0.5,
      color: Colors.grey[300],
      indent: 16,
      endIndent: 16,
    );
  }
} 