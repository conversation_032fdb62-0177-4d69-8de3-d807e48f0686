/// -----
/// upload_file_usecase.dart
///
/// 文件上传用例，处理文件上传的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/file_upload_repository.dart';

/// 文件上传用例
class UploadFileUseCase implements UseCase<UploadFileResult, UploadFileParams> {
  final FileUploadRepository repository;

  UploadFileUseCase(this.repository);

  @override
  Future<Either<Failure, UploadFileResult>> call(UploadFileParams params) async {
    return await repository.uploadFile(
      filePath: params.filePath,
      fileName: params.fileName,
      fileType: params.fileType,
      planId: params.planId,
      fileCode: params.fileCode,
      onProgress: params.onProgress,
    );
  }
}

/// 文件上传参数
class UploadFileParams {
  /// 文件路径
  final String filePath;
  /// 文件名
  final String fileName;
  /// 文件类型
  final String fileType;
  /// 计划ID
  final int planId;
  /// 文件代码
  final int fileCode;
  /// 上传进度回调
  final Function(double progress, String stepDescription)? onProgress;

  UploadFileParams({
    required this.filePath,
    required this.fileName,
    required this.fileType,
    required this.planId,
    required this.fileCode,
    this.onProgress,
  });
}

/// 文件上传结果
class UploadFileResult {
  /// 文件名
  final String fileName;
  /// 文件URL
  final String fileUrl;
  /// 保存结果ID
  final int saveResultId;

  UploadFileResult({
    required this.fileName,
    required this.fileUrl,
    required this.saveResultId,
  });
}
