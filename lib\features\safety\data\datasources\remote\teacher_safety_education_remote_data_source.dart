/// -----
/// teacher_safety_education_remote_data_source.dart
/// 
/// 教师端安全教育考试远程数据源接口
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions/server_exception.dart';

import '../../models/teacher_safety_education_response_model.dart';

/// 教师端安全教育考试远程数据源接口
/// 
/// 定义从远程API获取教师端安全教育考试数据的方法
abstract class TeacherSafetyEducationRemoteDataSource {
  /// 获取教师端安全教育考试数据
  /// 
  /// [planId] 实习计划ID
  /// 
  /// 返回包含统计数据和学生列表的响应模型
  /// 
  /// 抛出 [ServerException] 当网络请求失败时
  Future<TeacherSafetyEducationResponseModel> getTeacherSafetyEducationData(String planId);
}
