/// -----
/// reset_password_bloc.dart
///
/// 重置密码Bloc，处理重置密码的状态管理
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/send_verification_code_usecase.dart';
import '../../../domain/usecases/reset_password_usecase.dart';
import 'reset_password_event.dart';
import 'reset_password_state.dart';

/// 重置密码Bloc
///
/// 处理重置密码的状态管理
class ResetPasswordBloc extends Bloc<ResetPasswordEvent, ResetPasswordState> {
  final SendVerificationCodeUseCase sendVerificationCodeUseCase;
  final ResetPasswordUseCase resetPasswordUseCase;

  ResetPasswordBloc({
    required this.sendVerificationCodeUseCase,
    required this.resetPasswordUseCase,
  }) : super(ResetPasswordInitial()) {
    on<SendVerificationCodeEvent>(_onSendVerificationCode);
    on<ResetPasswordSubmittedEvent>(_onResetPassword);
  }

  Future<void> _onSendVerificationCode(
    SendVerificationCodeEvent event,
    Emitter<ResetPasswordState> emit,
  ) async {
    emit(ResetPasswordLoading());
    try {
      final result = await sendVerificationCodeUseCase(
        SendVerificationCodeParams(
          phone: event.phone,
          deviceId: event.deviceId,
        ),
      );
      result.fold(
        (failure) => emit(ResetPasswordError(failure.message)),
        (response) => emit(VerificationCodeSent(response)),
      );
    } on Exception catch (e) {
      emit(ResetPasswordError(e.toString()));
    }
  }

  Future<void> _onResetPassword(
    ResetPasswordSubmittedEvent event,
    Emitter<ResetPasswordState> emit,
  ) async {
    emit(ResetPasswordLoading());
    try {
      final result = await resetPasswordUseCase(
        ResetPasswordParams(
          phone: event.phone,
          code: event.code,
          newPassword: event.newPassword,
          deviceId: event.deviceId,
        ),
      );
      result.fold(
        (failure) => emit(ResetPasswordError(failure.message)),
        (response) => emit(PasswordResetSuccess(response)),
      );
    } on Exception catch (e) {
      emit(ResetPasswordError(e.toString()));
    }
  }
}