import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import '../../../../core/config/injection/injection.dart';
import '../../../../core/router/app_navigator.dart';
import '../bloc/login/login_bloc.dart';
import '../bloc/login/login_event.dart';
import '../bloc/login/login_state.dart';
import '../widgets/login_form.dart';

/// 登录页面
///
/// 显示登录表单，处理登录逻辑
class LoginScreen extends StatelessWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => getIt<LoginBloc>(),
      child: const LoginView(),
    );
  }
}

/// 登录视图
///
/// 登录页面的内容部分
class LoginView extends StatelessWidget {
  const LoginView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<LoginBloc, LoginState>(
        listener: (context, state) {
          if (state is LoginSuccessState) {
            // 登录成功，根据用户类型导航到不同页面
            if (state.user.userType != null) {
              // 用户类型有值，导航到首页
              AppNavigator.goToHome(context);
            } else {
              // 用户类型没有值，导航到身份验证页面
              AppNavigator.goToIdentityVerification(context);
            }
          } else if (state is LoginFailureState) {
            // 登录失败，显示错误消息
            AppSnackBar.showError(context, state.message);
          }
        },
        builder: (context, state) {
          if (state is LoginLoadingState) {
            return const Center(child: CircularProgressIndicator());
          }

          return LoginForm(
            onLogin: (phone, password) {
              context.read<LoginBloc>().add(
                    LoginRequestEvent(
                      phone: phone,
                      password: password,
                    ),
                  );
            },
            onRegister: () {
              AppNavigator.goToRegister(context);
            },
            onForgotPassword: () {
              AppNavigator.goToResetPassword(context);
            },
          );
        },
      ),
    );
  }
}
