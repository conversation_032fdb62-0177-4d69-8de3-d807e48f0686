---
trigger: always_on
---

# 页面开发规范

## 1. 缺省页规范

### 1.1 类型划分
缺省页应根据不同情况划分为以下几种类型：

- **空数据缺省页**：当没有数据时显示
- **网络错误缺省页**：当网络连接失败时显示
- **服务器错误缺省页**：当服务器返回错误时显示
- **权限错误缺省页**：当用户没有权限访问内容时显示
- **搜索无结果缺省页**：当搜索没有结果时显示

### 1.2 实现方式
使用 `EmptyStateWidget` 组件，该组件位于 `lib/core/widgets/empty_state_widget.dart`。

### 1.3 交互设计
- 提供明确的用户操作指引
- 包含操作按钮（如"重试"、"返回"等）

### 1.4 使用示例
```dart
EmptyStateWidget(
  icon: Icons.inbox,
  title: '暂无内容',
  message: '当前分类下暂无内容，请稍后再试',
  buttonText: '刷新',
  onButtonPressed: () => context.read<ContentBloc>().add(RefreshContentEvent()),
)
```

## 2. 页面加载状态规范

### 2.1 加载状态类型

#### 2.1.1 初始加载状态
- 页面首次加载时显示
- 使用骨架屏或加载指示器
- 避免显示空白页面

#### 2.1.2 内容刷新状态
- 下拉刷新时显示
- 使用顶部加载指示器
- 保持现有内容可见

#### 2.1.3 分页加载状态
- 加载更多数据时显示
- 使用底部加载指示器
- 显示当前加载状态（如"加载中..."）



### 2.3 加载指示器
- 使用 `CircularProgressIndicator` 或 `LinearProgressIndicator`
- 保持一致的加载动画样式
- 提供加载状态文本说明（可选）

### 2.4 状态管理
- 在 BLoC 中使用 `LoadingState` 状态控制显示和隐藏
- 设置超时处理（默认 30 秒后转为错误状态）
- 提供取消加载的选项（特别是耗时操作）


### 2.6 加载状态最佳实践

1. **即时反馈**：在用户操作后100ms内显示加载状态
2. **渐进式加载**：先显示骨架屏，再加载实际内容
3. **错误处理**：加载失败时提供重试选项
4. **性能优化**：
   - 预加载下一页数据
   - 使用 `KeepAlive` 缓存已加载页面
   - 避免不必要的重建

### 2.7 加载状态交互
- 提供取消加载的按钮（长时间加载时）
- 显示加载进度（如果可获取）
- 在弱网环境下优化加载体验

## 3. 列表页刷新加载规范

### 3.1 推荐第三方库
- **下拉刷新和上拉加载**：`pull_to_refresh` 或 `flutter_easyrefresh`
- **滚动检测**：`visibility_detector` 或 `flutter_visibility_detector`
- **滚动控制**：`scrollable_positioned_list` 用于滚动到特定位置

### 3.2 标准实现方式

#### 3.2.1 页面结构
```dart
class InternshipListView extends StatefulWidget {
  const InternshipListView({Key? key}) : super(key: key);

  @override
  _InternshipListViewState createState() => _InternshipListViewState();
}

class _InternshipListViewState extends State<InternshipListView> {
  final RefreshController _refreshController = RefreshController();

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh() {
    context.read<InternshipListBloc>().add(RefreshInternshipListEvent());
  }

  void _onLoadMore() {
    context.read<InternshipListBloc>().add(LoadMoreInternshipListEvent());
  }
  
  // ... 其他代码
}
```

#### 3.2.2 状态处理
```dart
BlocConsumer<InternshipListBloc, InternshipListState>(
  listener: (context, state) {
    if (state is InternshipListRefreshSuccess) {
      _refreshController.refreshCompleted();
    } else if (state is InternshipListRefreshFailure) {
      _refreshController.refreshFailed();
      // 显示错误提示
    }
    // 其他状态处理...
  },
  builder: (context, state) {
    // 状态渲染逻辑
  },
)
```

### 3.3 BLoC 状态管理集成

#### 3.3.1 事件定义
```dart
abstract class InternshipListEvent extends Equatable {
  const InternshipListEvent();
  @override
  List<Object?> get props => [];
}

class LoadInternshipListEvent extends InternshipListEvent {}
class RefreshInternshipListEvent extends InternshipListEvent {}
class LoadMoreInternshipListEvent extends InternshipListEvent {}
```

#### 3.3.2 状态定义
```dart
abstract class InternshipListState extends Equatable {
  const InternshipListState();
  @override
  List<Object?> get props => [];
}

class InternshipListInitial extends InternshipListState {}

class InternshipListLoading extends InternshipListState {
  final List<Internship> oldInternships;
  final bool isFirstFetch;

  const InternshipListLoading({
    this.oldInternships = const [],
    this.isFirstFetch = false,
  });
}

class InternshipListSuccess extends InternshipListState {
  final List<Internship> internships;
  final bool hasReachedMax;

  const InternshipListSuccess({
    required this.internships,
    this.hasReachedMax = false,
  });
}
```

## 4. 通用规范

### 4.1 应用栏
- 统一使用 `CustomAppBar` 组件
- 禁止直接使用 Flutter 原生的 `AppBar`
- 支持自定义标题、返回按钮、操作按钮等

### 4.2 提示信息
- 使用 `AppSnackBar` 工具类显示提示信息
- 根据提示类型使用对应的方法（成功、错误、警告等）

### 4.3 错误处理
- 统一处理网络错误、服务器错误等异常情况
- 在 BLoC 中捕获异常并转换为对应的状态
- 在 UI 层展示友好的错误提示

## 5. 性能优化建议

1. 使用 `const` 构造函数创建静态小部件
2. 为列表项添加 `Key`
3. 使用 `ListView.builder` 实现懒加载
4. 避免在 `build` 方法中创建大量对象
5. 使用 `AutomaticKeepAliveClientMixin` 保持页面状态