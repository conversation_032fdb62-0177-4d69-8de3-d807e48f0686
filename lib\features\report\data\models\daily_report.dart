/// -----
/// daily_report.dart
/// 
/// 日报模型，定义日报特有的属性
/// 
/// <AUTHOR>
/// @date 2025-06-17
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';

class DailyReport extends BaseReport {
  final DateTime reportDate;
  final String workContent;
  final String learningContent;
  final String problems;
  final String plans;

  const DailyReport({
    required super.id,
    required super.userId,
    required super.userName,
    required super.courseName,
    super.title,
    required super.createdAt,
    required super.status,
    super.isLate = false,
    super.rating,
    super.teacherComment,
    super.teacherName,
    super.commentTime,
    required this.reportDate,
    required this.workContent,
    required this.learningContent,
    required this.problems,
    required this.plans,
    super.infoTitle,
    super.contentTitle
  });

  @override
  String get content => workContent;

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'courseName': courseName,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'status': status.toString(),
      'isLate': isLate,
      'rating': rating,
      'teacherComment': teacherComment,
      'teacherName': teacherName,
      'commentTime': commentTime?.toIso8601String(),
      'reportDate': reportDate.toIso8601String(),
      'workContent': workContent,
      'learningContent': learningContent,
      'problems': problems,
      'plans': plans,
      'infoTitle': infoTitle,
      'contentTitle': contentTitle,
    };
  }

  factory DailyReport.fromJson(Map<String, dynamic> json) => DailyReport(
    id: json['id'],
    status: ReportStatus.values.firstWhere(
          (e) => e.toString() == json['status'],
    ),
    userId: json['userId'],
    userName: json['userName'],
    createdAt: DateTime.parse(json['createdAt']),
    reportDate: DateTime.parse(json['reportDate']),
    workContent: json['workContent'],
    learningContent: json['learningContent'],
    problems: json['problems'],
    plans: json['plans'],
    courseName: json['courseName'],
    infoTitle: json['infoTitle'],
    contentTitle: json['contentTitle'],
  );
}