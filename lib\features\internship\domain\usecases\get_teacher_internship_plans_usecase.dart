/// -----
/// get_teacher_internship_plans_usecase.dart
/// 
/// 获取教师实习计划列表用例
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/utils/logger.dart';
import '../entities/internship_plan.dart';
import '../repositories/internship_plan_repository.dart';

/// 获取教师实习计划列表用例
///
/// 封装获取教师实习计划列表的业务逻辑
class GetTeacherInternshipPlansUseCase implements UseCase<List<InternshipPlan>, NoParams> {
  final InternshipPlanRepository _repository;

  GetTeacherInternshipPlansUseCase(this._repository);

  @override
  Future<Either<Failure, List<InternshipPlan>>> call(NoParams params) async {
    Logger.info('GetTeacherInternshipPlansUseCase', '执行获取教师实习计划列表用例');

    final result = await _repository.getTeacherInternshipPlans();

    return result.fold(
      (failure) {
        Logger.error('GetTeacherInternshipPlansUseCase', '获取实习计划列表失败: ${failure.message}');
        return Left(failure);
      },
      (plans) {
        Logger.info('GetTeacherInternshipPlansUseCase', '成功获取${plans.length}个实习计划');
        return Right(plans);
      },
    );
  }
}
