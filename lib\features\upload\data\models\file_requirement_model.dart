/// -----
/// file_requirement_model.dart
/// 
/// 文件要求数据模型，用于API响应的数据映射
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/entities/file_requirement.dart';

/// 文件要求数据模型
/// 
/// 用于API响应的数据映射，继承自FileRequirement实体
class FileRequirementModel extends FileRequirement {
  const FileRequirementModel({
    super.id,
    required super.fileType,
    required super.fileCode,
    required super.fileStatus,
  });

  /// 从JSON创建FileRequirementModel
  factory FileRequirementModel.fromJson(Map<String, dynamic> json) {
    return FileRequirementModel(
      id: json['id']?.toString(),
      fileType: json['fileType'] ?? '',
      fileCode: json['fileCode'] ?? 0,
      fileStatus: FileStatus.fromValue(json['fileStatus'] ?? 0),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fileType': fileType,
      'fileCode': fileCode,
      'fileStatus': fileStatus.value,
    };
  }

  /// 转换为实体
  FileRequirement toEntity() {
    return FileRequirement(
      id: id,
      fileType: fileType,
      fileCode: fileCode,
      fileStatus: fileStatus,
    );
  }

  /// 从实体创建模型
  factory FileRequirementModel.fromEntity(FileRequirement entity) {
    return FileRequirementModel(
      id: entity.id,
      fileType: entity.fileType,
      fileCode: entity.fileCode,
      fileStatus: entity.fileStatus,
    );
  }

  @override
  String toString() {
    return 'FileRequirementModel(id: $id, fileType: $fileType, fileCode: $fileCode, fileStatus: $fileStatus)';
  }
}
