/// -----
/// internship_info_section.dart
///
/// 实习信息部分组件
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/data/models/internship_info.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_event.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/text_field.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/dropdown_field.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/location_field.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/date_field.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 实习信息部分组件
class InternshipInfoSection extends StatelessWidget {
  /// 实习信息
  final InternshipInfo internshipInfo;

  const InternshipInfoSection({
    super.key,
    required this.internshipInfo,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Text(
            '实习信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
        ),

        // 岗位开始时间
        DateField(
          label: '岗位开始时间',
          value: internshipInfo.startDate,
          placeholder: '请选择岗位开始时间',
          onChanged: (value) => _updateInternshipInfo(context, 'startDate', value),
        ),

        // 岗位结束时间
        DateField(
          label: '岗位结束时间',
          value: internshipInfo.endDate,
          placeholder: '请选择岗位结束时间',
          onChanged: (value) => _updateInternshipInfo(context, 'endDate', value),
        ),

        // 实习方式
        DropdownField(
          label: '实习方式',
          value: internshipInfo.internshipType,
          items: const ['校内实习', '自主联系', '统一分配'],
          placeholder: '请选择实习方式',
          onChanged: (value) => _updateInternshipInfo(context, 'internshipType', value),
        ),

        // 专业匹配
        DropdownField(
          label: '专业匹配',
          value: internshipInfo.professionalMatch,
          items: const ['完全匹配', '基本匹配', '不匹配但接受'],
          placeholder: '请选择专业匹配',
          onChanged: (value) => _updateInternshipInfo(context, 'professionalMatch', value),
        ),

        // 实习薪酬
        TextInputField(
          label: '实习薪酬',
          value: internshipInfo.salary,
          placeholder: '请输入实习薪酬',
          keyboardType: TextInputType.number,
          onChanged: (value) => _updateInternshipInfo(context, 'salary', value),
        ),

        // 住宿类型
        DropdownField(
          label: '住宿类型',
          value: internshipInfo.accommodationType,
          items: const ['学校宿舍', '企业宿舍', '自行解决', '其他'],
          placeholder: '请选择住宿类型',
          onChanged: (value) => _updateInternshipInfo(context, 'accommodationType', value),
        ),

        // 住宿区域
        LocationField(
          label: '住宿区域',
          value: internshipInfo.accommodationArea,
          placeholder: '请选择地址',
          onChanged: (value) => context.read<InternshipApplicationBloc>().add(
            SelectLocationEvent('accommodationArea', value),
          ),
        ),

        // 住宿详细地址
        TextInputField(
          label: '住宿详细地址',
          value: internshipInfo.accommodationAddress,
          placeholder: '请输入住宿详细地址',
          onChanged: (value) => _updateInternshipInfo(context, 'accommodationAddress', value),
        ),

        // 是否提供伙食
        DropdownField(
          label: '是否提供伙食',
          value: internshipInfo.provideMeals,
          items: const ['是', '否'],
          placeholder: '请选择是/否',
          onChanged: (value) => _updateInternshipInfo(context, 'provideMeals', value),
        ),

        // 实习工作是否有特殊情况
        DropdownField(
          label: '实习工作是否有特殊情况',
          value: internshipInfo.specialCircumstances,
          items: const ['是', '否'],
          placeholder: '请选择是/否',
          showDivider: false,
          onChanged: (value) => _updateInternshipInfo(context, 'specialCircumstances', value),
        ),
      ],
    );
  }

  /// 更新实习信息
  void _updateInternshipInfo(BuildContext context, String field, String value) {
    context.read<InternshipApplicationBloc>().add(
      UpdateInternshipInfoEvent(field, value),
    );
  }
}
