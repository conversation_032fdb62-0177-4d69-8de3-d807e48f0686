# UI 规范

## 应用栏规范
- 统一使用自定义AppBar
- 实现位置：`lib/core/widgets/custom_app_bar.dart`
- 确保整个应用的导航栏样式一致
- 支持标题、返回按钮、操作按钮、背景颜色等自定义选项

## Loading 页面规范
- 所有 Loading 页面使用统一的样式和动画
- 使用 `LoadingWidget` 组件
- 在 BLoC 中使用 `LoadingState` 状态控制
- 设置超时处理（默认30秒）
- 提供取消加载选项

## 缺省页规范
- 空数据缺省页
- 网络错误缺省页
- 服务器错误缺省页
- 权限错误缺省页
- 搜索无结果缺省页
- 使用 `EmptyStateWidget` 组件
- 提供明确的用户操作指引

## 提示（Snackbar）规范
- 统一使用 `AppSnackBar` 工具类
- 提示类型：
  - 普通提示：`AppSnackBar.show()`
  - 成功提示：`AppSnackBar.showSuccess()`
  - 错误提示：`AppSnackBar.showError()`
  - 警告提示：`AppSnackBar.showWarning()`
  - 带操作按钮的提示：`AppSnackBar.showWithAction()`
- 显示时长：
  - 普通提示：2秒
  - 成功提示：2秒
  - 错误提示：3秒
  - 警告提示：3秒
  - 带操作按钮的提示：4秒

## 列表页面规范
- 使用 `pull_to_refresh` 或 `flutter_easyrefresh` 实现下拉刷新和上拉加载
- 使用 `visibility_detector` 或 `flutter_visibility_detector` 实现滚动检测
- 使用 `scrollable_positioned_list` 实现滚动到特定位置
- 实现分页逻辑
- 处理加载状态
- 处理错误状态
- 优化性能

## 样式规范
- 使用 Material Design 3 设计规范
- 统一的颜色系统
- 统一的字体系统
- 统一的间距系统
- 统一的圆角系统
- 统一的阴影系统
- 统一的动画系统

## 响应式设计规范
- 使用 `MediaQuery` 适配不同屏幕尺寸
- 使用 `LayoutBuilder` 实现响应式布局
- 使用 `Flexible` 和 `Expanded` 实现弹性布局
- 使用 `AspectRatio` 保持宽高比
- 使用 `FractionallySizedBox` 实现相对尺寸
- 使用 `MediaQuery` 适配不同屏幕尺寸
- 使用 `LayoutBuilder` 实现响应式布局
- 使用 `Flexible` 和 `Expanded` 实现弹性布局
- 使用 `AspectRatio` 保持宽高比
- 使用 `FractionallySizedBox` 实现相对尺寸 