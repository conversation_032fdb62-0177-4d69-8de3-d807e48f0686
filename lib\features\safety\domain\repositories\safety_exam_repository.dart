/// -----
/// safety_exam_repository.dart
/// 
/// 安全教育考试仓库接口
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/domain/models/exam_record_request.dart';
import 'package:flutter_demo/features/safety/domain/models/exam_detail_response.dart';

/// 安全教育考试仓库接口
///
/// 定义获取安全教育考试题目、保存考试记录和获取考试详情的方法
abstract class SafetyExamRepository {
  /// 获取安全教育考试题目列表
  ///
  /// [planId] 实习计划ID，可选参数
  Future<List<SafetyExamQuestion>> getExamQuestions({String? planId});

  /// 保存学生考试记录
  ///
  /// [request] 考试记录请求数据
  /// 返回保存结果，成功返回true，失败抛出异常
  Future<bool> saveExamRecord(ExamRecordRequest request);

  /// 获取学生考试详情
  ///
  /// [recordId] 考试记录ID
  /// 返回考试详情数据
  Future<ExamDetailResponse> getExamDetail(String recordId);
}
