/// -----
/// approve_free_internship_exempt_usecase.dart
///
/// 审批免实习申请用例
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';

import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/free_internship_approval_request.dart';
import '../repositories/free_internship_exempt_repository.dart';

/// 审批免实习申请用例
/// 
/// 封装审批免实习申请的业务逻辑
class ApproveFreeInternshipExemptUseCase implements UseCase<void, ApproveFreeInternshipExemptParams> {
  ApproveFreeInternshipExemptUseCase(this._repository);

  final FreeInternshipExemptRepository _repository;

  @override
  Future<Either<Failure, void>> call(ApproveFreeInternshipExemptParams params) async {
    return await _repository.approveFreeInternshipExempt(params.request);
  }
}

/// 审批免实习申请用例参数
class ApproveFreeInternshipExemptParams extends Equatable {
  const ApproveFreeInternshipExemptParams({
    required this.request,
  });

  /// 审批请求
  final FreeInternshipApprovalRequest request;

  @override
  List<Object> get props => [request];
}
