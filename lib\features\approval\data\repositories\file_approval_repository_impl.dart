/// -----
/// file_approval_repository_impl.dart
///
/// 文件审批仓库实现
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/exceptions/auth_exception.dart';
import 'package:flutter_demo/core/error/exceptions/network_exception.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/error/failures/auth_failure.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/file_approval_list_item.dart';
import '../../domain/repositories/file_approval_repository.dart';
import '../datasources/remote/file_approval_remote_data_source.dart';

/// 文件审批仓库实现
class FileApprovalRepositoryImpl implements FileApprovalRepository {
  final FileApprovalRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  FileApprovalRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<FileApprovalListItem>>> getFileApprovalList(String planId) async {
    Logger.info('FileApprovalRepository', '开始获取文件审批列表，planId: $planId');

    if (await _networkInfo.isConnected) {
      try {
        final fileApprovalModels = await _remoteDataSource.getFileApprovalList(planId);
        final fileApprovalList = fileApprovalModels.map((model) => model.toEntity()).toList();
        Logger.info('FileApprovalRepository', '成功获取文件审批列表');
        return Right(fileApprovalList);
      } on UnauthorizedException catch (e) {
        Logger.error('FileApprovalRepository', '认证异常: ${e.message}');
        return Left(UnauthorizedFailure(e.message));
      } on ForbiddenException catch (e) {
        Logger.error('FileApprovalRepository', '权限异常: ${e.message}');
        return Left(ForbiddenFailure(e.message));
      } on ServerException catch (e) {
        Logger.error('FileApprovalRepository', '服务器异常: ${e.message}');
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        Logger.error('FileApprovalRepository', '网络异常: ${e.message}');
        return Left(NetworkFailure(e.message));
      } catch (e) {
        Logger.error('FileApprovalRepository', '未知异常: $e');
        return Left(ServerFailure('获取文件审批列表失败: $e'));
      }
    } else {
      Logger.warning('FileApprovalRepository', '网络未连接');
      return const Left(NetworkFailure('网络未连接，请检查网络设置'));
    }
  }
}
