/// -----
/// submit_internship_application_usecase.dart
///
/// 提交实习申请用例
/// 封装提交实习申请的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../../data/models/internship_application_request_model.dart';
import '../repositories/internship_application_repository.dart';

/// 提交实习申请用例
///
/// 封装提交实习申请的业务逻辑，包括数据验证和处理
class SubmitInternshipApplicationUseCase implements UseCase<bool, SubmitInternshipApplicationParams> {
  /// 实习申请仓库
  final InternshipApplicationRepository repository;

  /// 构造函数
  ///
  /// 参数:
  ///   - [repository]: 实习申请仓库实例
  const SubmitInternshipApplicationUseCase({
    required this.repository,
  });

  @override
  Future<Either<Failure, bool>> call(SubmitInternshipApplicationParams params) async {
    // 创建请求模型
    final request = InternshipApplicationRequestModel.fromFormData(
      planId: params.planId,
      companyInfo: params.companyInfo,
      positionInfo: params.positionInfo,
      internshipInfo: params.internshipInfo,
    );

    // 调用仓库提交申请
    return await repository.submitApplication(request);
  }
}

/// 提交实习申请用例参数
///
/// 包含提交实习申请所需的所有参数
class SubmitInternshipApplicationParams {
  /// 实习计划ID
  final String planId;
  
  /// 企业信息
  final Map<String, dynamic> companyInfo;
  
  /// 岗位信息
  final Map<String, dynamic> positionInfo;
  
  /// 实习信息
  final Map<String, dynamic> internshipInfo;

  /// 构造函数
  const SubmitInternshipApplicationParams({
    required this.planId,
    required this.companyInfo,
    required this.positionInfo,
    required this.internshipInfo,
  });
}
