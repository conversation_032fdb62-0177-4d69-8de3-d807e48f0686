/// -----
/// form_validators.dart
///
/// 实习申请表单验证工具类
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/internship/data/models/company_info.dart';
import 'package:flutter_demo/features/internship/data/models/position_info.dart';
import 'package:flutter_demo/features/internship/data/models/internship_info.dart';

/// 表单验证工具类
class FormValidators {
  /// 验证企业信息
  static Map<String, String> validateCompanyInfo(CompanyInfo companyInfo) {
    final Map<String, String> errors = {};

    // 企业名称验证
    if (companyInfo.name.isEmpty) {
      errors['companyName'] = '请输入企业名称';
    }

    // 统一社会信用代码验证
    if (companyInfo.creditCode.isEmpty) {
      errors['creditCode'] = '请输入统一社会信用代码';
    } else if (!_isValidCreditCode(companyInfo.creditCode)) {
      errors['creditCode'] = '统一社会信用代码格式不正确';
    }

    // 企业规模验证
    if (companyInfo.size.isEmpty) {
      errors['companySize'] = '请选择企业规模';
    }

    // 企业性质验证
    if (companyInfo.type.isEmpty) {
      errors['companyType'] = '请选择企业性质';
    }

    // 所属行业验证
    if (companyInfo.industry.isEmpty) {
      errors['industry'] = '请选择所属行业';
    }

    // 企业所在地验证
    if (companyInfo.location.isEmpty) {
      errors['location'] = '请选择企业所在地';
    }

    // 详细地址验证
    if (companyInfo.address.isEmpty) {
      errors['address'] = '请输入详细地址';
    }

    // 联系人验证
    if (companyInfo.contactPerson.isEmpty) {
      errors['contactPerson'] = '请输入联系人';
    }

    // 联系人电话验证
    if (companyInfo.contactPhone.isEmpty) {
      errors['contactPhone'] = '请输入联系人电话';
    } else if (!_isValidPhone(companyInfo.contactPhone)) {
      errors['contactPhone'] = '联系人电话格式不正确';
    }

    // 电子邮箱验证
    if (companyInfo.email.isEmpty) {
      errors['email'] = '请输入电子邮箱';
    } else if (!_isValidEmail(companyInfo.email)) {
      errors['email'] = '电子邮箱格式不正确';
    }

    return errors;
  }

  /// 验证岗位信息
  static Map<String, String> validatePositionInfo(PositionInfo positionInfo) {
    final Map<String, String> errors = {};

    // 部门验证
    if (positionInfo.department.isEmpty) {
      errors['department'] = '请输入部门';
    }

    // 岗位名称验证
    if (positionInfo.name.isEmpty) {
      errors['positionName'] = '请输入岗位名称';
    }

    // 岗位类别验证
    if (positionInfo.type.isEmpty) {
      errors['positionType'] = '请选择岗位类别';
    }

    // 企业老师验证
    if (positionInfo.supervisor.isEmpty) {
      errors['supervisor'] = '请输入企业老师';
    }

    // 企业老师联系电话验证
    if (positionInfo.supervisorPhone.isEmpty) {
      errors['supervisorPhone'] = '请输入企业老师联系电话';
    } else if (!_isValidPhone(positionInfo.supervisorPhone)) {
      errors['supervisorPhone'] = '企业老师联系电话格式不正确';
    }

    // 岗位所在省市验证
    if (positionInfo.location.isEmpty) {
      errors['positionLocation'] = '请选择岗位所在省市';
    }

    // 详细地址验证
    if (positionInfo.address.isEmpty) {
      errors['positionAddress'] = '请输入详细地址';
    }

    return errors;
  }

  /// 验证实习信息
  static Map<String, String> validateInternshipInfo(InternshipInfo internshipInfo) {
    final Map<String, String> errors = {};

    // 岗位开始时间验证
    if (internshipInfo.startDate.isEmpty) {
      errors['startDate'] = '请选择岗位开始时间';
    }

    // 岗位结束时间验证
    if (internshipInfo.endDate.isEmpty) {
      errors['endDate'] = '请选择岗位结束时间';
    }

    // 时间逻辑验证
    if (internshipInfo.startDate.isNotEmpty && internshipInfo.endDate.isNotEmpty) {
      final startDate = DateTime.tryParse(internshipInfo.startDate);
      final endDate = DateTime.tryParse(internshipInfo.endDate);
      
      if (startDate != null && endDate != null && startDate.isAfter(endDate)) {
        errors['dateRange'] = '开始时间不能晚于结束时间';
      }
    }

    // 实习方式验证
    if (internshipInfo.internshipType.isEmpty) {
      errors['internshipType'] = '请选择实习方式';
    }

    // 专业匹配验证
    if (internshipInfo.professionalMatch.isEmpty) {
      errors['professionalMatch'] = '请选择专业匹配';
    }

    // 实习薪酬验证
    if (internshipInfo.salary.isEmpty) {
      errors['salary'] = '请输入实习薪酬';
    }

    // 住宿类型验证
    if (internshipInfo.accommodationType.isEmpty) {
      errors['accommodationType'] = '请选择住宿类型';
    }

    // 住宿区域验证
    if (internshipInfo.accommodationArea.isEmpty) {
      errors['accommodationArea'] = '请选择住宿区域';
    }

    // 住宿详细地址验证
    if (internshipInfo.accommodationAddress.isEmpty) {
      errors['accommodationAddress'] = '请输入住宿详细地址';
    }

    // 是否提供伙食验证
    if (internshipInfo.provideMeals.isEmpty) {
      errors['provideMeals'] = '请选择是否提供伙食';
    }

    // 实习工作是否有特殊情况验证
    if (internshipInfo.specialCircumstances.isEmpty) {
      errors['specialCircumstances'] = '请选择实习工作是否有特殊情况';
    }

    return errors;
  }

  /// 验证统一社会信用代码
  static bool _isValidCreditCode(String creditCode) {
    // 统一社会信用代码为18位，由数字和大写字母组成
    final RegExp creditCodeRegex = RegExp(r'^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$');
    return creditCodeRegex.hasMatch(creditCode);
  }

  /// 验证手机号码
  static bool _isValidPhone(String phone) {
    final RegExp phoneRegex = RegExp(r'^1[3-9]\d{9}$');
    return phoneRegex.hasMatch(phone);
  }

  /// 验证邮箱
  static bool _isValidEmail(String email) {
    final RegExp emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }
}
