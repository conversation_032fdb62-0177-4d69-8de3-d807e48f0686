/// -----
/// student_file_approval_remote_data_source.dart
///
/// 学生文件审批远程数据源接口
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions/server_exception.dart';

import '../../models/student_file_approval_model.dart';

/// 学生文件审批远程数据源接口
abstract class StudentFileApprovalRemoteDataSource {
  /// 获取学生文件审批列表
  /// 
  /// [planId] 实习计划ID
  /// [type] 审批类型（0:待审批，1:已经审批）
  /// 返回学生文件审批列表
  /// 抛出 [ServerException] 当服务器错误时
  Future<List<StudentFileApprovalModel>> getStudentFileApprovalList(
    String planId,
    int type,
  );
}
