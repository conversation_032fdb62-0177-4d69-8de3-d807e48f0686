/// -----
/// internship_application_remote_data_source.dart
///
/// 实习申请远程数据源抽象接口
/// 定义实习申请相关的远程数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../data/models/internship_application_request_model.dart';
import '../../../data/models/internship_application_response_model.dart';

/// 实习申请远程数据源抽象接口
///
/// 定义实习申请相关的远程数据操作方法
abstract class InternshipApplicationRemoteDataSource {
  /// 提交实习申请
  ///
  /// 向服务器提交学生的实习申请信息
  ///
  /// 参数:
  ///   - [request]: 实习申请请求数据
  ///
  /// 返回:
  ///   Future<InternshipApplicationResponseModel> 申请响应结果
  ///
  /// 异常:
  ///   - ServerException: 服务器错误
  ///   - NetworkException: 网络错误
  Future<InternshipApplicationResponseModel> submitApplication(
    InternshipApplicationRequestModel request,
  );
}
