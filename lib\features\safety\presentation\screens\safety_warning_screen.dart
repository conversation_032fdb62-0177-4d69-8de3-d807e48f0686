/// -----
/// safety_warning_screen.dart
///
/// 安全预警页面，用于展示实习安全预警信息列表
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/safety/presentation/screens/checkin_warning_screen.dart';
import 'package:flutter_demo/features/safety/presentation/screens/insurance_warning_screen.dart';
import 'package:flutter_demo/features/safety/presentation/screens/file_upload_warning_screen.dart';
import 'package:flutter_demo/features/safety/presentation/screens/report_warning_screen.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SafetyWarningScreen extends StatefulWidget {
  const SafetyWarningScreen({Key? key}) : super(key: key);

  @override
  State<SafetyWarningScreen> createState() => _SafetyWarningScreenState();
}

class _SafetyWarningScreenState extends State<SafetyWarningScreen> {
  // 安全预警数据
  final List<Map<String, dynamic>> _warningItems = [
    {
      'type': '签到异常',
      'date': '2025-05-12',
      'content': '学生 李敏夫 签到超过了安全距离30KM',
      'icon': 'assets/images/safe_warning_check_in_error_icon.png',
      'iconColor': const Color(0xFFFFC107),
      'studentName': '李敏夫',
      'checkinTime': '2025.03.16 16:00',
      'checkinLocation': '湖北省武汉市武汉火车站',
      'isOutOfRange': true,
      'remarks': '今天短距离外出',
    },
    {
      'type': '实习报告AI检测异常',
      'date': '2025-05-12',
      'content': '学生 李敏夫 的报告中显示加班严重',
      'icon': 'assets/images/safe_warning_internship_report_error.png',
      'iconColor': const Color(0xFFFF7043),
      'studentName': '李敏夫',
    },
    {
      'type': '实习保险',
      'date': '2025-05-12',
      'content': '学生 李敏夫 的保险未覆盖全实习周期',
      'icon': 'assets/images/safe_waring_internship_baoxian_icon.png',
      'iconColor': const Color(0xFFFF4081),
      'studentName': '李敏夫',
    },
    {
      'type': '签到异常',
      'date': '2025-05-12',
      'content': '学生 李敏夫 签到超过了安全距离30KM',
      'icon': 'assets/images/safe_warning_check_in_error_icon.png',
      'iconColor': const Color(0xFFFFC107),
      'studentName': '李敏夫',
      'checkinTime': '2025.03.15 15:30',
      'checkinLocation': '湖北省武汉市江汉区',
      'isOutOfRange': true,
      'remarks': '外出参加活动',
    },
    {
      'type': '实习报告AI检测异常',
      'date': '2025-05-12',
      'content': '学生 李敏夫 的报告中显示加班严重',
      'icon': 'assets/images/safe_warning_internship_report_error.png',
      'iconColor': const Color(0xFFFF7043),
      'studentName': '李敏夫',
    },
    {
      'type': '实习保险',
      'date': '2025-05-12',
      'content': '学生 李敏夫 的保险未覆盖全实习周期',
      'icon': 'assets/images/safe_waring_internship_baoxian_icon.png',
      'iconColor': const Color(0xFFFF4081),
      'studentName': '李敏夫',
    },
    {
      'type': '文件未上传',
      'date': '2025-05-12',
      'content': '学生 李敏夫 的文件未上传',
      'icon': 'assets/images/safe_waring_internship_file_icon.png',
      'iconColor': const Color(0xFFFF4081),
      'studentName': '李敏夫',
    }
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(
        title: '安全预警',
        centerTitle: true,
        showBackButton: true,
      ),
      body: ListView.separated(
        itemCount: _warningItems.length,
        separatorBuilder: (context, index) => const Divider(height: 1, thickness: 1, color: Color(0xFFFFFFFF)),
        itemBuilder: (context, index) {
          final item = _warningItems[index];
          return _buildWarningItem(
            type: item['type'],
            date: item['date'],
            content: item['content'],
            icon: item['icon'],
            iconColor: item['iconColor'],
            item: item,
          );
        },
      ),
    );
  }

  Widget _buildWarningItem({
    required String type,
    required String date,
    required String content,
    required String icon,
    required Color iconColor,
    required Map<String, dynamic> item,
  }) {
    return InkWell(
      onTap: () {
        _navigateToDetailPage(item);
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图标
            Container(
              width: 88.w,
              height: 88.h,
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Image.asset(icon),
              ),
            ),
            const SizedBox(width: 16),
            // 内容
            Expanded(

              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(

                    children: [
                      Text(
                        type,
                        style: TextStyle(
                          fontSize: 28.sp,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF333333),
                        ),
                      ),
                      const Spacer(),
                      Text(
                        date,
                        style: TextStyle(
                          fontSize: 20.sp,
                          color: const Color(0xFF999999),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    content,
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: const Color(0xFF999999),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 导航到详情页面
  void _navigateToDetailPage(Map<String, dynamic> item) {
    if (item['type'] == '签到异常') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const CheckinWarningScreen(),
        ),
      );
    } else if (item['type'] == '实习报告AI检测异常') {
      // 报告预警页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const ReportWarningScreen(),
        ),
      );
    } else if (item['type'] == '实习保险') {
      // 实习保险预警页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const InsuranceWarningScreen(),
        ),
      );
    } else if (item['type'] == '文件未上传') {
      // 文件未上传预警页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const FileUploadWarningScreen(),
        ),
      );
    } else {
      // 其他类型的预警详情页面跳转
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${item['type']}详情页面正在开发中'),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}