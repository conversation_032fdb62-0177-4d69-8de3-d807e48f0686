import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// 获取当前用户用例
///
/// 处理获取当前用户的业务逻辑
class GetCurrentUserUseCase {
  final AuthRepository _repository;

  GetCurrentUserUseCase(this._repository);

  /// 调用获取当前用户用例
  ///
  /// 返回：Either<Failure, User?>，表示获取当前用户成功或失败
  Future<Either<Failure, User?>> call() async {
    return await _repository.getCurrentUser();
  }
}
