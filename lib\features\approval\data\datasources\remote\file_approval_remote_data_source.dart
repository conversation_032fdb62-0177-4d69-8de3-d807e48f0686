/// -----
/// file_approval_remote_data_source.dart
///
/// 文件审批远程数据源接口
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/error/exceptions/server_exception.dart';

import '../../models/file_approval_list_item_model.dart';

/// 文件审批远程数据源接口
abstract class FileApprovalRemoteDataSource {
  /// 获取文件审批列表
  /// 
  /// [planId] 实习计划ID
  /// 返回文件审批列表项列表
  /// 抛出 [ServerException] 当服务器错误时
  Future<List<FileApprovalListItemModel>> getFileApprovalList(String planId);
}
