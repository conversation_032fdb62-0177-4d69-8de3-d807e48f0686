import 'package:flutter/material.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// 页面状态管理器
///
/// 用于管理页面的不同状态：加载中、成功、空数据、错误、无网络
class PageStateManager<T> extends StatefulWidget {
  /// 子组件，正常显示的内容
  final Widget child;
  
  /// 数据加载函数
  final Future<T> Function() loadData;
  
  /// 判断数据是否为空的函数
  final bool Function(T data) isEmpty;
  
  /// 自定义空数据组件
  final Widget? emptyWidget;
  
  /// 自定义错误组件
  final Widget? errorWidget;
  
  /// 自定义无网络组件
  final Widget? noNetworkWidget;
  
  /// 是否自动加载数据
  final bool autoLoad;
  
  /// 刷新回调
  final Function(T data)? onDataLoaded;

  const PageStateManager({
    Key? key,
    required this.child,
    required this.loadData,
    this.isEmpty = _defaultIsEmpty,
    this.emptyWidget,
    this.errorWidget,
    this.noNetworkWidget,
    this.autoLoad = true,
    this.onDataLoaded,
  }) : super(key: key);

  static bool _defaultIsEmpty(dynamic data) {
    if (data == null) return true;
    if (data is List) return data.isEmpty;
    if (data is Map) return data.isEmpty;
    if (data is String) return data.isEmpty;
    return false;
  }

  @override
  _PageStateManagerState<T> createState() => _PageStateManagerState<T>();
}

class _PageStateManagerState<T> extends State<PageStateManager<T>> {
  bool _isLoading = true;
  bool _hasError = false;
  bool _isEmpty = false;
  dynamic _error;
  T? _data;
  
  @override
  void initState() {
    super.initState();
    if (widget.autoLoad) {
      _loadData();
    }
  }
  
  Future<void> _loadData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });
    }
    
    try {
      final result = await widget.loadData();
      _data = result;
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isEmpty = widget.isEmpty(result);
        });
      }
      
      if (widget.onDataLoaded != null) {
        widget.onDataLoaded!(result);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
          _error = e;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return OfflineBuilder(
      connectivityBuilder: (context, connectivity, child) {
        final bool isConnected = connectivity != ConnectivityResult.none;
        
        if (!isConnected && !_isLoading) {
          return widget.noNetworkWidget ?? _buildNoNetworkWidget();
        }
        
        if (_isLoading) {
          return Skeletonizer(
            enabled: true,
            child: widget.child,
          );
        }
        
        if (_hasError) {
          return widget.errorWidget ?? _buildErrorWidget();
        }
        
        if (_isEmpty) {
          return widget.emptyWidget ?? _buildEmptyWidget();
        }
        
        return widget.child;
      },
      child: widget.child,
    );
  }
  
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, size: 60, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('暂无数据', style: TextStyle(color: Colors.grey[600])),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('刷新'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 60, color: Colors.red[300]),
          const SizedBox(height: 16),
          Text('加载失败', style: TextStyle(color: Colors.grey[600])),
          const SizedBox(height: 8),
          Text(_error.toString(), 
               style: TextStyle(color: Colors.grey[500], fontSize: 12),
               textAlign: TextAlign.center),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }
  
  Widget _buildNoNetworkWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.wifi_off, size: 60, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('无网络连接', style: TextStyle(color: Colors.grey[600])),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }
}

/// 页面状态管理扩展
extension PageStateExtension on Widget {
  /// 添加状态管理
  ///
  /// 为Widget添加状态管理功能，包括加载中、空数据、错误和无网络状态
  Widget withStateManager<T>({
    required Future<T> Function() loadData,
    bool Function(T data)? isEmpty,
    Widget? emptyWidget,
    Widget? errorWidget,
    Widget? noNetworkWidget,
    bool autoLoad = true,
    Function(T data)? onDataLoaded,
  }) {
    return PageStateManager<T>(
      loadData: loadData,
      isEmpty: isEmpty ?? PageStateManager._defaultIsEmpty,
      emptyWidget: emptyWidget,
      errorWidget: errorWidget,
      noNetworkWidget: noNetworkWidget,
      autoLoad: autoLoad,
      onDataLoaded: onDataLoaded,
      child: this,
    );
  }
}
