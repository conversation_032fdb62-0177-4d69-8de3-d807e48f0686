/// -----
/// free_internship_exempt_repository.dart
///
/// 免实习申请仓库接口
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/free_internship_exempt.dart';
import '../entities/free_internship_approval_request.dart';

/// 免实习申请仓库接口
///
/// 定义免实习申请数据访问的抽象方法
abstract class FreeInternshipExemptRepository {
  /// 获取免实习申请列表
  ///
  /// [planId] 实习计划ID
  /// [type] 申请类型（0:待审批，1:已审批）
  ///
  /// 返回Either<Failure, List<FreeInternshipExempt>>
  Future<Either<Failure, List<FreeInternshipExempt>>> getFreeInternshipExemptList({
    required int planId,
    required int type,
  });

  /// 审批免实习申请
  ///
  /// [request] 审批请求数据
  ///
  /// 返回Either<Failure, void>
  Future<Either<Failure, void>> approveFreeInternshipExempt(FreeInternshipApprovalRequest request);
}
