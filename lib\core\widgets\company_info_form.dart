/// -----------------------------------------------------------------------------
/// company_info_form.dart
/// 
/// 企业信息表单组件，用于展示和编辑企业基本信息
/// 
/// <AUTHOR>
/// @date 2025-04-15
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/location_picker.dart';

// 企业规模选项
const List<String> _companySizes = [
  '1-49人',
  '50-99人',
  '100-499人',
  '500-999人',
  '1000-4999人',
  '5000-9999人',
  '10000人以上'
];

// 企业性质选项
const List<String> _companyTypes = [
  '国企',
  '民企',
  '外企',
  '事业单位',
  '其他企业'
];

// 所属行业选项
const List<String> _industries = [
  '农、林、牧、渔业',
  '采矿业',
  '制造业',
  '电力、热力、燃气及水生产和供应业',
  '建筑业',
  '批发和零售业',
  '交通运输、仓储和邮政业',
  '住宿和餐饮业',
  '信息传输、软件和信息技术服务业',
  '金融业',
  '房地产业',
  '租赁和商务服务业',
  '科学研究和技术服务业',
  '水利、环境和公共设施管理业',
  '居民服务、修理和其他服务业',
  '教育',
  '卫生和社会工作',
  '文化、体育和娱乐业',
  '公共管理、社会保障和社会组织',
  '其他'
];

class CompanyInfo {
  String name;
  String creditCode;
  String size;
  String type;
  String industry;
  String location;
  String address;
  String contactPerson;
  String contactPhone;
  String email;
  String zipCode;
  String? leaveReason;

  CompanyInfo({
    required this.name,
    required this.creditCode,
    required this.size,
    required this.type,
    required this.industry,
    required this.location,
    required this.address,
    required this.contactPerson,
    required this.contactPhone,
    required this.email,
    required this.zipCode,
    this.leaveReason,
  });
}

class CompanyInfoForm extends StatelessWidget {
  final CompanyInfo companyInfo;
  final bool showLeaveReason;
  final bool allFieldsRequired;
  final Function(CompanyInfo) onCompanyInfoChanged;
  final bool showVerifiedIcon;

  const CompanyInfoForm({
    Key? key,
    required this.companyInfo,
    required this.onCompanyInfoChanged,
    this.showLeaveReason = false,
    this.allFieldsRequired = false,
    this.showVerifiedIcon = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('企业信息'),
          _buildTextField(
            '企业名称',
            companyInfo.name,
            (value) {
              companyInfo.name = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
            showVerified: showVerifiedIcon,
          ),
          _buildDivider(),
          _buildTextField(
            '企业统一社会信用代码',
            companyInfo.creditCode,
            (value) {
              companyInfo.creditCode = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
          ),
          _buildDivider(),
          _buildDropdownField(
            '企业规模',
            _companySizes,
            companyInfo.size,
            (value) {
              companyInfo.size = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
          ),
          _buildDivider(),
          _buildDropdownField(
            '企业性质',
            _companyTypes,
            companyInfo.type,
            (value) {
              companyInfo.type = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
          ),
          _buildDivider(),
          _buildDropdownField(
            '所属行业',
            _industries,
            companyInfo.industry,
            (value) {
              companyInfo.industry = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
          ),
          _buildDivider(),
          LocationPicker(
            label: '企业所在省市',
            value: companyInfo.location,
            onSelected: (newLocation) {
              companyInfo.location = newLocation;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
          ),
          _buildDivider(),
          _buildTextField(
            '详细地址',
            companyInfo.address,
            (value) {
              companyInfo.address = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
            hintText: '请输入详细地址',
          ),
          _buildDivider(),
          _buildTextField(
            '企业联系人',
            companyInfo.contactPerson,
            (value) {
              companyInfo.contactPerson = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
          ),
          _buildDivider(),
          _buildTextField(
            '企业联系人电话',
            companyInfo.contactPhone,
            (value) {
              companyInfo.contactPhone = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
          ),
          _buildDivider(),
          _buildTextField(
            '企业邮箱',
            companyInfo.email,
            (value) {
              companyInfo.email = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
            hintText: '请输入企业邮箱地址',
          ),
          _buildDivider(),
          _buildTextField(
            '企业邮编',
            companyInfo.zipCode,
            (value) {
              companyInfo.zipCode = value;
              onCompanyInfoChanged(companyInfo);
            },
            required: allFieldsRequired,
            hintText: '请输入企业所在地邮编',
          ),
          if (showLeaveReason) ...[
            _buildDivider(),
            _buildTextField(
              '留岗/离岗原因',
              companyInfo.leaveReason ?? '',
              (value) {
                companyInfo.leaveReason = value;
                onCompanyInfoChanged(companyInfo);
              },
              hintText: '请填写',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildTextField(
    String label,
    String value,
    Function(String) onChanged, {
    bool required = false,
    bool showVerified = false,
    String? hintText,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          required
              ? const Text(
                  '*',
                  style: TextStyle(color: Colors.red, fontSize: 14),
                )
              : const SizedBox(width: 8),
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: TextField(
              controller: TextEditingController(text: value),
              onChanged: onChanged,
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: hintText,
                hintStyle: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[400],
                ),
                isCollapsed: true,
                contentPadding: EdgeInsets.zero,
              ),
              style: const TextStyle(fontSize: 14),
            ),
          ),
          if (showVerified)
            Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.green,
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.check,
                color: Colors.white,
                size: 14,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDropdownField(
    String label,
    List<String> items,
    String value,
    void Function(String) onChanged, {
    bool required = false,
  }) {
    // 确保当前值在选项列表中
    if (!items.contains(value)) {
      value = items.first;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (required)
            const Text(
              '*',
              style: TextStyle(color: Colors.red, fontSize: 14),
            ),
          SizedBox(
            width: 160,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 15,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: value,
                isExpanded: true,
                icon: const Icon(Icons.keyboard_arrow_down),
                items: items.map((String item) {
                  return DropdownMenuItem<String>(
                    value: item,
                    child: Text(
                      item,
                      style: const TextStyle(fontSize: 15),
                    ),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    onChanged(newValue);
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Divider(
      height: 1,
      thickness: 0.5,
      color: Colors.grey[300],
      indent: 16,
      endIndent: 16,
    );
  }
} 