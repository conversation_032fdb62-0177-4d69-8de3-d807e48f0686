/// -----
/// file_approval_list_event.dart
///
/// 文件审批列表事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 文件审批列表事件基类
abstract class FileApprovalListEvent extends Equatable {
  const FileApprovalListEvent();

  @override
  List<Object?> get props => [];
}

/// 加载文件审批列表事件
class LoadFileApprovalListEvent extends FileApprovalListEvent {
  final String planId;

  const LoadFileApprovalListEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];

  @override
  String toString() {
    return 'LoadFileApprovalListEvent{planId: $planId}';
  }
}

/// 刷新文件审批列表事件
class RefreshFileApprovalListEvent extends FileApprovalListEvent {
  final String planId;

  const RefreshFileApprovalListEvent({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];

  @override
  String toString() {
    return 'RefreshFileApprovalListEvent{planId: $planId}';
  }
}
