/// -----
/// remote_exemption_records_data_source.dart
///
/// 远程免实习记录数据源实现
/// 通过API获取免实习记录数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import '../models/exemption_record_model.dart';
import 'exemption_records_data_source.dart';

/// 远程免实习记录数据源实现
///
/// 通过HTTP API获取免实习记录数据
class RemoteExemptionRecordsDataSource implements ExemptionRecordsDataSource {
  static const String _tag = 'RemoteExemptionRecordsDataSource';
  
  final DioClient _dioClient;

  const RemoteExemptionRecordsDataSource({
    required DioClient dioClient,
  }) : _dioClient = dioClient;

  @override
  Future<ExemptionRecordModel?> getExemptionRecord({
    required String planId,
  }) async {
    try {
      Logger.info(_tag, '开始获取免实习记录，planId: $planId');

      // 发送GET请求
      final response = await _dioClient.get(
        'internshipservice/v1/internship/student/exempt/applyRecord',
        queryParameters: {
          'planId': planId,
        },
      );

      Logger.info(_tag, '获取免实习记录API响应: $response');

      // 检查响应数据
      if (response == null) {
        Logger.info(_tag, '没有免实习记录数据');
        return null;
      }

      // 解析响应数据
      if (response is Map<String, dynamic>) {
        final record = ExemptionRecordModel.fromJson(response);
        Logger.info(_tag, '成功解析免实习记录: ${record.toString()}');
        return record;
      } else {
        Logger.warning(_tag, '免实习记录API响应格式不正确: ${response.runtimeType}');
        return null;
      }
    } catch (e) {
      Logger.error(_tag, '获取免实习记录失败: $e');
      
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('获取免实习记录失败: $e');
    }
  }
}
