/// -----
/// free_internship_exempt_list_event.dart
///
/// 免实习申请列表BLoC事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 免实习申请列表BLoC事件基类
abstract class FreeInternshipExemptListEvent extends Equatable {
  const FreeInternshipExemptListEvent();

  @override
  List<Object?> get props => [];
}

/// 加载免实习申请列表事件
class LoadFreeInternshipExemptListEvent extends FreeInternshipExemptListEvent {
  /// 实习计划ID
  final int planId;
  
  /// 申请类型（0:待审批，1:已审批）
  final int type;

  const LoadFreeInternshipExemptListEvent({
    required this.planId,
    required this.type,
  });

  @override
  List<Object> get props => [planId, type];
}

/// 刷新免实习申请列表事件
class RefreshFreeInternshipExemptListEvent extends FreeInternshipExemptListEvent {
  /// 实习计划ID
  final int planId;
  
  /// 申请类型（0:待审批，1:已审批）
  final int type;

  const RefreshFreeInternshipExemptListEvent({
    required this.planId,
    required this.type,
  });

  @override
  List<Object> get props => [planId, type];
}
