/// -----
/// internship_score_bloc.dart
/// 
/// 实习成绩BLoC类，处理实习成绩相关的业务逻辑
///
/// <AUTHOR>
/// @date 2025-05-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_score.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_state.dart';

/// 实习成绩BLoC类
///
/// 处理实习成绩相关的业务逻辑，包括加载实习成绩列表、切换标签页、评分等
class InternshipScoreBloc extends Bloc<InternshipScoreEvent, InternshipScoreState> {
  InternshipScoreBloc() : super(InternshipScoreInitial()) {
    on<LoadInternshipScoreEvent>(_onLoadInternshipScore);
    on<SwitchInternshipScoreTabEvent>(_onSwitchTab);
    on<RateInternshipScoreEvent>(_onRateInternshipScore);
    on<ChangeCourseEvent>(_onChangeCourse);
  }

  /// 处理加载实习成绩列表事件
  Future<void> _onLoadInternshipScore(
    LoadInternshipScoreEvent event,
    Emitter<InternshipScoreState> emit,
  ) async {
    try {
      // 如果不是强制刷新且当前状态已经是加载成功状态，则检查是否需要重新加载
      if (!event.forceRefresh && state is InternshipScoreLoaded) {
        final currentState = state as InternshipScoreLoaded;
        if (currentState.courseId == event.courseId) {
          // 如果课程ID相同，则不需要重新加载
          return;
        }
      }

      // 发射加载中状态
      final List<InternshipScore> oldScores = state is InternshipScoreLoaded 
          ? (state as InternshipScoreLoaded).scores 
          : <InternshipScore>[];
      emit(InternshipScoreLoading(
        oldScores: oldScores,
        isFirstLoad: oldScores.isEmpty,
      ));

      // 模拟网络请求，获取实习成绩列表
      // 实际项目中，这里应该调用仓库层的方法获取数据
      await Future.delayed(const Duration(seconds: 1));

      // 模拟数据
      final mockScores = _getMockData();
      final pendingCount = mockScores.where((score) => score.score == null).length;

      // 模拟课程列表
      const availableCourses = [
        '2021级市场销售2023-2024实习学年第二学期岗位实习',
        '2021级市场销售2023-2024实习学年第一学期岗位实习',
        '2022级市场销售2023-2024实习学年第二学期岗位实习',
      ];

      // 发射加载成功状态
      emit(InternshipScoreLoaded(
        scores: mockScores,
        courseId: event.courseId,
        courseName: availableCourses[0],
        availableCourses: availableCourses,
        pendingCount: pendingCount,
      ));
    } catch (e) {
      // 发射加载失败状态
      emit(InternshipScoreError(message: e.toString()));
    }
  }

  /// 处理切换标签页事件
  void _onSwitchTab(
    SwitchInternshipScoreTabEvent event,
    Emitter<InternshipScoreState> emit,
  ) {
    if (state is InternshipScoreLoaded) {
      final currentState = state as InternshipScoreLoaded;
      emit(currentState.copyWith(currentTabIndex: event.tabIndex));
    }
  }

  /// 处理评分事件
  Future<void> _onRateInternshipScore(
    RateInternshipScoreEvent event,
    Emitter<InternshipScoreState> emit,
  ) async {
    if (state is InternshipScoreLoaded) {
      final currentState = state as InternshipScoreLoaded;
      
      // 模拟网络请求，提交评分
      // 实际项目中，这里应该调用仓库层的方法提交评分
      await Future.delayed(const Duration(milliseconds: 500));

      // 更新学生成绩
      final updatedScores = currentState.scores.map((score) {
        if (score.id == event.studentId) {
          return score.copyWith(score: event.score);
        }
        return score;
      }).toList();

      // 计算新的待评分数量
      final newPendingCount = updatedScores.where((score) => score.score == null).length;

      // 发射更新后的状态
      emit(currentState.copyWith(
        scores: updatedScores,
        pendingCount: newPendingCount,
      ));
    }
  }

  /// 处理切换课程事件
  Future<void> _onChangeCourse(
    ChangeCourseEvent event,
    Emitter<InternshipScoreState> emit,
  ) async {
    // 加载新课程的实习成绩列表
    add(LoadInternshipScoreEvent(courseId: event.courseId, forceRefresh: true));
  }

  /// 获取模拟数据
  List<InternshipScore> _getMockData() {
    return [
      const InternshipScore(
        id: '1',
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
        score: null,
      ),
      const InternshipScore(
        id: '2',
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
        score: null,
      ),
      const InternshipScore(
        id: '3',
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
        score: null,
      ),
      const InternshipScore(
        id: '4',
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
        score: null,
      ),
      const InternshipScore(
        id: '5',
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
        score: 60,
      ),
      const InternshipScore(
        id: '6',
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
        score: 60,
      ),
      const InternshipScore(
        id: '7',
        studentName: '李成儒',
        phoneNumber: '13569874562',
        avatarUrl: 'https://randomuser.me/api/portraits/men/32.jpg',
        score: 60,
      ),
    ];
  }
}
