/// -----
/// get_file_requirements_usecase.dart
/// 
/// 获取文件要求列表用例，封装获取文件要求列表的业务逻辑
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../entities/file_requirement.dart';
import '../repositories/file_upload_repository.dart';

/// 获取文件要求列表用例参数
class GetFileRequirementsParams extends Equatable {
  /// 计划ID
  final String planId;

  const GetFileRequirementsParams({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}

/// 获取文件要求列表用例
/// 
/// 处理获取文件要求列表的业务逻辑
class GetFileRequirementsUseCase implements UseCase<List<FileRequirement>, GetFileRequirementsParams> {
  final FileUploadRepository repository;

  GetFileRequirementsUseCase(this.repository);

  @override
  Future<Either<Failure, List<FileRequirement>>> call(GetFileRequirementsParams params) async {
    return await repository.getFileRequirements(
      planId: params.planId,
    );
  }
}
