/// 职位信息实体类
///
/// 用于表示和管理实习职位的详细信息，包括职位名称、类型、工作内容等。
/// 实现了JSON序列化和反序列化方法，便于网络传输和本地存储。
///
/// 示例代码：
/// ```dart
/// final position = PositionInfo(
///   name: '前端开发实习生',
///   type: '技术类',
///   // ...其他参数
/// );
/// ```
class PositionInfo {
  /// 所属部门
  final String department;
  /// 职位名称
  final String name;
  /// 职位类型（如：'技术类'）
  final String type;
  /// 实习开始日期（格式：'YYYY-MM-DD'）
  final String startDate;
  /// 实习结束日期（格式：'YYYY-MM-DD'）
  final String endDate;
  /// 实习周期（如：'6个月'）
  final String period;
  /// 每周工作天数（如：'5天'）
  final String daysPerWeek;
  /// 职位描述
  final String description;
  /// 主管姓名
  final String supervisor;
  /// 主管联系电话
  final String supervisorPhone;
  /// 职位类别（如：'其他人员'）
  final String positionCategory;
  /// 工作内容详细描述
  final String workContent;
  /// 工作地点（省/市/区）
  final String location;
  /// 详细工作地址
  final String address;

  /// 构造函数
  ///
  /// 创建一个[PositionInfo]实例
  ///
  /// 所有参数都是必需的，用于完整描述职位信息。
  const PositionInfo({
    required this.department,
    required this.name,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.period,
    required this.daysPerWeek,
    required this.description,
    required this.supervisor,
    required this.supervisorPhone,
    required this.positionCategory,
    required this.workContent,
    required this.location,
    required this.address,
  });

  /// 从JSON Map创建[PositionInfo]实例
  ///
  /// 用于从服务器响应或本地存储中反序列化职位信息
  ///
  /// 参数:
  ///   - [json]: 包含职位信息的JSON对象
  factory PositionInfo.fromJson(Map<String, dynamic> json) {
    return PositionInfo(
      department: json['department'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      startDate: json['startDate'] ?? '',
      endDate: json['endDate'] ?? '',
      period: json['period'] ?? '',
      daysPerWeek: json['daysPerWeek'] ?? '',
      description: json['description'] ?? '',
      supervisor: json['supervisor'] ?? '',
      supervisorPhone: json['supervisorPhone'] ?? '',
      positionCategory: json['positionCategory'] ?? '',
      workContent: json['workContent'] ?? '',
      location: json['location'] ?? '',
      address: json['address'] ?? '',
    );
  }

  /// 将[PositionInfo]实例转换为JSON Map
  ///
  /// 用于将职位信息序列化为JSON格式，便于网络传输或本地存储
  ///
  /// 返回:
  ///   包含职位信息的Map对象
  Map<String, dynamic> toJson() {
    return {
      'department': department,
      'name': name,
      'type': type,
      'startDate': startDate,
      'endDate': endDate,
      'period': period,
      'daysPerWeek': daysPerWeek,
      'description': description,
      'supervisor': supervisor,
      'supervisorPhone': supervisorPhone,
      'positionCategory': positionCategory,
      'workContent': workContent,
      'location': location,
      'address': address,
    };
  }

  /// 获取示例数据
  ///
  /// 用于开发和测试，返回一个预定义的[PositionInfo]实例
  ///
  /// 返回:
  ///   包含示例数据的[PositionInfo]实例
  static PositionInfo sampleData() {
    return PositionInfo(
      department: '客服',
      name: '网络客服',
      type: '技术类',
      startDate: '2023-09-01',
      endDate: '2024-02-28',
      period: '6个月',
      daysPerWeek: '5天',
      description: '参与公司前端项目开发，负责Web应用的界面设计和交互实现。',
      supervisor: '小徐',
      supervisorPhone: '18627171276',
      positionCategory: '其他人员',
      workContent: '回消息',
      location: '海外/其他/其他',
      address: '武职',
    );
  }
} 