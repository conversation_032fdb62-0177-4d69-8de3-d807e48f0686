/// -----
/// internship_score_detail_screen.dart
/// 
/// 实习成绩详情页面，展示学生的实习成绩详情和评分项
///
/// <AUTHOR>
/// @date 2025-05-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/app_snack_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_score.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_state.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/score_circle_widget.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/score_item_widget.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 实习成绩详情页面
///
/// 展示学生的实习成绩详情和评分项，支持教师评分
class InternshipScoreDetailScreen extends StatefulWidget {
  /// 学生ID
  final String studentId;
  
  /// 课程ID
  final String courseId;
  
  /// 课程名称
  final String courseName;

  const InternshipScoreDetailScreen({
    Key? key,
    required this.studentId,
    required this.courseId,
    required this.courseName,
  }) : super(key: key);

  @override
  State<InternshipScoreDetailScreen> createState() => _InternshipScoreDetailScreenState();
}

class _InternshipScoreDetailScreenState extends State<InternshipScoreDetailScreen> {
  late InternshipScoreBloc _scoreBloc;
  int? _totalScore;
  final Map<String, int?> _itemScores = {};
  final List<Map<String, dynamic>> _scoreItems = [
    {
      'id': 'workQuality1',
      'title': '工作质量',
      'weight': '20%',
      'description': '任务完成准确性、专业度、细节处理能力',
      'score': null,
      'maxScore': 10,
    },
    {
      'id': 'workQuality2',
      'title': '工作质量',
      'weight': '20%',
      'description': '任务完成准确性、专业度、细节处理能力',
      'score': null,
      'maxScore': 10,
    },
    {
      'id': 'workQuality3',
      'title': '工作质量',
      'weight': '20%',
      'description': '任务完成准确性、专业度、细节处理能力',
      'score': null,
      'maxScore': 10,
    },
    {
      'id': 'workQuality4',
      'title': '工作质量',
      'weight': '20%',
      'description': '任务完成准确性、专业度、细节处理能力',
      'score': null,
      'maxScore': 10,
    },
    {
      'id': 'workQuality5',
      'title': '工作质量',
      'weight': '20%',
      'description': '任务完成准确性、专业度、细节处理能力',
      'score': null,
      'maxScore': 10,
    },
  ];
  bool _canSubmit = true;

  @override
  void initState() {
    super.initState();
    _scoreBloc = InternshipScoreBloc();
    
    // 加载学生成绩详情
    _loadStudentScoreDetail();
  }

  @override
  void dispose() {
    _scoreBloc.close();
    super.dispose();
  }

  /// 加载学生成绩详情
  void _loadStudentScoreDetail() {
    // 在实际项目中，这里应该从API获取学生成绩详情
    // 这里使用模拟数据
    
    // 模拟已有的评分数据
    setState(() {
      _totalScore = 80; // 模拟总分
      
      // 模拟评分项分数
      _itemScores['workQuality1'] = 10;
      _itemScores['workQuality2'] = null;
      _itemScores['workQuality3'] = null;
      _itemScores['workQuality4'] = null;
      _itemScores['workQuality5'] = null;
      
      // 更新评分项列表
      for (var item in _scoreItems) {
        item['score'] = _itemScores[item['id']];
      }
      
      // 检查是否可以提交
      _updateSubmitStatus();
    });
  }

  /// 处理评分
  void _handleRate(String itemId) {
    // 在实际项目中，这里应该弹出评分对话框
    // 这里简单模拟评分操作
    setState(() {
      _itemScores[itemId] = 10;
      
      // 更新评分项列表
      for (var item in _scoreItems) {
        if (item['id'] == itemId) {
          item['score'] = _itemScores[itemId];
          break;
        }
      }
      
      // 检查是否可以提交
      _updateSubmitStatus();
    });
  }

  /// 更新提交状态
  void _updateSubmitStatus() {
    bool allRated = true;
    for (var score in _itemScores.values) {
      if (score == null) {
        allRated = false;
        break;
      }
    }
    setState(() {
      _canSubmit = allRated;
    });
  }

  /// 提交评分
  void _submitScores() {
    AppSnackBar.showSuccess(context, '评分提交成功');
    // 返回上一页
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    // 获取状态栏高度
    final double statusBarHeight = MediaQuery.of(context).padding.top;
    
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.white,size: 20,),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '成绩评分',
          style: TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      extendBodyBehindAppBar: true, // 让AppBar透明
      body: Stack(
        children: [
          // 顶部背景图片
          Positioned(
            child: Image.asset(
              'assets/images/internship_score_detail_top_bg.png',
              height: 628.h,
              fit: BoxFit.fitHeight, // 确保图片覆盖整个区域
            ),
          ),
          
          // 内容区域
          Column(
            children: [
              // 课程头部 - 透明背景，白色文字
              Container(
                margin: EdgeInsets.only(
                  top: statusBarHeight + kToolbarHeight , // 状态栏高度 + AppBar高度 + 上边距
                  left: 24.w,
                  right: 24.w,
                ),
                child: Text(
                  widget.courseName,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              
              // 可滚动内容
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.only(
                    left: 27.w,
                    right: 27.w,
                    top: 35.h, // 调整顶部间距
                    bottom: 140.h, // 底部留出按钮高度+边距
                  ),
                  child: Column(
                    children: [
                      // 总分圆环
                      _buildScoreCircle(),
                      SizedBox(height: 39.h),
                      // 评分项标题
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 3.w),
                        child: Row(
                          children: [
                            Text(
                              '评分项',
                              style: TextStyle(
                                fontSize: 30.sp,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.black333,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              '每项评分总分10分',
                              style: TextStyle(
                                fontSize: 22.sp,
                                color: AppTheme.black666,
                              ),
                            ),
                          ],
                        ),
                      ),
                      
                      // 评分项列表
                      ..._scoreItems.map((item) => ScoreItemWidget(
                        title: item['title'],
                        weight: item['weight'],
                        description: item['description'],
                        score: item['score'],
                        maxScore: item['maxScore'],
                        onRatePressed: () => _handleRate(item['id']),
                      )),
                      
                      // 底部占位，防止内容被底部固定按钮遮挡
                      SizedBox(height: 20.h),
                    ],
                  ),
                ),
              ),
            ],
          ),
          
          // 固定在底部的提交区域
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 16.h),
              decoration: const BoxDecoration(
                color: Colors.white
              ),
              child: SafeArea(
                top: false,
                child: SizedBox(
                  height: 88.h,
                  child: ElevatedButton(
                    onPressed: _submitScores,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                    child: Text(
                      '提交',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 32.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建评分圆环
  Widget _buildScoreCircle() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 46.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          ScoreCircleWidget(
            score: _totalScore ?? 0,
            title: '校内老师\n总评分',
            size: 263,
            strokeWidth: 20,

          ),
        ],
      ),
    );
  }
}
