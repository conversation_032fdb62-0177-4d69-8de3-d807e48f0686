import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_demo/core/error/exceptions/auth_exception.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/error/exceptions/network_exception.dart';
import 'package:flutter_demo/core/utils/logger.dart';

/// 错误处理拦截器
///
/// 统一处理网络错误和服务器错误
/// 转换错误为应用内的异常类型
class ErrorInterceptor extends Interceptor {
  static const String _tag = 'ErrorHandler';
  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    // 使用Logger记录错误信息
    Logger.error(
      _tag,
      '网络请求错误: ${err.type}',
      exception: err,
      stackTrace: err.stackTrace,
    );

    // 记录请求详情
    Logger.error(_tag, '请求URL: ${err.requestOptions.uri}');
    Logger.error(_tag, '请求方法: ${err.requestOptions.method}');

    // 记录请求数据
    if (err.requestOptions.data != null) {
      Logger.error(_tag, '请求数据: ${Logger.prettyJson(err.requestOptions.data)}');
    }

    // 记录响应信息（如果有）
    if (err.response != null) {
      Logger.error(_tag, '响应状态码: ${err.response?.statusCode}');
      Logger.error(_tag, '响应数据: ${Logger.prettyJson(err.response?.data)}');
    }

    // 保留原有的控制台输出
    if (kDebugMode) {
      debugPrint('网络请求错误: ${err.type}');
      debugPrint('请求URL: ${err.requestOptions.uri}');
      debugPrint('请求方法: ${err.requestOptions.method}');
      debugPrint('请求数据: ${err.requestOptions.data}');

      if (err.response != null) {
        debugPrint('响应状态码: ${err.response?.statusCode}');
        debugPrint('响应数据: ${err.response?.data}');
      }
    }

    // 将Dio异常转换为应用自定义异常
    switch (err.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        handler.reject(
          DioException(
            requestOptions: err.requestOptions,
            error: NetworkException('网络连接超时，请检查您的网络连接'),
          ),
        );
        break;

      case DioExceptionType.badResponse:
        // 根据状态码处理不同类型的错误
        switch (err.response?.statusCode) {
          case 400:
            handler.reject(
              DioException(
                requestOptions: err.requestOptions,
                error: ServerException('请求参数错误'),
              ),
            );
            break;
          case 401:
            // 401错误应该由AuthInterceptor先处理，如果到了这里说明AuthInterceptor没有处理
            // 直接传递原始错误，不做转换
            handler.next(err);
            break;
          case 403:
            handler.reject(
              DioException(
                requestOptions: err.requestOptions,
                error: ForbiddenException(),
              ),
            );
            break;
          case 404:
            handler.reject(
              DioException(
                requestOptions: err.requestOptions,
                error: ServerException('请求的资源不存在'),
              ),
            );
            break;
          case 500:
          case 501:
          case 502:
          case 503:
          case 504:
            handler.reject(
              DioException(
                requestOptions: err.requestOptions,
                error: ServerException('服务器内部错误，请稍后再试'),
              ),
            );
            break;
          default:
            // 尝试从响应中获取错误信息
            String errorMessage = '发生未知错误，请稍后再试';
            if (err.response?.data is Map && err.response?.data['resultMsg'] != null) {
              errorMessage = err.response?.data['resultMsg'];
            }

            handler.reject(
              DioException(
                requestOptions: err.requestOptions,
                error: ServerException(errorMessage),
              ),
            );
        }
        break;

      case DioExceptionType.connectionError:
        handler.reject(
          DioException(
            requestOptions: err.requestOptions,
            error: NetworkException('网络连接失败，请检查您的网络'),
          ),
        );
        break;

      case DioExceptionType.cancel:
        handler.reject(
          DioException(
            requestOptions: err.requestOptions,
            error: NetworkException('请求已取消'),
          ),
        );
        break;

      default:
        handler.reject(
          DioException(
            requestOptions: err.requestOptions,
            error: NetworkException('发生未知错误，请稍后再试'),
          ),
        );
    }
  }
}