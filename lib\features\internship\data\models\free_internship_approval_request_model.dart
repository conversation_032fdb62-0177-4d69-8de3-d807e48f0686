/// -----
/// free_internship_approval_request_model.dart
///
/// 免实习申请审批请求数据模型类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/entities/free_internship_approval_request.dart';

/// 免实习申请审批请求数据模型类
/// 
/// 继承自实体类，添加JSON序列化功能
class FreeInternshipApprovalRequestModel extends FreeInternshipApprovalRequest {
  const FreeInternshipApprovalRequestModel({
    required super.id,
    required super.reviewOpinion,
    required super.status,
  });

  /// 从实体类创建模型实例
  factory FreeInternshipApprovalRequestModel.fromEntity(FreeInternshipApprovalRequest entity) {
    return FreeInternshipApprovalRequestModel(
      id: entity.id,
      reviewOpinion: entity.reviewOpinion,
      status: entity.status,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reviewOpinion': reviewOpinion,
      'status': status,
    };
  }

  /// 从JSON创建模型实例
  factory FreeInternshipApprovalRequestModel.fromJson(Map<String, dynamic> json) {
    return FreeInternshipApprovalRequestModel(
      id: json['id'] ?? 0,
      reviewOpinion: json['reviewOpinion'] ?? '',
      status: json['status'] ?? 0,
    );
  }

  /// 转换为实体类
  FreeInternshipApprovalRequest toEntity() {
    return FreeInternshipApprovalRequest(
      id: id,
      reviewOpinion: reviewOpinion,
      status: status,
    );
  }
}
