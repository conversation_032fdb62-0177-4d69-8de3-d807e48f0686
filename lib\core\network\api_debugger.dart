import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/config/env_config.dart';

/// API调试工具类
///
/// 提供API调试相关功能，如模拟响应、请求记录等
class ApiDebugger {
  /// 私有构造函数
  ApiDebugger._();
  
  /// 单例实例
  static final ApiDebugger _instance = ApiDebugger._();
  
  /// 获取单例实例
  static ApiDebugger get instance => _instance;
  
  /// 是否启用调试模式
  bool _debugEnabled = false;
  
  /// 请求历史记录
  final List<RequestRecord> _requestHistory = [];
  
  /// 模拟响应映射表
  final Map<String, MockResponse> _mockResponses = {};
  
  /// 获取是否启用调试模式
  bool get isDebugEnabled => _debugEnabled;
  
  /// 获取请求历史记录
  List<RequestRecord> get requestHistory => List.unmodifiable(_requestHistory);
  
  /// 启用API调试
  void enable() {
    _debugEnabled = true;
    _setupInterceptor();
    debugPrint('API调试模式已启用');
  }
  
  /// 禁用API调试
  void disable() {
    _debugEnabled = false;
    _clearMockResponses();
    debugPrint('API调试模式已禁用');
  }
  
  /// 设置拦截器
  void _setupInterceptor() {
    final dioClient = DioClient();
    
    // 添加调试拦截器
    dioClient.dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (!_debugEnabled) {
            return handler.next(options);
          }
          
          // 记录请求
          _recordRequest(options);
          
          // 检查是否有模拟响应
          final mockResponse = _getMockResponse(options);
          if (mockResponse != null) {
            debugPrint('使用模拟响应: ${options.path}');
            
            if (mockResponse.delayMillis > 0) {
              // 模拟网络延迟
              Future.delayed(Duration(milliseconds: mockResponse.delayMillis), () {
                if (mockResponse.error != null) {
                  // 返回错误响应
                  handler.reject(
                    DioException(
                      requestOptions: options,
                      error: mockResponse.error,
                      type: DioExceptionType.badResponse,
                      response: Response(
                        statusCode: mockResponse.statusCode,
                        data: mockResponse.data,
                        requestOptions: options,
                      ),
                    ),
                  );
                } else {
                  // 返回成功响应
                  handler.resolve(
                    Response(
                      statusCode: mockResponse.statusCode,
                      data: mockResponse.data,
                      requestOptions: options,
                    ),
                  );
                }
              });
            } else {
              if (mockResponse.error != null) {
                // 返回错误响应
                handler.reject(
                  DioException(
                    requestOptions: options,
                    error: mockResponse.error,
                    type: DioExceptionType.badResponse,
                    response: Response(
                      statusCode: mockResponse.statusCode,
                      data: mockResponse.data,
                      requestOptions: options,
                    ),
                  ),
                );
              } else {
                // 返回成功响应
                handler.resolve(
                  Response(
                    statusCode: mockResponse.statusCode,
                    data: mockResponse.data,
                    requestOptions: options,
                  ),
                );
              }
            }
          } else {
            // 继续正常请求
            handler.next(options);
          }
        },
        onResponse: (response, handler) {
          if (_debugEnabled) {
            // 记录响应
            _recordResponse(response);
          }
          handler.next(response);
        },
        onError: (error, handler) {
          if (_debugEnabled) {
            // 记录错误
            _recordError(error);
          }
          handler.next(error);
        },
      ),
    );
  }
  
  /// 记录请求
  void _recordRequest(RequestOptions options) {
    final record = RequestRecord(
      path: options.path,
      method: options.method,
      baseUrl: options.baseUrl,
      headers: options.headers,
      queryParameters: options.queryParameters,
      data: options.data,
      timestamp: DateTime.now(),
    );
    
    _requestHistory.add(record);
    
    // 限制历史记录数量
    if (_requestHistory.length > 100) {
      _requestHistory.removeAt(0);
    }
  }
  
  /// 记录响应
  void _recordResponse(Response response) {
    final index = _findRequestIndex(response.requestOptions);
    if (index != -1) {
      _requestHistory[index].response = ResponseRecord(
        statusCode: response.statusCode ?? 0,
        data: response.data,
        headers: response.headers.map,
        timestamp: DateTime.now(),
      );
    }
  }
  
  /// 记录错误
  void _recordError(DioException error) {
    final index = _findRequestIndex(error.requestOptions);
    if (index != -1) {
      _requestHistory[index].error = ErrorRecord(
        message: error.message ?? '未知错误',
        type: error.type.toString(),
        statusCode: error.response?.statusCode,
        data: error.response?.data,
        timestamp: DateTime.now(),
      );
    }
  }
  
  /// 查找请求索引
  int _findRequestIndex(RequestOptions options) {
    for (int i = _requestHistory.length - 1; i >= 0; i--) {
      final record = _requestHistory[i];
      if (record.path == options.path && 
          record.method == options.method &&
          record.response == null && 
          record.error == null) {
        return i;
      }
    }
    return -1;
  }
  
  /// 添加模拟响应
  void addMockResponse(String path, dynamic data, {
    int statusCode = 200,
    String method = 'GET',
    int delayMillis = 0,
    dynamic error,
  }) {
    final key = _getMockKey(method, path);
    _mockResponses[key] = MockResponse(
      data: data,
      statusCode: statusCode,
      delayMillis: delayMillis,
      error: error,
    );
    debugPrint('已添加模拟响应: $method $path');
  }
  
  /// 移除模拟响应
  void removeMockResponse(String path, {String method = 'GET'}) {
    final key = _getMockKey(method, path);
    _mockResponses.remove(key);
    debugPrint('已移除模拟响应: $method $path');
  }
  
  /// 清除所有模拟响应
  void _clearMockResponses() {
    _mockResponses.clear();
    debugPrint('已清除所有模拟响应');
  }
  
  /// 获取模拟响应
  MockResponse? _getMockResponse(RequestOptions options) {
    final key = _getMockKey(options.method, options.path);
    return _mockResponses[key];
  }
  
  /// 获取模拟键
  String _getMockKey(String method, String path) {
    return '$method:$path';
  }
  
  /// 清除请求历史
  void clearHistory() {
    _requestHistory.clear();
    debugPrint('已清除请求历史');
  }
  
  /// 打印请求历史
  void printHistory() {
    if (_requestHistory.isEmpty) {
      debugPrint('请求历史为空');
      return;
    }
    
    debugPrint('===== 请求历史 (${_requestHistory.length}) =====');
    for (int i = 0; i < _requestHistory.length; i++) {
      final record = _requestHistory[i];
      debugPrint('[$i] ${record.method} ${record.path}');
      debugPrint('  时间: ${record.timestamp}');
      if (record.response != null) {
        debugPrint('  状态: ${record.response!.statusCode}');
      } else if (record.error != null) {
        debugPrint('  错误: ${record.error!.message}');
      } else {
        debugPrint('  状态: 等待中');
      }
    }
    debugPrint('================================');
  }
}

/// 请求记录类
class RequestRecord {
  final String path;
  final String method;
  final String baseUrl;
  final Map<String, dynamic> headers;
  final Map<String, dynamic>? queryParameters;
  final dynamic data;
  final DateTime timestamp;
  ResponseRecord? response;
  ErrorRecord? error;
  
  RequestRecord({
    required this.path,
    required this.method,
    required this.baseUrl,
    required this.headers,
    this.queryParameters,
    this.data,
    required this.timestamp,
    this.response,
    this.error,
  });
  
  @override
  String toString() {
    return '$method $path';
  }
}

/// 响应记录类
class ResponseRecord {
  final int statusCode;
  final dynamic data;
  final Map<String, List<String>> headers;
  final DateTime timestamp;
  
  ResponseRecord({
    required this.statusCode,
    required this.data,
    required this.headers,
    required this.timestamp,
  });
}

/// 错误记录类
class ErrorRecord {
  final String message;
  final String type;
  final int? statusCode;
  final dynamic data;
  final DateTime timestamp;
  
  ErrorRecord({
    required this.message,
    required this.type,
    this.statusCode,
    this.data,
    required this.timestamp,
  });
}

/// 模拟响应类
class MockResponse {
  final dynamic data;
  final int statusCode;
  final int delayMillis;
  final dynamic error;
  
  MockResponse({
    required this.data,
    this.statusCode = 200,
    this.delayMillis = 0,
    this.error,
  });
}
