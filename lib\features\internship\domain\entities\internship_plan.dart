/// -----
/// internship_plan.dart
/// 
/// 实习计划领域实体，定义实习计划的核心业务对象
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习计划实体
/// 
/// 表示一个实习计划的核心业务对象，包含所有必要的业务属性
class InternshipPlan extends Equatable {
  /// 主键ID
  final String id;
  
  /// 实习计划名称
  final String planName;
  
  /// 计划编号
  final String planCode;
  
  /// 学年
  final String academicYear;
  
  /// 学期
  final String semester;
  
  /// 实习年级
  final String grade;
  
  /// 层次（本科、专科等）
  final String level;
  
  /// 所属专业名称
  final String majorName;
  
  /// 所属院系名称
  final String facultyName;
  
  /// 状态（0=待启用，1=进行中, 2=已结束）
  final int planStatus;
  
  /// 类型（1=岗位实习, 2=认识实习, 3=其他实习, 4=学徒制, 5=综合实训, 6=工学交替）
  final int planType;
  
  /// 实习形式（1=校外实习, 2=校内真实职业场景, 3=虚拟仿真）
  final int practiceMode;
  
  /// 实习开始日期（时间戳）
  final int startTime;
  
  /// 实习结束日期（时间戳）
  final int endTime;
  
  /// 创建人
  final String createPerson;
  
  /// 创建时间（时间戳）
  final int createTime;
  
  /// 专业负责人名称
  final String majorAdminName;
  
  /// 补贴标准
  final String subsidy;
  
  /// 应交日报数
  final int dailyReportNum;
  
  /// 应交周报数
  final int weeklyReportNum;
  
  /// 应交月报数
  final int monthlyReportNum;
  
  /// 应交总结数
  final int summaryNum;
  
  /// 应签到数
  final int signNum;
  
  /// 单选题数量
  final int checkNum;
  
  /// 多选题数量
  final int mulCheckNum;

  const InternshipPlan({
    required this.id,
    required this.planName,
    required this.planCode,
    required this.academicYear,
    required this.semester,
    required this.grade,
    required this.level,
    required this.majorName,
    required this.facultyName,
    required this.planStatus,
    required this.planType,
    required this.practiceMode,
    required this.startTime,
    required this.endTime,
    required this.createPerson,
    required this.createTime,
    required this.majorAdminName,
    required this.subsidy,
    required this.dailyReportNum,
    required this.weeklyReportNum,
    required this.monthlyReportNum,
    required this.summaryNum,
    required this.signNum,
    required this.checkNum,
    required this.mulCheckNum,
  });

  @override
  List<Object?> get props => [
    id,
    planName,
    planCode,
    academicYear,
    semester,
    grade,
    level,
    majorName,
    facultyName,
    planStatus,
    planType,
    practiceMode,
    startTime,
    endTime,
    createPerson,
    createTime,
    majorAdminName,
    subsidy,
    dailyReportNum,
    weeklyReportNum,
    monthlyReportNum,
    summaryNum,
    signNum,
    checkNum,
    mulCheckNum,
  ];

  /// 获取状态文本
  String get statusText {
    switch (planStatus) {
      case 0:
        return '待启用';
      case 1:
        return '进行中';
      case 2:
        return '已结束';
      default:
        return '未知';
    }
  }

  /// 获取类型文本
  String get typeText {
    switch (planType) {
      case 1:
        return '岗位实习';
      case 2:
        return '认识实习';
      case 3:
        return '其他实习';
      case 4:
        return '学徒制';
      case 5:
        return '综合实训';
      case 6:
        return '工学交替';
      default:
        return '未知类型';
    }
  }

  /// 获取实习形式文本
  String get practiceModeText {
    switch (practiceMode) {
      case 1:
        return '校外实习';
      case 2:
        return '校内真实职业场景';
      case 3:
        return '虚拟仿真';
      default:
        return '未知形式';
    }
  }

  /// 获取格式化的开始时间
  String get formattedStartTime {
    if (startTime == 0) return '';
    final date = DateTime.fromMillisecondsSinceEpoch(startTime);
    return '${date.year}.${date.month.toString().padLeft(2, '0')}.${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取格式化的结束时间
  String get formattedEndTime {
    if (endTime == 0) return '';
    final date = DateTime.fromMillisecondsSinceEpoch(endTime);
    return '${date.year}.${date.month.toString().padLeft(2, '0')}.${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取格式化的实习周期
  String get formattedPeriod {
    if (startTime == 0 || endTime == 0) return '';
    return '$formattedStartTime-$formattedEndTime';
  }

  /// 获取格式化的创建时间
  String get formattedCreateTime {
    if (createTime == 0) return '';
    final date = DateTime.fromMillisecondsSinceEpoch(createTime);
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }
}
