/// -----
/// teacher_safety_education_response.dart
/// 
/// 教师端安全教育考试数据响应模型
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 安全教育考试统计数据模型
/// 
/// 包含已考试、应考试、已及格、80分以上等统计信息
class SafetyEducationStatistics extends Equatable {
  /// 已考试人数
  final int alreadyExam;
  
  /// 应考试人数
  final int shouldExam;
  
  /// 已及格人数
  final int alreadyPass;
  
  /// 80分以上人数
  final int above;

  const SafetyEducationStatistics({
    required this.alreadyExam,
    required this.shouldExam,
    required this.alreadyPass,
    required this.above,
  });

  @override
  List<Object?> get props => [alreadyExam, shouldExam, alreadyPass, above];

  /// 从JSON映射创建统计数据对象
  factory SafetyEducationStatistics.fromJson(Map<String, dynamic> json) {
    return SafetyEducationStatistics(
      alreadyExam: json['alreadyExam'] as int? ?? 0,
      shouldExam: json['shouldExam'] as int? ?? 0,
      alreadyPass: json['alreadyPass'] as int? ?? 0,
      above: json['above'] as int? ?? 0,
    );
  }

  /// 将统计数据对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'alreadyExam': alreadyExam,
      'shouldExam': shouldExam,
      'alreadyPass': alreadyPass,
      'above': above,
    };
  }
}

/// 学生考试信息模型
/// 
/// 包含学生的基本信息和考试成绩
class StudentExamInfo extends Equatable {
  /// 学生ID
  final String studentId;
  
  /// 学生姓名
  final String studentName;
  
  /// 学生手机号
  final String phone;
  
  /// 学生头像URL（可为空）
  final String? avatar;
  
  /// 考试分数
  final double score;
  
  /// 试题记录ID
  final String recordId;

  const StudentExamInfo({
    required this.studentId,
    required this.studentName,
    required this.phone,
    this.avatar,
    required this.score,
    required this.recordId,
  });

  @override
  List<Object?> get props => [studentId, studentName, phone, avatar, score, recordId];

  /// 从JSON映射创建学生考试信息对象
  factory StudentExamInfo.fromJson(Map<String, dynamic> json) {
    return StudentExamInfo(
      studentId: json['studentId'] as String? ?? '',
      studentName: json['studentName'] as String? ?? '',
      phone: json['phone'] as String? ?? '',
      avatar: json['avatar'] as String?,
      score: (json['score'] as num?)?.toDouble() ?? 0.0,
      recordId: json['recordId'] as String? ?? '',
    );
  }

  /// 将学生考试信息对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'studentId': studentId,
      'studentName': studentName,
      'phone': phone,
      'avatar': avatar,
      'score': score,
      'recordId': recordId,
    };
  }
}

/// 学生分组模型
/// 
/// 按班级分组的学生列表
class StudentGroup extends Equatable {
  /// 分组名称（班级名称）
  final String name;
  
  /// 该分组下的学生列表
  final List<StudentExamInfo> list;

  const StudentGroup({
    required this.name,
    required this.list,
  });

  @override
  List<Object?> get props => [name, list];

  /// 从JSON映射创建学生分组对象
  factory StudentGroup.fromJson(Map<String, dynamic> json) {
    final studentList = (json['list'] as List<dynamic>?)
        ?.map((studentJson) => StudentExamInfo.fromJson(studentJson as Map<String, dynamic>))
        .toList() ?? <StudentExamInfo>[];

    return StudentGroup(
      name: json['name'] as String? ?? '',
      list: studentList,
    );
  }

  /// 将学生分组对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'list': list.map((student) => student.toJson()).toList(),
    };
  }
}

/// 教师端安全教育考试数据响应模型
/// 
/// 包含统计数据和按班级分组的学生列表
class TeacherSafetyEducationResponse extends Equatable {
  /// 安全教育考试统计数据
  final SafetyEducationStatistics statistics;
  
  /// 按班级分组的学生列表
  final List<StudentGroup> studentList;

  const TeacherSafetyEducationResponse({
    required this.statistics,
    required this.studentList,
  });

  @override
  List<Object?> get props => [statistics, studentList];

  /// 从JSON映射创建教师端安全教育考试数据响应对象
  factory TeacherSafetyEducationResponse.fromJson(Map<String, dynamic> json) {
    final statistics = SafetyEducationStatistics.fromJson(json);

    final studentGroups = (json['studentList'] as List<dynamic>?)
        ?.map((groupJson) => StudentGroup.fromJson(groupJson as Map<String, dynamic>))
        .toList() ?? <StudentGroup>[];

    return TeacherSafetyEducationResponse(
      statistics: statistics,
      studentList: studentGroups,
    );
  }

  /// 将教师端安全教育考试数据响应对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'alreadyExam': statistics.alreadyExam,
      'shouldExam': statistics.shouldExam,
      'alreadyPass': statistics.alreadyPass,
      'above': statistics.above,
      'studentList': studentList.map((group) => group.toJson()).toList(),
    };
  }
}
