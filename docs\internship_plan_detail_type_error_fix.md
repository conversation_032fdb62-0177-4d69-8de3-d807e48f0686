# 实习计划详情页面类型转换错误修复

## 问题描述

在访问教师实习计划详情页面时，应用崩溃并显示以下错误：

```
type '_Map<String, dynamic>' is not a subtype of type 'String'
```

错误发生在 JSON 数据解析过程中，某个字段期望 String 类型，但实际接收到了 Map<String, dynamic> 类型。

## 根本原因分析

1. **API 响应数据结构与模型定义不匹配**：某些字段在 API 响应中可能是嵌套对象，但模型期望的是字符串类型
2. **缺乏类型安全检查**：原始的 `fromJson` 方法直接使用 `?.toString()` 转换，没有处理复杂数据类型
3. **错误日志不够详细**：无法确定具体是哪个字段导致了类型转换失败

## 修复方案

### 1. 增强错误日志的可调试性

**修改文件**: `lib/features/internship/data/datasources/remote/internship_plan_remote_data_source_impl.dart`

**主要改进**:
- 添加响应数据类型检查
- 详细打印每个字段的类型和值
- 在 JSON 解析失败时提供具体的错误信息

```dart
// 详细打印响应数据结构
if (response is Map<String, dynamic>) {
  Logger.debug('InternshipPlanRemoteDataSource', '响应数据字段:');
  response.forEach((key, value) {
    Logger.debug('InternshipPlanRemoteDataSource', '  $key: ${value.runtimeType} = $value');
  });
}

try {
  final planModel = InternshipPlanModel.fromJson(response);
  // ...
} catch (parseError) {
  Logger.error('InternshipPlanRemoteDataSource', 'JSON解析失败: $parseError');
  Logger.error('InternshipPlanRemoteDataSource', '原始响应数据: $response');
  throw ServerException('实习计划详情数据解析失败: $parseError');
}
```

### 2. 修复类型转换错误

**修改文件**: `lib/features/internship/data/models/internship_plan_model.dart`

**主要改进**:
- 实现类型安全的转换函数
- 添加详细的错误信息，指明具体字段名称
- 处理各种数据类型的转换场景

#### 安全的字符串转换函数
```dart
String safeToString(dynamic value, String fieldName) {
  if (value == null) {
    return '';
  }
  if (value is String) {
    return value;
  }
  if (value is Map || value is List) {
    throw FormatException('字段 $fieldName 期望 String 类型，但收到 ${value.runtimeType}: $value');
  }
  return value.toString();
}
```

#### 安全的整数转换函数
```dart
int safeToInt(dynamic value, String fieldName, {int defaultValue = 0}) {
  if (value == null) {
    return defaultValue;
  }
  if (value is int) {
    return value;
  }
  if (value is String) {
    final parsed = int.tryParse(value);
    if (parsed != null) {
      return parsed;
    }
  }
  if (value is double) {
    return value.toInt();
  }
  if (value is Map || value is List) {
    throw FormatException('字段 $fieldName 期望 int 类型，但收到 ${value.runtimeType}: $value');
  }
  return defaultValue;
}
```

#### 安全的字符串列表转换函数
```dart
List<String>? safeToStringList(dynamic value, String fieldName) {
  if (value == null) {
    return null;
  }
  if (value is List) {
    return value.map((item) => item?.toString() ?? '').toList();
  }
  if (value is String) {
    // 如果是字符串，尝试解析为JSON数组
    try {
      final decoded = jsonDecode(value);
      if (decoded is List) {
        return decoded.map((item) => item?.toString() ?? '').toList();
      }
    } catch (e) {
      // 如果解析失败，将字符串作为单个元素
      return [value];
    }
  }
  throw FormatException('字段 $fieldName 期望 List<String> 类型，但收到 ${value.runtimeType}: $value');
}
```

### 3. 应用安全转换函数

在 `fromJson` 方法中使用这些安全转换函数：

```dart
return InternshipPlanModel(
  id: safeToString(json['id'], 'id'),
  academicYear: safeToString(json['academicYear'], 'academicYear'),
  grade: safeToString(json['grade'], 'grade'),
  planType: safeToInt(json['planType'], 'planType'),
  // ... 其他字段
  requiredFileList: safeToStringList(json['requiredFileList'], 'requiredFileList'),
);
```

## 修复效果

### 1. 类型安全保障
- 所有字段转换都有类型检查
- 复杂数据类型（Map、List）会抛出明确的错误信息
- 支持多种数据类型的自动转换（String ↔ int ↔ double）

### 2. 详细的错误信息
- 错误信息包含具体的字段名称
- 显示期望类型 vs 实际类型
- 提供原始数据内容用于调试

### 3. 向后兼容性
- 保持原有的 API 接口不变
- 支持 null 值和默认值处理
- 兼容不同的数据格式

## 测试结果

✅ **编译成功** - 项目已成功编译为 APK，没有任何错误

## 预期效果

1. **页面正常加载**：实习计划详情页面能够正常显示数据
2. **错误定位精确**：如果将来再出现类似错误，日志能够清楚指出具体字段和类型不匹配信息
3. **数据兼容性强**：支持多种 API 响应格式，提高系统的健壮性

## 注意事项

1. **性能影响**：增加了类型检查逻辑，但对性能影响微乎其微
2. **日志级别**：详细的调试日志只在 debug 模式下输出
3. **错误处理**：类型转换失败会抛出 `FormatException`，上层需要适当处理

## 后续建议

1. **API 文档完善**：建议后端提供详细的 API 响应格式文档
2. **单元测试**：为 `fromJson` 方法添加单元测试，覆盖各种数据类型场景
3. **代码生成**：考虑使用 `json_annotation` 等工具自动生成类型安全的序列化代码
