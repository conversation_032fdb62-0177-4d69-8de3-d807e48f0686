import 'package:flutter/material.dart';

/// 安全教育统计卡片组件
///
/// 用于展示安全教育相关的统计数据，如已考试人数、已及格人数、高分人数等
/// 支持显示右侧分隔线，用于在统计卡片区域中分隔不同的统计项
///
/// <AUTHOR>
/// @date 2025-04-28
/// @version 1.0
class SafetyStatCard extends StatelessWidget {
  /// 卡片图标
  final IconData icon;

  /// 卡片标签文本
  final String label;

  /// 分子数值（如已考试人数）
  final int numerator;

  /// 分母数值（如总人数）
  final int denominator;

  /// 卡片主题颜色
  final Color color;

  /// 是否显示右侧边框
  final bool showRightBorder;

  const SafetyStatCard({
    Key? key,
    required this.icon,
    required this.label,
    required this.numerator,
    required this.denominator,
    required this.color,
    this.showRightBorder = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: Colors.white,
        border: showRightBorder
            ? const Border(
                right: BorderSide(
                  color: Color(0xFFEEEEEE),
                  width: 0.5,
                ),
              )
            : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Center(
              child: Icon(icon, color: Colors.white, size: 24),
            ),
          ),
          const SizedBox(width: 10),

          // 标签和数值
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标签
              Text(
                label,
                style: const TextStyle(
                  fontSize: 13,
                  color: Color(0xFF666666),
                ),
              ),
              const SizedBox(height: 4),

              // 数值
              Text(
                '$numerator/$denominator',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF333333),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
