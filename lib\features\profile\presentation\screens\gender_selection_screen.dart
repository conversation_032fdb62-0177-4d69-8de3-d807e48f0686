import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 性别选择页面
/// 
/// 用于选择用户性别，包含男/女两个选项
class GenderSelectionScreen extends StatefulWidget {
  /// 当前选中的性别值
  final int initialGender;

  const GenderSelectionScreen({
    Key? key,
    required this.initialGender,
  }) : super(key: key);

  @override
  State<GenderSelectionScreen> createState() => _GenderSelectionScreenState();
}

class _GenderSelectionScreenState extends State<GenderSelectionScreen> {
  /// 选中的性别值
  late int _selectedGender;

  @override
  void initState() {
    super.initState();
    _selectedGender = widget.initialGender;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: '设置性别',
        backgroundColor: Colors.white,
        titleColor: AppTheme.black333,
        backIconColor: AppTheme.black333,
        actions: [
          TextButton(
            onPressed: () {
              // 返回选中的性别值
              Navigator.of(context).pop(_selectedGender);
            },
            child: Text(
              '确定',
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 32.sp,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
          SizedBox(width: 25.w),
        ],
      ),
      body: Column(
        children: [
          // 男性选项
          _buildGenderOption(
            label: '男',
            value: 0,
          ),
          Divider(
            height: 1,
            color: Colors.grey[200],
          ),
          // 女性选项
          _buildGenderOption(
            label: '女',
            value: 1,
          ),
        ],
      ),
    );
  }

  /// 构建性别选项
  Widget _buildGenderOption({
    required String label,
    required int value,
  }) {
    final bool isSelected = _selectedGender == value;

    return InkWell(
      onTap: () {
        setState(() {
          _selectedGender = value;
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 30.h),
        color: Colors.white,
        child: Row(
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 32.sp,
                color: AppTheme.black333,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(
                Icons.check,
                color: AppTheme.primaryColor,
                size: 40.sp,
              ),
          ],
        ),
      ),
    );
  }
}
