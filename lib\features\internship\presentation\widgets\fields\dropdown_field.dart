/// -----
/// dropdown_field.dart
///
/// 下拉选择字段组件
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/form_field_item.dart';
import 'package:flutter_demo/features/internship/presentation/utils/dialog_utils.dart';

/// 下拉选择字段组件
class DropdownField extends StatelessWidget {
  /// 字段标签
  final String label;
  
  /// 当前值
  final String value;
  
  /// 选项列表
  final List<String> items;
  
  /// 占位符文本
  final String placeholder;
  
  /// 值变化回调
  final Function(String) onChanged;
  
  /// 是否显示分隔线
  final bool showDivider;

  const DropdownField({
    super.key,
    required this.label,
    required this.value,
    required this.items,
    this.placeholder = '请选择',
    required this.onChanged,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return FormFieldItem(
      label: label,
      value: value.isEmpty ? placeholder : value,
      type: FormFieldType.dropdown,
      dropdownItems: items,
      showDivider: showDivider,
      onTap: () => _showDropdownPicker(context),
    );
  }

  /// 显示下拉选择器
  void _showDropdownPicker(BuildContext context) {
    DialogUtils.showDropdownDialog(
      context,
      title: label,
      items: items,
      onSelected: onChanged,
    );
  }
}
