/// -----
/// get_exam_detail_usecase.dart
/// 
/// 获取考试详情用例
///
/// <AUTHOR>
/// @date 2025-06-19
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/safety/domain/models/exam_detail_response.dart';
import 'package:flutter_demo/features/safety/domain/repositories/safety_exam_repository.dart';

/// 获取考试详情用例
///
/// 封装获取学生考试详情的业务逻辑
class GetExamDetailUseCase {
  /// 安全教育考试仓库
  final SafetyExamRepository repository;

  /// 构造函数
  const GetExamDetailUseCase({
    required this.repository,
  });

  /// 执行用例，获取学生考试详情
  ///
  /// [recordId] 考试记录ID
  /// 返回考试详情数据
  Future<ExamDetailResponse> call(String recordId) async {
    return await repository.getExamDetail(recordId);
  }
}
