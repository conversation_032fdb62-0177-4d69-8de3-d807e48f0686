/// -----
/// exam_detail_event.dart
/// 
/// 考试详情事件类
///
/// <AUTHOR>
/// @date 2025-06-19
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 考试详情事件基类
///
/// 所有考试详情相关事件的基类
abstract class ExamDetailEvent extends Equatable {
  /// 构造函数
  const ExamDetailEvent();

  @override
  List<Object?> get props => [];
}

/// 加载考试详情事件
///
/// 触发加载学生考试详情的操作
class LoadExamDetailEvent extends ExamDetailEvent {
  /// 考试记录ID
  final String recordId;

  /// 构造函数
  const LoadExamDetailEvent({
    required this.recordId,
  });

  @override
  List<Object?> get props => [recordId];
}

/// 刷新考试详情事件
///
/// 触发刷新学生考试详情的操作
class RefreshExamDetailEvent extends ExamDetailEvent {
  /// 考试记录ID
  final String recordId;

  /// 构造函数
  const RefreshExamDetailEvent({
    required this.recordId,
  });

  @override
  List<Object?> get props => [recordId];
}
