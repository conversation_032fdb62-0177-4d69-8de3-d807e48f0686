/// -----
/// student_file_approval_repository.dart
///
/// 学生文件审批仓库接口
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../entities/student_file_approval.dart';

/// 学生文件审批仓库接口
abstract class StudentFileApprovalRepository {
  /// 获取学生文件审批列表
  /// 
  /// [planId] 实习计划ID
  /// [type] 审批类型（0:待审批，1:已经审批）
  /// 返回 [Either<Failure, List<StudentFileApproval>>]
  Future<Either<Failure, List<StudentFileApproval>>> getStudentFileApprovalList(
    String planId,
    int type,
  );
}
