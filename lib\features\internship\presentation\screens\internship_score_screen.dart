/// -----
/// internship_score_screen.dart
/// 
/// 实习成绩页面，展示学生的实习成绩信息，包括待评分和已评分两个标签页
///
/// <AUTHOR>
/// @date 2025-05-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_score.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/score/internship_score_state.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/internship_score_item.dart';

/// 实习成绩页面
///
/// 展示学生的实习成绩信息，包括待评分和已评分两个标签页
class InternshipScoreScreen extends StatefulWidget {
  const InternshipScoreScreen({Key? key}) : super(key: key);

  @override
  State<InternshipScoreScreen> createState() => _InternshipScoreScreenState();
}

class _InternshipScoreScreenState extends State<InternshipScoreScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late InternshipScoreBloc _scoreBloc;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _scoreBloc = InternshipScoreBloc();
    
    // 加载实习成绩列表
    _scoreBloc.add(const LoadInternshipScoreEvent(courseId: '1'));
    
    // 监听标签页切换
    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _scoreBloc.close();
    super.dispose();
  }

  /// 处理标签页切换
  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      _scoreBloc.add(SwitchInternshipScoreTabEvent(tabIndex: _tabController.index));
    }
  }

  /// 处理课程变更
  void _handleCourseChanged(String newCourse) {
    // 在实际项目中，这里需要根据课程名称获取课程ID
    final courseId = newCourse.hashCode.toString();
    _scoreBloc.add(ChangeCourseEvent(courseId: courseId, courseName: newCourse));
  }

  /// 处理评分
  void _handleRate(String studentId) {
    // 实际项目中，这里应该跳转到评分页面
    // 这里简单模拟一个评分操作
    _scoreBloc.add(RateInternshipScoreEvent(
      studentId: studentId,
      courseId: '1',
      score: 60,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => _scoreBloc,
      child: Scaffold(
        backgroundColor: Colors.grey[100],
        appBar: const CustomAppBar(
          title: '实习成绩',
        ),
        body: BlocBuilder<InternshipScoreBloc, InternshipScoreState>(
          builder: (context, state) {
            if (state is InternshipScoreInitial || 
                (state is InternshipScoreLoading && state.isFirstLoad)) {
              return const Center(child: CircularProgressIndicator());
            }
            
            if (state is InternshipScoreError) {
              return Center(child: Text('加载失败: ${state.message}'));
            }
            
            // 加载成功或加载中但非首次加载
            final scores = state is InternshipScoreLoaded 
                ? state.scores 
                : (state as InternshipScoreLoading).oldScores;
            
            final courseName = state is InternshipScoreLoaded 
                ? state.courseName 
                : '加载中...';
            
            final List<String> availableCourses = state is InternshipScoreLoaded 
                ? state.availableCourses 
                : <String>[];
            
            final pendingCount = state is InternshipScoreLoaded 
                ? state.pendingCount 
                : 0;
            
            // 设置当前标签页
            if (state is InternshipScoreLoaded && 
                _tabController.index != state.currentTabIndex) {
              _tabController.animateTo(state.currentTabIndex);
            }
            
            return Column(
              children: [
                // 课程头部
                CourseHeaderSection(
                  courseName: courseName,
                  availableCourses: availableCourses,
                  onCourseChanged: _handleCourseChanged,
                ),
                
                // 标签栏
                ApprovalTabBar(
                  controller: _tabController,
                  pendingCount: pendingCount,
                  pendingText: '待评分',
                  approvedText: '已评分',
                ),
                
                // 标签页内容
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      // 待评分列表
                      _buildScoreList(
                        scores.where((s) => s.score == null).toList(),
                        isPending: true,
                      ),
                      
                      // 已评分列表
                      _buildScoreList(
                        scores.where((s) => s.score != null).toList(),
                        isPending: false,
                      ),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 构建成绩列表
  Widget _buildScoreList(List<InternshipScore> scores, {required bool isPending}) {
    if (scores.isEmpty) {
      return Center(
        child: Text(isPending ? '暂无待评分学生' : '暂无已评分学生'),
      );
    }
    
    return ListView.builder(
      itemCount: scores.length,
      itemBuilder: (context, index) {
        final score = scores[index];
        return InternshipScoreItem(
          studentName: score.studentName,
          phoneNumber: score.phoneNumber,
          avatarUrl: score.avatarUrl,
          scoreStatus: '实习考核成绩: 待您评分',
          score: score.score,
          isPending: isPending,
          onRatePressed: isPending ? () => _handleRate(score.id) : null,
        );
      },
    );
  }
}
