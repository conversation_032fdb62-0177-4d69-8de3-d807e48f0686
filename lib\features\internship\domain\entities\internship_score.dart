/// -----
/// internship_score.dart
/// 
/// 实习成绩实体类，表示学生的实习成绩信息
///
/// <AUTHOR>
/// @date 2025-05-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习成绩实体类
///
/// 包含学生的基本信息和实习成绩信息
class InternshipScore extends Equatable {
  /// 学生ID
  final String id;
  
  /// 学生姓名
  final String studentName;
  
  /// 学生电话
  final String phoneNumber;
  
  /// 学生头像URL
  final String avatarUrl;
  
  /// 成绩分数，如果为null则表示未评分
  final int? score;
  
  /// 创建实习成绩实体
  const InternshipScore({
    required this.id,
    required this.studentName,
    required this.phoneNumber,
    required this.avatarUrl,
    this.score,
  });
  
  /// 判断是否已评分
  bool get isRated => score != null;
  
  @override
  List<Object?> get props => [id, studentName, phoneNumber, avatarUrl, score];
  
  /// 创建一个新的实习成绩实体，可更新部分属性
  InternshipScore copyWith({
    String? id,
    String? studentName,
    String? phoneNumber,
    String? avatarUrl,
    int? score,
    bool clearScore = false,
  }) {
    return InternshipScore(
      id: id ?? this.id,
      studentName: studentName ?? this.studentName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      score: clearScore ? null : (score ?? this.score),
    );
  }
}
