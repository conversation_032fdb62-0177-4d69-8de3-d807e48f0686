/// -----
/// free_internship_approval_bloc.dart
///
/// 免实习申请审批BLoC
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../../core/utils/logger.dart';
import '../../../domain/entities/free_internship_approval_request.dart';
import '../../../domain/usecases/approve_free_internship_exempt_usecase.dart';
import 'free_internship_approval_event.dart';
import 'free_internship_approval_state.dart';

/// 免实习申请审批BLoC
/// 
/// 管理免实习申请审批的状态和业务逻辑
class FreeInternshipApprovalBloc extends Bloc<FreeInternshipApprovalEvent, FreeInternshipApprovalState> {
  FreeInternshipApprovalBloc({
    required ApproveFreeInternshipExemptUseCase approveFreeInternshipExemptUseCase,
  })  : _approveFreeInternshipExemptUseCase = approveFreeInternshipExemptUseCase,
        super(FreeInternshipApprovalInitial()) {
    on<ApproveFreeInternshipEvent>(_onApproveFreeInternship);
  }

  final ApproveFreeInternshipExemptUseCase _approveFreeInternshipExemptUseCase;

  /// 处理审批免实习申请事件
  Future<void> _onApproveFreeInternship(
    ApproveFreeInternshipEvent event,
    Emitter<FreeInternshipApprovalState> emit,
  ) async {
    try {
      Logger.info('FreeInternshipApprovalBloc', '开始审批免实习申请 - ID: ${event.id}, 通过: ${event.isApproved}');
      
      emit(FreeInternshipApprovalLoading());

      // 创建审批请求
      final request = event.isApproved
          ? FreeInternshipApprovalRequest.approve(
              id: event.id,
              reviewOpinion: event.reviewOpinion,
            )
          : FreeInternshipApprovalRequest.reject(
              id: event.id,
              reviewOpinion: event.reviewOpinion,
            );

      // 执行审批
      final result = await _approveFreeInternshipExemptUseCase(
        ApproveFreeInternshipExemptParams(request: request),
      );

      result.fold(
        (failure) {
          Logger.error('FreeInternshipApprovalBloc', '审批免实习申请失败: ${failure.message}');
          emit(FreeInternshipApprovalError(message: failure.message));
        },
        (_) {
          final message = event.isApproved ? '审批通过成功' : '审批驳回成功';
          Logger.info('FreeInternshipApprovalBloc', message);
          emit(FreeInternshipApprovalSuccess(message: message));
        },
      );
    } catch (e) {
      Logger.error('FreeInternshipApprovalBloc', '审批免实习申请时发生未知错误: $e');
      emit(const FreeInternshipApprovalError(message: '审批失败，请稍后重试'));
    }
  }
}
