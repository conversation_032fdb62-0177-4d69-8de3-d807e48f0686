/// -----
/// progress_ring_content.dart
/// 
/// 圆环进度内容显示组件，只负责显示中心文字内容
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

/// 圆环进度内容显示组件
/// 
/// 只负责显示中心文字内容，可以独立使用或与圆环组件组合使用
class ProgressRingContent extends StatelessWidget {
  /// 主要文本内容
  final String mainText;
  
  /// 副标题文本
  final String? subtitle;
  
  /// 主要文本样式
  final TextStyle? mainTextStyle;
  
  /// 副标题文本样式
  final TextStyle? subtitleStyle;
  
  /// 主要文本和副标题之间的间距
  final double spacing;
  
  /// 内容的对齐方式
  final MainAxisAlignment alignment;
  
  /// 是否启用文本动画
  final bool enableAnimation;
  
  /// 动画持续时间
  final Duration animationDuration;

  const ProgressRingContent({
    Key? key,
    required this.mainText,
    this.subtitle,
    this.mainTextStyle,
    this.subtitleStyle,
    this.spacing = 12,
    this.alignment = MainAxisAlignment.center,
    this.enableAnimation = false,
    this.animationDuration = const Duration(milliseconds: 600),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final defaultMainTextStyle = TextStyle(
      fontSize: 80.sp,
      fontWeight: FontWeight.bold,
      color: AppTheme.primaryColor,
      height: 0.9,
    );

    final defaultSubtitleStyle = TextStyle(
      fontSize: 24.sp,
      color: AppTheme.black666,
      height: 1.1,
    );

    if (enableAnimation) {
      return _AnimatedContent(
        mainText: mainText,
        subtitle: subtitle,
        mainTextStyle: mainTextStyle ?? defaultMainTextStyle,
        subtitleStyle: subtitleStyle ?? defaultSubtitleStyle,
        spacing: spacing,
        alignment: alignment,
        animationDuration: animationDuration,
      );
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: alignment,
      children: [
        // 主要文本
        Text(
          mainText,
          style: mainTextStyle ?? defaultMainTextStyle,
          textAlign: TextAlign.center,
        ),
        
        // 间距和副标题
        if (subtitle != null) ...[
          SizedBox(height: spacing.h),
          SizedBox(
            width: double.infinity,
            child: Text(
              subtitle!,
              textAlign: TextAlign.center,
              style: subtitleStyle ?? defaultSubtitleStyle,
            ),
          ),
        ],
      ],
    );
  }
}

/// 带动画的内容显示组件
class _AnimatedContent extends StatefulWidget {
  final String mainText;
  final String? subtitle;
  final TextStyle mainTextStyle;
  final TextStyle subtitleStyle;
  final double spacing;
  final MainAxisAlignment alignment;
  final Duration animationDuration;

  const _AnimatedContent({
    Key? key,
    required this.mainText,
    this.subtitle,
    required this.mainTextStyle,
    required this.subtitleStyle,
    required this.spacing,
    required this.alignment,
    required this.animationDuration,
  }) : super(key: key);

  @override
  _AnimatedContentState createState() => _AnimatedContentState();
}

class _AnimatedContentState extends State<_AnimatedContent>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    _fadeController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // 延迟启动动画，让圆环先开始
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) {
        _fadeController.forward();
        _scaleController.forward();
      }
    });
  }

  @override
  void didUpdateWidget(_AnimatedContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.mainText != widget.mainText) {
      _fadeController.forward(from: 0);
      _scaleController.forward(from: 0);
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_fadeAnimation, _scaleAnimation]),
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: widget.alignment,
              children: [
                // 主要文本
                Text(
                  widget.mainText,
                  style: widget.mainTextStyle,
                  textAlign: TextAlign.center,
                ),
                
                // 间距和副标题
                if (widget.subtitle != null) ...[
                  SizedBox(height: widget.spacing.h),
                  SizedBox(
                    width: double.infinity,
                    child: Text(
                      widget.subtitle!,
                      textAlign: TextAlign.center,
                      style: widget.subtitleStyle,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
}

/// 预定义的内容样式
class ProgressRingContentStyles {
  /// 分数样式（用于评分显示）
  static TextStyle scoreMainTextStyle = TextStyle(
    fontSize: 80.sp,
    fontWeight: FontWeight.bold,
    color: AppTheme.primaryColor,
    height: 0.9,
  );

  /// 计数样式（用于签到天数等）
  static TextStyle countMainTextStyle = TextStyle(
    fontSize: 28.sp,
    fontWeight: FontWeight.bold,
    color: Colors.black,
    height: 1.0,
  );

  /// 百分比样式
  static TextStyle percentageMainTextStyle = TextStyle(
    fontSize: 60.sp,
    fontWeight: FontWeight.bold,
    color: AppTheme.primaryColor,
    height: 0.9,
  );

  /// 标准副标题样式
  static TextStyle standardSubtitleStyle = TextStyle(
    fontSize: 24.sp,
    color: AppTheme.black666,
    height: 1.1,
  );

  /// 小号副标题样式
  static TextStyle smallSubtitleStyle = TextStyle(
    fontSize: 20.sp,
    color: Colors.grey[600],
    height: 1.1,
  );
}
