/// -----
/// exemption_application_data_source.dart
///
/// 免实习申请数据源接口
/// 定义免实习申请相关的数据操作方法
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/plan/data/models/exemption_application_request_model.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_response_model.dart';

/// 免实习申请数据源接口
///
/// 定义免实习申请相关的数据操作抽象方法
abstract class ExemptionApplicationDataSource {
  /// 提交免实习申请
  ///
  /// [request] 免实习申请请求数据
  /// 返回免实习申请响应数据
  /// 抛出ServerException当服务器返回错误时
  Future<ExemptionApplicationResponseModel> submitExemptionApplication(
    ExemptionApplicationRequestModel request,
  );
}
