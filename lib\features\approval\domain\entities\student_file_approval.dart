/// -----
/// student_file_approval.dart
///
/// 学生文件审批实体类
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 学生文件审批实体
class StudentFileApproval extends Equatable {
  /// 学生ID
  final String studentId;
  
  /// 学生姓名
  final String studentName;
  
  /// 文件列表
  final List<FileApprovalDetail> fileList;

  const StudentFileApproval({
    required this.studentId,
    required this.studentName,
    required this.fileList,
  });

  @override
  List<Object?> get props => [studentId, studentName, fileList];

  @override
  String toString() {
    return 'StudentFileApproval{studentId: $studentId, studentName: $studentName, fileList: ${fileList.length} files}';
  }
}

/// 文件审批详情实体
class FileApprovalDetail extends Equatable {
  /// 文件ID
  final String id;
  
  /// 计划ID
  final String planId;
  
  /// 学生ID
  final String studentId;
  
  /// 文件名称
  final String fileName;
  
  /// 文件类型
  final String fileType;
  
  /// 文件代码
  final int fileCode;
  
  /// 文件URL
  final String fileUrl;
  
  /// 文件状态 (1: 待审批, 2: 已审批)
  final int fileStatus;
  
  /// 审批人姓名
  final String? approveName;
  
  /// 教师ID
  final String? teacherId;
  
  /// 审批角色
  final String? approveRole;
  
  /// 审批角色名称
  final String? approveRoleName;
  
  /// 备注
  final String? remark;
  
  /// 创建人
  final String createPerson;
  
  /// 创建时间
  final int createTime;
  
  /// 更新人
  final String? updatePerson;
  
  /// 更新时间
  final int? updateTime;

  const FileApprovalDetail({
    required this.id,
    required this.planId,
    required this.studentId,
    required this.fileName,
    required this.fileType,
    required this.fileCode,
    required this.fileUrl,
    required this.fileStatus,
    this.approveName,
    this.teacherId,
    this.approveRole,
    this.approveRoleName,
    this.remark,
    required this.createPerson,
    required this.createTime,
    this.updatePerson,
    this.updateTime,
  });

  /// 是否为待审批状态
  bool get isPending => fileStatus == 1;

  /// 是否为已审批状态
  bool get isApproved => fileStatus == 2;

  /// 获取状态文本
  String get statusText => isPending ? '待审批' : '已审批';

  @override
  List<Object?> get props => [
        id,
        planId,
        studentId,
        fileName,
        fileType,
        fileCode,
        fileUrl,
        fileStatus,
        approveName,
        teacherId,
        approveRole,
        approveRoleName,
        remark,
        createPerson,
        createTime,
        updatePerson,
        updateTime,
      ];

  @override
  String toString() {
    return 'FileApprovalDetail{id: $id, fileName: $fileName, fileType: $fileType, fileStatus: $fileStatus}';
  }
}
