import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

/// 自定义应用栏组件
/// 提供统一的应用栏样式，支持多种自定义选项
class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// 应用栏标题
  final String title;

  /// 标题是否居中
  final bool centerTitle;

  /// 是否显示返回按钮
  final bool showBackButton;

  /// 右侧操作按钮列表
  final List<Widget>? actions;

  /// 应用栏背景颜色
  final Color? backgroundColor;

  /// 标题文字颜色
  final Color? titleColor;

  /// 应用栏阴影高度
  final double elevation;

  /// 自定义左侧组件
  final Widget? leading;

  /// 返回按钮点击回调
  final VoidCallback? onBackPressed;

  /// 标题字体大小
  final double titleFontSize;

  /// 标题字体粗细
  final FontWeight titleFontWeight;

  /// 返回按钮图标大小
  final double backIconSize;

  /// 返回按钮图标颜色
  final Color? backIconColor;

  /// 应用栏高度
  final double? height;

  /// 底部边框
  final Border? border;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.centerTitle = true,
    this.showBackButton = true,
    this.actions,
    this.backgroundColor,
    this.titleColor,
    this.elevation = 0,
    this.leading,
    this.onBackPressed,
    this.titleFontSize = 18,
    this.titleFontWeight = FontWeight.bold,
    this.backIconSize = 20,
    this.backIconColor,
    this.height,
    this.border,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(height ?? kToolbarHeight),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white ?? AppTheme.backgroundColor,
          border: border,
          boxShadow: elevation > 0
              ? [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: elevation,
                    offset: const Offset(0, 1),
                  ),
                ]
              : null,
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Row(
              children: [
                // 左侧返回按钮
                if (showBackButton)
                  leading ??
                      IconButton(
                        icon: Icon(
                          Icons.arrow_back_ios,
                          size: backIconSize,
                          color: backIconColor ?? AppTheme.textPrimaryColor,
                        ),
                        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                      ),
                if (!showBackButton && leading != null) leading!,

                // 标题
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(
                      title,
                      style: TextStyle(
                        color: titleColor ?? AppTheme.textPrimaryColor,
                        fontSize: titleFontSize,
                        fontWeight: titleFontWeight,
                      ),
                      textAlign: centerTitle ? TextAlign.center : TextAlign.start,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),

                // 右侧操作按钮
                if (actions != null) ...actions!,
                if (actions == null || actions!.isEmpty)
                  const SizedBox(width: 48), // 保持对称性
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height ?? kToolbarHeight);
}