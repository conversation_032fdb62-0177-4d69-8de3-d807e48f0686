/// -----
/// insurance_event.dart
/// 
/// 实习保险相关事件定义
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习保险事件基类
abstract class InsuranceEvent extends Equatable {
  const InsuranceEvent();

  @override
  List<Object?> get props => [];
}

/// 加载实习保险信息事件
class LoadInsuranceInfoEvent extends InsuranceEvent {
  /// 课程ID
  final String courseId;

  const LoadInsuranceInfoEvent({required this.courseId});

  @override
  List<Object?> get props => [courseId];
}

/// 切换课程事件
class ChangeCourseEvent extends InsuranceEvent {
  /// 新的课程ID
  final String newCourseId;

  const ChangeCourseEvent({required this.newCourseId});

  @override
  List<Object?> get props => [newCourseId];
}

/// 查看保险合同事件
class ViewInsuranceContractEvent extends InsuranceEvent {
  /// 文件路径
  final String filePath;
  
  /// 文件名
  final String fileName;

  const ViewInsuranceContractEvent({
    required this.filePath,
    required this.fileName,
  });

  @override
  List<Object?> get props => [filePath, fileName];
}
