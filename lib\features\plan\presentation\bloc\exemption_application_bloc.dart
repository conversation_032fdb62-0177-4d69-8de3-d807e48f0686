/// -----
/// exemption_application_bloc.dart
///
/// 免实习申请BLoC
/// 处理免实习申请相关的业务逻辑和状态管理
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// 第三方库
import 'package:flutter_bloc/flutter_bloc.dart';

// 项目内部库
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/plan/domain/usecases/submit_exemption_application_usecase.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/exemption_application_event.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/exemption_application_state.dart';

/// 免实习申请BLoC
///
/// 处理免实习申请相关的业务逻辑，包括提交申请、状态管理等功能
class ExemptionApplicationBloc extends Bloc<ExemptionApplicationEvent, ExemptionApplicationState> {
  static const String _tag = 'ExemptionApplicationBloc';
  
  final SubmitExemptionApplicationUseCase _submitExemptionApplicationUseCase;

  ExemptionApplicationBloc({
    required SubmitExemptionApplicationUseCase submitExemptionApplicationUseCase,
  }) : _submitExemptionApplicationUseCase = submitExemptionApplicationUseCase,
       super(const ExemptionApplicationInitialState()) {
    on<SubmitExemptionApplicationEvent>(_onSubmitExemptionApplication);
    on<ResetExemptionApplicationEvent>(_onResetExemptionApplication);
  }

  /// 处理提交免实习申请事件
  Future<void> _onSubmitExemptionApplication(
    SubmitExemptionApplicationEvent event,
    Emitter<ExemptionApplicationState> emit,
  ) async {
    Logger.info(_tag, '开始提交免实习申请');

    emit(const ExemptionApplicationSubmittingState());

    try {
      // 调用用例提交申请
      final result = await _submitExemptionApplicationUseCase(
        SubmitExemptionApplicationParams(
          planId: event.planId,
          reason: event.reason,
          fileUrl: event.fileUrl,
        ),
      );

      result.fold(
        (failure) {
          Logger.error(_tag, '提交免实习申请失败: ${failure.message}');
          emit(ExemptionApplicationFailureState(
            message: failure.message,
          ));
        },
        (response) {
          Logger.info(_tag, '成功提交免实习申请: ${response.toString()}');
          emit(ExemptionApplicationSubmittedState(
            message: '免实习申请提交成功，请等待审核！',
            applicationId: response.data,
          ));
        },
      );
    } catch (e) {
      Logger.error(_tag, '提交免实习申请异常: $e');
      emit(ExemptionApplicationFailureState(
        message: '提交免实习申请失败: ${e.toString()}',
      ));
    }
  }

  /// 处理重置申请状态事件
  void _onResetExemptionApplication(
    ResetExemptionApplicationEvent event,
    Emitter<ExemptionApplicationState> emit,
  ) {
    Logger.info(_tag, '重置免实习申请状态');
    emit(const ExemptionApplicationInitialState());
  }
}
