/// -----
/// safety_exam_question.dart
///
/// 安全教育考试题目模型
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:convert';
import 'package:equatable/equatable.dart';

/// 安全教育考试题目选项模型
///
/// 表示试题中的一个选项，包含选项标识和内容
class ExamOption extends Equatable {
  /// 选项标识，如A、B、C、D
  final String id;

  /// 选项内容
  final String content;

  /// 构造函数
  const ExamOption({
    required this.id,
    required this.content,
  });

  @override
  List<Object?> get props => [id, content];

  /// 从JSON映射创建选项对象
  factory ExamOption.fromJson(Map<String, dynamic> json) {
    return ExamOption(
      id: json['id'] as String,
      content: json['content'] as String,
    );
  }

  /// 将选项对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
    };
  }
}

/// 安全教育考试题目类型枚举
enum QuestionType {
  /// 单选题
  singleChoice,
  
  /// 多选题
  multipleChoice,
  
  /// 判断题
  judgment,
  
  /// 填空题
  fillBlank,
}

/// 安全教育考试题目模型
///
/// 表示一道完整的考试题目，包含题目内容、选项、答案等信息
class SafetyExamQuestion extends Equatable {
  /// 题目ID
  final String id;

  /// 实习计划ID
  final String planId;

  /// 题目标题/内容
  final String title;

  /// 题目选项列表
  final List<ExamOption> options;

  /// 正确答案列表（选项ID列表，支持多选）
  final List<String> correctAnswers;

  /// 题目序号（自动生成）
  final int number;

  /// 构造函数
  const SafetyExamQuestion({
    required this.id,
    required this.planId,
    required this.title,
    required this.options,
    required this.correctAnswers,
    required this.number,
  });

  @override
  List<Object?> get props => [id, planId, title, options, correctAnswers, number];

  /// 判断是否为多选题
  bool get isMultipleChoice => correctAnswers.length > 1;

  /// 获取题目类型
  QuestionType get type => isMultipleChoice ? QuestionType.multipleChoice : QuestionType.singleChoice;

  /// 从API JSON数据创建题目对象
  factory SafetyExamQuestion.fromJson(Map<String, dynamic> json, int questionNumber) {
    // 解析optionsJson字段
    final optionsJsonStr = json['optionsJson'] as String;
    final optionsJsonList = jsonDecode(optionsJsonStr) as List;
    final optionsMap = jsonDecode(optionsJsonList.first) as Map<String, dynamic>;

    // 转换为ExamOption列表
    final options = optionsMap.entries
        .map((entry) => ExamOption(id: entry.key, content: entry.value))
        .toList();

    // 解析correctAnswer字段，支持多选
    final correctAnswerStr = json['correctAnswer'] as String;
    final correctAnswers = correctAnswerStr.split('').toList();

    return SafetyExamQuestion(
      id: json['id'] as String,
      planId: json['planId'] as String,
      title: json['title'] as String,
      options: options,
      correctAnswers: correctAnswers,
      number: questionNumber,
    );
  }

  /// 将题目对象转换为JSON映射
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'planId': planId,
      'title': title,
      'options': options.map((e) => e.toJson()).toList(),
      'correctAnswers': correctAnswers,
      'number': number,
    };
  }


}
