/// -----
/// authenticate_usecase.dart
/// 
/// 用户认证用例，处理用户角色信息认证的业务逻辑
///
/// <AUTHOR>
/// @date 2025-05-20
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

/// 认证用例参数
class AuthenticateParams {
  final String deptName;
  final String userCode;
  final String userName;
  final String userType;

  AuthenticateParams({
    required this.deptName,
    required this.userCode,
    required this.userName,
    required this.userType,
  });
}

/// 用户认证用例
///
/// 处理用户角色信息认证的业务逻辑
class AuthenticateUseCase implements UseCase<int, AuthenticateParams> {
  final AuthRepository repository;

  AuthenticateUseCase(this.repository);

  @override
  Future<Either<Failure, int>> call(AuthenticateParams params) async {
    try {
      final result = await repository.authenticate(
        deptName: params.deptName,
        userCode: params.userCode,
        userName: params.userName,
        userType: params.userType,
      );
      return Right(result);
    } on Exception catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}
