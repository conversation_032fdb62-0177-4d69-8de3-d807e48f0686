/// -----
/// teacher_safety_education_response_model.dart
/// 
/// 教师端安全教育考试数据响应数据模型
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/models/teacher_safety_education_response.dart';

/// 安全教育考试统计数据数据模型
/// 
/// 继承自领域模型，用于数据层的JSON序列化和反序列化
class SafetyEducationStatisticsModel extends SafetyEducationStatistics {
  const SafetyEducationStatisticsModel({
    required super.alreadyExam,
    required super.shouldExam,
    required super.alreadyPass,
    required super.above,
  });

  /// 从JSON映射创建统计数据模型对象
  factory SafetyEducationStatisticsModel.fromJson(Map<String, dynamic> json) {
    return SafetyEducationStatisticsModel(
      alreadyExam: json['alreadyExam'] as int? ?? 0,
      shouldExam: json['shouldExam'] as int? ?? 0,
      alreadyPass: json['alreadyPass'] as int? ?? 0,
      above: json['above'] as int? ?? 0,
    );
  }

  /// 转换为领域模型
  SafetyEducationStatistics toDomain() {
    return SafetyEducationStatistics(
      alreadyExam: alreadyExam,
      shouldExam: shouldExam,
      alreadyPass: alreadyPass,
      above: above,
    );
  }
}

/// 学生考试信息数据模型
/// 
/// 继承自领域模型，用于数据层的JSON序列化和反序列化
class StudentExamInfoModel extends StudentExamInfo {
  const StudentExamInfoModel({
    required super.studentId,
    required super.studentName,
    required super.phone,
    super.avatar,
    required super.score,
    required super.recordId,
  });

  /// 从JSON映射创建学生考试信息模型对象
  factory StudentExamInfoModel.fromJson(Map<String, dynamic> json) {
    return StudentExamInfoModel(
      studentId: json['studentId'] as String? ?? '',
      studentName: json['studentName'] as String? ?? '',
      phone: json['phone'] as String? ?? '',
      avatar: json['avatar'] as String?,
      score: (json['score'] as num?)?.toDouble() ?? 0.0,
      recordId: json['recordId'] as String? ?? '',
    );
  }

  /// 转换为领域模型
  StudentExamInfo toDomain() {
    return StudentExamInfo(
      studentId: studentId,
      studentName: studentName,
      phone: phone,
      avatar: avatar,
      score: score,
      recordId: recordId,
    );
  }
}

/// 学生分组数据模型
/// 
/// 继承自领域模型，用于数据层的JSON序列化和反序列化
class StudentGroupModel extends StudentGroup {
  const StudentGroupModel({
    required super.name,
    required super.list,
  });

  /// 从JSON映射创建学生分组模型对象
  factory StudentGroupModel.fromJson(Map<String, dynamic> json) {
    final studentList = (json['list'] as List<dynamic>?)
        ?.map((studentJson) => StudentExamInfoModel.fromJson(studentJson as Map<String, dynamic>))
        .toList() ?? <StudentExamInfoModel>[];

    return StudentGroupModel(
      name: json['name'] as String? ?? '',
      list: studentList,
    );
  }

  /// 转换为领域模型
  StudentGroup toDomain() {
    return StudentGroup(
      name: name,
      list: list.map((student) => (student as StudentExamInfoModel).toDomain()).toList(),
    );
  }
}

/// 教师端安全教育考试数据响应数据模型
/// 
/// 继承自领域模型，用于数据层的JSON序列化和反序列化
class TeacherSafetyEducationResponseModel extends TeacherSafetyEducationResponse {
  const TeacherSafetyEducationResponseModel({
    required super.statistics,
    required super.studentList,
  });

  /// 从JSON映射创建教师端安全教育考试数据响应模型对象
  factory TeacherSafetyEducationResponseModel.fromJson(Map<String, dynamic> json) {
    final statistics = SafetyEducationStatisticsModel.fromJson(json);

    final studentGroups = (json['studentList'] as List<dynamic>?)
        ?.map((groupJson) => StudentGroupModel.fromJson(groupJson as Map<String, dynamic>))
        .toList() ?? <StudentGroupModel>[];

    return TeacherSafetyEducationResponseModel(
      statistics: statistics,
      studentList: studentGroups,
    );
  }

  /// 创建空的教师端安全教育考试数据响应模型对象
  factory TeacherSafetyEducationResponseModel.empty() {
    return const TeacherSafetyEducationResponseModel(
      statistics: SafetyEducationStatisticsModel(
        alreadyExam: 0,
        shouldExam: 0,
        alreadyPass: 0,
        above: 0,
      ),
      studentList: <StudentGroupModel>[],
    );
  }

  /// 转换为领域模型
  TeacherSafetyEducationResponse toDomain() {
    return TeacherSafetyEducationResponse(
      statistics: (statistics as SafetyEducationStatisticsModel).toDomain(),
      studentList: studentList.map((group) => (group as StudentGroupModel).toDomain()).toList(),
    );
  }
}
