import 'package:flutter/material.dart';
import 'package:flutter_demo/features/safety/domain/models/exam_detail_response.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 考试试题项组件
///
/// 用于展示单个试题的内容、选项和答题情况
///
/// <AUTHOR>
/// @date 2025-04-30
/// @version 1.0
class ExamQuestionItem extends StatelessWidget {
  /// 试题数据
  final ExamQuestionDetailRecord question;

  /// 题目序号
  final int questionNumber;

  const ExamQuestionItem({
    Key? key,
    required this.question,
    required this.questionNumber,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 判断学生答案是否正确
    final bool isCorrect = question.isCorrect == 1;

    // 答案文本颜色 - 根据学生答案是否正确来决定颜色
    final Color answerColor = isCorrect
        ? const Color(0xFF4ECB73)  // 正确时为绿色
        : const Color(0xFFFF5252); // 错误时为红色（与错误选项边框颜色一致）

    return Container(
      margin: EdgeInsets.only(bottom: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 题号
          Text(
            'NO.$questionNumber',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 8.h),

          // 题目内容
          Text(
            question.title,
            style: TextStyle(
              fontSize: 30.sp,
              color: const Color(0xFF333333),
            ),
          ),

          // 正确答案提示
          Row(
            children: [
              Text(
                '(答案${question.correctAnswer})',
                style: TextStyle(
                  fontSize: 30.sp,
                  color: answerColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),

          // 选项列表
          ...question.optionsMap.entries.map<Widget>((optionEntry) {
            final optionKey = optionEntry.key;
            final optionValue = optionEntry.value;

            // 判断选项状态 - 支持多选题
            bool isStudentSelected = question.studentAnswer.contains(optionKey);
            bool isCorrectOption = question.correctAnswer.contains(optionKey);
            
            // 设置选项背景颜色和边框颜色
            Color backgroundColor;
            Color borderColor;
            Color textColor;
            
            if (isStudentSelected && isCorrectOption) {
              // 学生选择了正确答案
              backgroundColor = const Color(0xFFE8F5E9);
              borderColor = const Color(0xFF4ECB73);
              textColor = const Color(0xFF4ECB73);
            } else if (isStudentSelected && !isCorrectOption) {
              // 学生选择了错误答案
              backgroundColor = const Color(0xFFFFEBEE);
              borderColor = const Color(0xFFFF5252);
              textColor = const Color(0xFFFF5252);
            } else if (isCorrectOption) {
              // 正确答案（但学生未选择）
              backgroundColor = const Color(0xFFE8F5E9);
              borderColor = const Color(0xFF4ECB73);
              textColor = const Color(0xFF4ECB73);
            } else {
              // 普通选项 - 有背景色但无边框
              backgroundColor = const Color(0xFFF8F8F8);
              borderColor = Colors.transparent;
              textColor = const Color(0xFF666666);
            }
            
            return Container(
              margin: EdgeInsets.only(bottom: 20.h),
              decoration: BoxDecoration(
                color: backgroundColor,
                borderRadius: BorderRadius.circular(10.r),
                border: Border.all(
                  color: borderColor,
                  width: 1,
                ),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(10.r),
                  onTap: () {
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 16.w,
                      vertical: 28.h,
                    ),
                    child: Row(
                      children: [
                        Text(
                          '$optionKey: ',
                          style: TextStyle(
                            fontSize: 26.sp,
                            color: textColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            optionValue,
                            style: TextStyle(
                              fontSize: 26.sp,
                              color: textColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
          
          SizedBox(height: 8.h),
        ],
      ),
    );
  }
}
