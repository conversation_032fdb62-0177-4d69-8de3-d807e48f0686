/// -----
/// exemption_approval_screen.dart
/// 
/// 免签申请审批页面，用于审批待处理的免签申请
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';

class ExemptionApprovalScreen extends StatefulWidget {
  final String name;
  final String company;
  final String department;
  final String startDate;
  final String endDate;
  final String days;
  final String reason;

  const ExemptionApprovalScreen({
    Key? key,
    required this.name,
    required this.company,
    required this.department,
    required this.startDate,
    required this.endDate,
    required this.days,
    required this.reason,
  }) : super(key: key);

  @override
  State<ExemptionApprovalScreen> createState() => _ExemptionApprovalScreenState();
}

class _ExemptionApprovalScreenState extends State<ExemptionApprovalScreen> {
  final TextEditingController _rejectReasonController = TextEditingController();

  @override
  void dispose() {
    _rejectReasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '免签申请',
        centerTitle: true,
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildUserInfo(),
            _buildDivider(),
            _buildExemptionInfo(),
            _buildDivider(),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '请输入驳回理由:',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                  const SizedBox(height: 10),
                  TextField(
                    controller: _rejectReasonController,
                    maxLines: 4,
                    decoration: InputDecoration(
                      hintText: '请输入驳回原因...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey[300]!),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: AppTheme.primaryColor),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 40),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          const CircleAvatar(
            radius: 25,
            child: Text('头像'),
          ),
          const SizedBox(width: 16),
          Text(
            widget.name,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExemptionInfo() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '免签申请信息',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('实习单位:', widget.company),
          const SizedBox(height: 12),
          _buildInfoRow('实习岗位:', widget.department),
          const SizedBox(height: 12),
          _buildInfoRow('起止时间:', '${widget.startDate}-${widget.endDate} ${widget.days}'),
          const SizedBox(height: 12),
          _buildInfoRow('免签原因:', widget.reason),
          const SizedBox(height: 20),
          // 添加图片占位
          Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: Icon(
                Icons.image,
                size: 50,
                color: Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 16,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 8,
      color: Colors.grey[200],
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                _handleReject();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text(
                '驳回',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                _handleApprove();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text(
                '通过',
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleReject() {
    final rejectReason = _rejectReasonController.text.trim();
    if (rejectReason.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入驳回理由')),
      );
      return;
    }
    
    // 处理驳回逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('已驳回免签申请')),
    );
    Navigator.pop(context, {'approved': false, 'reason': rejectReason});
  }

  void _handleApprove() {
    // 处理通过逻辑
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('已通过免签申请')),
    );
    Navigator.pop(context, {'approved': true});
  }
} 