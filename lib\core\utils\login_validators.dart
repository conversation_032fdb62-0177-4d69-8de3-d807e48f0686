class Validators {
  // 验证手机号
  static String? validateMobile(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入手机号';
    }
    
    final RegExp mobileRegExp = RegExp(r'^1[3-9]\d{9}$');
    if (!mobileRegExp.hasMatch(value)) {
      return '请输入有效的手机号';
    }
    
    return null;
  }
  
  // 验证密码
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入密码';
    }
    
    if (value.length < 6) {
      return '密码长度不能少于6位';
    }
    
    return null;
  }
  
  // 验证验证码
  static String? validateVerificationCode(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入验证码';
    }
    
    if (value.length != 4 || int.tryParse(value) == null) {
      return '请输入至少位数字验证码';
    }
    
    return null;
  }
  
  // 验证非空字段
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '请输入$fieldName';
    }
    
    return null;
  }
  
  // 验证学号
  static String? validateStudentId(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入学号';
    }
    
    if (value.length < 5 || int.tryParse(value) == null) {
      return '请输入有效的学号';
    }
    
    return null;
  }
} 