/// -----
/// free_internship_approval_event.dart
///
/// 免实习申请审批BLoC事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 免实习申请审批BLoC事件基类
abstract class FreeInternshipApprovalEvent extends Equatable {
  const FreeInternshipApprovalEvent();

  @override
  List<Object?> get props => [];
}

/// 审批免实习申请事件
class ApproveFreeInternshipEvent extends FreeInternshipApprovalEvent {
  const ApproveFreeInternshipEvent({
    required this.id,
    required this.reviewOpinion,
    required this.isApproved,
  });

  /// 免实习申请ID
  final int id;
  
  /// 审批意见
  final String reviewOpinion;
  
  /// 是否通过（true=通过，false=驳回）
  final bool isApproved;

  @override
  List<Object> get props => [id, reviewOpinion, isApproved];
}
