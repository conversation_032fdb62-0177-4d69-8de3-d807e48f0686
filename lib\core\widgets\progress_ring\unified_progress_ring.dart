/// -----
/// unified_progress_ring.dart
/// 
/// 统一的圆环进度组件，组合圆环绘制和内容显示功能
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'base_progress_ring.dart';
import 'progress_ring_content.dart';

/// 统一的圆环进度组件
/// 
/// 组合了圆环绘制和内容显示功能，提供完整的圆环进度展示
class UnifiedProgressRing extends StatelessWidget {
  /// 进度值 (0.0 - 1.0)
  final double progress;
  
  /// 主要文本内容
  final String mainText;
  
  /// 副标题文本
  final String? subtitle;
  
  /// 圆环大小
  final double size;
  
  /// 圆环线宽
  final double strokeWidth;
  
  /// 进度圆环颜色
  final Color progressColor;
  
  /// 背景圆环颜色
  final Color backgroundColor;
  
  /// 主要文本样式
  final TextStyle? mainTextStyle;
  
  /// 副标题文本样式
  final TextStyle? subtitleStyle;
  
  /// 主要文本和副标题之间的间距
  final double textSpacing;
  
  /// 圆环起始角度（弧度）
  final double startAngle;
  
  /// 是否使用圆形端点
  final bool useRoundCap;
  
  /// 是否启用动画
  final bool enableAnimation;
  
  /// 动画持续时间
  final Duration animationDuration;
  
  /// 内容的对齐方式
  final MainAxisAlignment contentAlignment;

  const UnifiedProgressRing({
    Key? key,
    required this.progress,
    required this.mainText,
    this.subtitle,
    this.size = 160,
    this.strokeWidth = 10,
    this.progressColor = const Color(0xFF2979FF),
    this.backgroundColor = const Color(0xFFEEEEEE),
    this.mainTextStyle,
    this.subtitleStyle,
    this.textSpacing = 12,
    this.startAngle = -pi / 2,
    this.useRoundCap = true,
    this.enableAnimation = false,
    this.animationDuration = const Duration(milliseconds: 800),
    this.contentAlignment = MainAxisAlignment.center,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size.w,
      height: size.w,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 圆环进度
          BaseProgressRing(
            progress: progress,
            size: size,
            strokeWidth: strokeWidth,
            progressColor: progressColor,
            backgroundColor: backgroundColor,
            startAngle: startAngle,
            useRoundCap: useRoundCap,
            enableAnimation: enableAnimation,
            animationDuration: animationDuration,
          ),
          
          // 中心内容
          Positioned.fill(
            child: Center(
              child: ProgressRingContent(
                mainText: mainText,
                subtitle: subtitle,
                mainTextStyle: mainTextStyle,
                subtitleStyle: subtitleStyle,
                spacing: textSpacing,
                alignment: contentAlignment,
                enableAnimation: enableAnimation,
                animationDuration: Duration(
                  milliseconds: (animationDuration.inMilliseconds * 0.8).round(),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 预定义的圆环进度样式
class UnifiedProgressRingStyles {
  /// 评分样式（用于成绩评分）
  static UnifiedProgressRing score({
    required double progress,
    required String score,
    required String title,
    double size = 160,
    Color? progressColor,
    bool enableAnimation = false,
  }) {
    return UnifiedProgressRing(
      progress: progress,
      mainText: score,
      subtitle: title,
      size: size,
      strokeWidth: 10,
      progressColor: progressColor ?? const Color(0xFF2979FF),
      backgroundColor: const Color(0xFFEEEEEE),
      mainTextStyle: ProgressRingContentStyles.scoreMainTextStyle,
      subtitleStyle: ProgressRingContentStyles.standardSubtitleStyle,
      enableAnimation: enableAnimation,
    );
  }

  /// 签到样式（用于签到天数）
  static UnifiedProgressRing signIn({
    required double progress,
    required int signedDays,
    required int totalDays,
    String label = '签到天数',
    double size = 100,
    Color? progressColor,
    bool enableAnimation = false,
  }) {
    return UnifiedProgressRing(
      progress: progress,
      mainText: '$signedDays/$totalDays',
      subtitle: label,
      size: size,
      strokeWidth: 12,
      progressColor: progressColor ?? const Color(0xFF2165F6),
      backgroundColor: Colors.grey[200]!,
      mainTextStyle: ProgressRingContentStyles.countMainTextStyle,
      subtitleStyle: ProgressRingContentStyles.smallSubtitleStyle,
      enableAnimation: enableAnimation,
    );
  }

  /// 百分比样式
  static UnifiedProgressRing percentage({
    required double progress,
    required String title,
    double size = 120,
    Color? progressColor,
    bool enableAnimation = false,
  }) {
    final percentage = (progress * 100).round();
    return UnifiedProgressRing(
      progress: progress,
      mainText: '$percentage%',
      subtitle: title,
      size: size,
      strokeWidth: 8,
      progressColor: progressColor ?? AppTheme.primaryColor,
      backgroundColor: const Color(0xFFEEEEEE),
      mainTextStyle: ProgressRingContentStyles.percentageMainTextStyle,
      subtitleStyle: ProgressRingContentStyles.standardSubtitleStyle,
      enableAnimation: enableAnimation,
    );
  }

  /// 简单计数样式
  static UnifiedProgressRing count({
    required double progress,
    required String count,
    required String title,
    double size = 100,
    Color? progressColor,
    bool enableAnimation = false,
  }) {
    return UnifiedProgressRing(
      progress: progress,
      mainText: count,
      subtitle: title,
      size: size,
      strokeWidth: 8,
      progressColor: progressColor ?? AppTheme.primaryColor,
      backgroundColor: const Color(0xFFEEEEEE),
      mainTextStyle: ProgressRingContentStyles.countMainTextStyle,
      subtitleStyle: ProgressRingContentStyles.smallSubtitleStyle,
      enableAnimation: enableAnimation,
    );
  }
}

/// 圆环进度组件配置类
class ProgressRingConfig {
  /// 进度值 (0.0 - 1.0)
  final double progress;
  
  /// 主要文本内容
  final String mainText;
  
  /// 副标题文本
  final String? subtitle;
  
  /// 圆环大小
  final double size;
  
  /// 圆环线宽
  final double strokeWidth;
  
  /// 进度圆环颜色
  final Color progressColor;
  
  /// 背景圆环颜色
  final Color backgroundColor;
  
  /// 主要文本样式
  final TextStyle? mainTextStyle;
  
  /// 副标题文本样式
  final TextStyle? subtitleStyle;
  
  /// 是否启用动画
  final bool enableAnimation;

  const ProgressRingConfig({
    required this.progress,
    required this.mainText,
    this.subtitle,
    this.size = 160,
    this.strokeWidth = 10,
    this.progressColor = const Color(0xFF2979FF),
    this.backgroundColor = const Color(0xFFEEEEEE),
    this.mainTextStyle,
    this.subtitleStyle,
    this.enableAnimation = false,
  });

  /// 创建 UnifiedProgressRing 组件
  UnifiedProgressRing build() {
    return UnifiedProgressRing(
      progress: progress,
      mainText: mainText,
      subtitle: subtitle,
      size: size,
      strokeWidth: strokeWidth,
      progressColor: progressColor,
      backgroundColor: backgroundColor,
      mainTextStyle: mainTextStyle,
      subtitleStyle: subtitleStyle,
      enableAnimation: enableAnimation,
    );
  }
}
