/// -----
/// internship_student_model.dart
///
/// 实习生数据模型，用于存储实习生的基本信息
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/constants/constants.dart';

class InternshipStudentModel {
  final String id;
  final String name;
  final String avatar;
  final String phone;
  final String insuranceDays;
  final String safetyScore;
  final String status;
  final String className;

  InternshipStudentModel({
    required this.id,
    required this.name,
    required this.avatar,
    required this.phone,
    required this.insuranceDays,
    required this.safetyScore,
    required this.status,
    required this.className,
  });

  factory InternshipStudentModel.fromJson(Map<String, dynamic> json) {
    return InternshipStudentModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      avatar: json['avatar'] ?? '',
      phone: json['phone'] ?? '',
      insuranceDays: json['insuranceDays'] ?? '',
      safetyScore: json['safetyScore'] ?? '',
      status: json['status'] ?? '',
      className: json['className'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'avatar': avatar,
      'phone': phone,
      'insuranceDays': insuranceDays,
      'safetyScore': safetyScore,
      'status': status,
      'className': className,
    };
  }

  static List<InternshipStudentModel> getSampleData() {
    return [
      InternshipStudentModel(
        id: '1',
        name: '李成儒',
        avatar: AppConstants.avatar1,
        phone: '13569874562',
        insuranceDays: '3',
        safetyScore: '60',
        status: '实习中',
        className: '建筑工程5班',
      ),
      InternshipStudentModel(
        id: '2',
        name: '李成儒',
        avatar: AppConstants.avatar1,
        phone: '13569874562',
        insuranceDays: '3',
        safetyScore: '60',
        status: '实习中',
        className: '建筑工程5班',
      ),
      InternshipStudentModel(
        id: '3',
        name: '李成儒',
        avatar: AppConstants.avatar1,
        phone: '13569874562',
        insuranceDays: '3',
        safetyScore: '60',
        status: '实习中',
        className: '建筑工程5班',
      ),
      InternshipStudentModel(
        id: '4',
        name: '李成儒',
        avatar: AppConstants.avatar1,
        phone: '13569874562',
        insuranceDays: '3',
        safetyScore: '60',
        status: '实习中',
        className: '建筑工程5班',
      ),
      InternshipStudentModel(
        id: '5',
        name: '李成儒',
        avatar: AppConstants.avatar1,
        phone: '13569874562',
        insuranceDays: '3',
        safetyScore: '60',
        status: '实习中',
        className: '建筑工程5班',
      ),
    ];
  }
}