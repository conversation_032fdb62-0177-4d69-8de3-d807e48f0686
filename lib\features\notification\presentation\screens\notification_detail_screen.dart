/// -----------------------------------------------------------------------------
/// notification_detail_screen.dart
///
/// 消息详情页面，用于展示单条通知消息的详细内容
///
/// 功能：
/// 1. 展示消息标题、发布时间、详细内容和发布单位
/// 2. 提供返回上一页的功能
///
/// 使用方法：
/// ```dart
/// Navigator.push(
///   context,
///   MaterialPageRoute(
///     builder: (context) => NotificationDetailScreen(
///       title: '这是公告标题',
///       date: '2025.02.12 13:00',
///       content: '公告的背景和目的是帮助读者理解公告产生的原因和预期要达到的效果。这是公告内容',
///       publisher: '建筑工程学院',
///     ),
///   ),
/// );
/// ```
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';

/// 消息详情页面
///
/// 展示单条通知消息的详细内容，包括标题、发布时间、详细内容和发布单位
class NotificationDetailScreen extends StatelessWidget {
  /// 消息标题
  final String title;

  /// 发布时间
  final String date;

  /// 消息内容
  final String content;

  /// 发布单位
  final String publisher;

  const NotificationDetailScreen({
    Key? key,
    required this.title,
    required this.date,
    required this.content,
    required this.publisher,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(title: "消息详情"),
      body: _buildBody(),
    );
  }

  /// 构建页面主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            _buildTitle(),

            const SizedBox(height: 8),

            // 日期
            _buildDate(),

            const SizedBox(height: 24),

            // 分隔线
            const Divider(height: 1, thickness: 1, color: Color(0xFFF5F5F5)),

            const SizedBox(height: 24),

            // 内容
            _buildContent(),

            const SizedBox(height: 40),

            // 发布单位
            _buildPublisher(),
          ],
        ),
      ),
    );
  }

  /// 构建标题
  Widget _buildTitle() {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        color: AppTheme.textPrimaryColor,
        height: 1.4,
      ),
    );
  }

  /// 构建日期
  Widget _buildDate() {
    return Text(
      date,
      style: TextStyle(
        fontSize: 14,
        color: Colors.grey[500],
      ),
    );
  }

  /// 构建内容
  Widget _buildContent() {
    return Text(
      content,
      style: const TextStyle(
        fontSize: 16,
        color: AppTheme.textSecondaryColor,
        height: 1.6,
        letterSpacing: 0.5,
      ),
    );
  }

  /// 构建发布单位
  Widget _buildPublisher() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            publisher,
            style: TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w500,
              color: AppTheme.primaryColor,
            ),
          ),
        ),
      ],
    );
  }
}
