/// -----
/// internship_application_repository.dart
///
/// 实习申请仓库抽象接口
/// 定义实习申请相关的数据操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../data/models/internship_application_request_model.dart';

/// 实习申请仓库抽象接口
///
/// 定义实习申请相关的数据操作方法
/// 使用Either类型处理成功和失败的情况
abstract class InternshipApplicationRepository {
  /// 提交实习申请
  ///
  /// 向服务器提交学生的实习申请信息
  ///
  /// 参数:
  ///   - [request]: 实习申请请求数据
  ///
  /// 返回:
  ///   Either<Failure, bool> 成功返回true，失败返回Failure
  Future<Either<Failure, bool>> submitApplication(
    InternshipApplicationRequestModel request,
  );
}
