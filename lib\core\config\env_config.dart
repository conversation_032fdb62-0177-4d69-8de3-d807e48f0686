import 'package:flutter/foundation.dart';
import 'package:flutter_demo/core/constants/constants.dart';

/// 环境类型枚举
enum EnvType {
  /// 开发环境
  dev,

  /// 测试环境
  test,

  /// 生产环境
  prod,
}

/// 环境配置类
///
/// 用于管理不同环境的配置信息，如API基础URL、超时设置等
class EnvConfig {
  /// 当前环境类型
  final EnvType envType;

  /// API基础URL
  final String baseUrl;

  /// API超时时间（毫秒）
  final int timeoutMillis;

  /// 是否启用日志
  final bool enableLogging;

  /// 是否启用缓存
  final bool enableCache;

  /// 缓存有效期
  final Duration cacheMaxAge;

  /// 构造函数
  const EnvConfig({
    required this.envType,
    required this.baseUrl,
    this.timeoutMillis = 15000,
    this.enableLogging = true,
    this.enableCache = false,
    this.cacheMaxAge = const Duration(hours: 1),
  });

  /// 开发环境配置
  static const EnvConfig development = EnvConfig(
    envType: EnvType.dev,
    baseUrl: AppConstants.baseUrl,
    timeoutMillis: 30000, // 开发环境超时时间更长
    enableLogging: true,
    enableCache: true,
    cacheMaxAge: Duration(minutes: 5), // 开发环境缓存时间短
  );

  /// 测试环境配置
  static const EnvConfig test = EnvConfig(
    envType: EnvType.test,
    baseUrl: AppConstants.baseUrl,
    timeoutMillis: 15000,
    enableLogging: true,
    enableCache: true,
    cacheMaxAge: Duration(hours: 1),
  );

  /// 生产环境配置
  static const EnvConfig production = EnvConfig(
    envType: EnvType.prod,
    baseUrl: AppConstants.baseUrl,
    timeoutMillis: 10000,
    enableLogging: false, // 生产环境禁用详细日志
    enableCache: true,
    cacheMaxAge: Duration(hours: 24), // 生产环境缓存时间长
  );

  /// 获取当前环境名称
  String get name => envType.toString().split('.').last;

  /// 是否为开发环境
  bool get isDevelopment => envType == EnvType.dev;

  /// 是否为测试环境
  bool get isTest => envType == EnvType.test;

  /// 是否为生产环境
  bool get isProduction => envType == EnvType.prod;

  @override
  String toString() {
    return 'EnvConfig(envType: $envType, baseUrl: $baseUrl, enableLogging: $enableLogging)';
  }
}

/// 环境管理类
///
/// 单例模式，用于全局管理当前环境配置
class Env {
  /// 私有构造函数
  Env._();

  /// 单例实例
  static final Env _instance = Env._();

  /// 获取单例实例
  static Env get instance => _instance;

  /// 当前环境配置
  late EnvConfig _config;

  /// 获取当前环境配置
  EnvConfig get config => _config;

  /// 初始化环境配置
  ///
  /// 应在应用启动时调用
  void initialize({EnvType environment = EnvType.dev}) {
    switch (environment) {
      case EnvType.dev:
        _config = EnvConfig.development;
        break;
      case EnvType.test:
        _config = EnvConfig.test;
        break;
      case EnvType.prod:
        _config = EnvConfig.production;
        break;
      default:
        // 默认使用开发环境
        _config = EnvConfig.development;
    }

    debugPrint('应用环境初始化为: ${_config.name}');
    debugPrint('API基础URL: ${_config.baseUrl}');
  }
}
