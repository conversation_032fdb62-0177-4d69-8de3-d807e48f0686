/// -----
/// get_student_file_approval_list_usecase.dart
///
/// 获取学生文件审批列表用例
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/utils/logger.dart';
import '../entities/student_file_approval.dart';
import '../repositories/student_file_approval_repository.dart';

/// 获取学生文件审批列表用例
class GetStudentFileApprovalListUseCase implements UseCase<List<StudentFileApproval>, GetStudentFileApprovalListParams> {
  final StudentFileApprovalRepository _repository;

  GetStudentFileApprovalListUseCase(this._repository);

  @override
  Future<Either<Failure, List<StudentFileApproval>>> call(GetStudentFileApprovalListParams params) async {
    Logger.info('GetStudentFileApprovalListUseCase', '执行获取学生文件审批列表用例，planId: ${params.planId}, type: ${params.type}');

    final result = await _repository.getStudentFileApprovalList(params.planId, params.type);

    return result.fold(
      (failure) {
        Logger.error('GetStudentFileApprovalListUseCase', '获取学生文件审批列表失败: ${failure.message}');
        return Left(failure);
      },
      (studentFileApprovalList) {
        Logger.info('GetStudentFileApprovalListUseCase', '成功获取${studentFileApprovalList.length}个学生文件审批项');
        return Right(studentFileApprovalList);
      },
    );
  }
}

/// 获取学生文件审批列表参数
class GetStudentFileApprovalListParams extends Equatable {
  final String planId;
  final int type;

  const GetStudentFileApprovalListParams({
    required this.planId,
    required this.type,
  });

  @override
  List<Object?> get props => [planId, type];

  @override
  String toString() {
    return 'GetStudentFileApprovalListParams{planId: $planId, type: $type}';
  }
}
