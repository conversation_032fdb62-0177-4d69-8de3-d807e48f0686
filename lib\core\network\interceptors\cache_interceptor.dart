import 'package:dio/dio.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'dart:convert';

/// 简单的缓存拦截器，使用SharedPreferences存储数据
/// 适用于不经常变化的数据，如配置信息、基础数据等
class CacheInterceptor extends Interceptor {
  /// 日志标签
  static const String _tag = 'CacheInterceptor';

  /// 缓存最大保存时间
  final Duration maxAge;

  /// 可以缓存的API路径列表
  final List<String> cacheablePaths;

  /// 是否强制使用缓存，即使缓存已过期
  final bool forceCache;

  /// 构造函数
  ///
  /// [maxAge] - 缓存最大保存时间
  /// [cacheablePaths] - 可以缓存的API路径列表，例如 ['/config', '/categories']
  /// [forceCache] - 是否强制使用缓存，即使缓存已过期
  CacheInterceptor({
    this.maxAge = const Duration(hours: 1),
    this.cacheablePaths = const [],
    this.forceCache = false,
  });

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // 只对GET请求和指定路径进行缓存
    if (options.method == 'GET' && _shouldCache(options.path)) {
      Logger.debug(_tag, '检查缓存: ${options.path}');

      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _generateCacheKey(options);
      final cacheJson = prefs.getString(cacheKey);

      if (cacheJson != null) {
        final cacheData = jsonDecode(cacheJson);
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(cacheData['timestamp']);
        final now = DateTime.now();

        // 检查缓存是否有效
        if (forceCache || now.difference(cacheTime) <= maxAge) {
          Logger.info(_tag, '使用缓存数据: $cacheKey');

          // 返回缓存数据
          final response = Response(
            data: cacheData['data'],
            statusCode: 200,
            requestOptions: options,
          );

          return handler.resolve(response);
        } else {
          Logger.debug(_tag, '缓存已过期: $cacheKey');
        }
      } else {
        Logger.debug(_tag, '缓存不存在: $cacheKey');
      }
    }

    // 不使用缓存，继续请求
    super.onRequest(options, handler);
  }

  @override
  Future<void> onResponse(Response response, ResponseInterceptorHandler handler) async {
    // 只缓存GET请求和指定路径的成功响应
    if (response.requestOptions.method == 'GET' &&
        _shouldCache(response.requestOptions.path) &&
        response.statusCode == 200) {

      Logger.debug(_tag, '准备缓存响应: ${response.requestOptions.path}');

      try {
        final prefs = await SharedPreferences.getInstance();
        final cacheKey = _generateCacheKey(response.requestOptions);
        final cacheData = {
          'data': response.data,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        };

        await prefs.setString(cacheKey, jsonEncode(cacheData));
        Logger.info(_tag, '数据已缓存: $cacheKey');
      } on Exception catch (e, s) {
        Logger.error(_tag, '缓存数据失败', exception: e, stackTrace: s);
      }
    }

    super.onResponse(response, handler);
  }

  // 检查是否应该缓存该路径
  bool _shouldCache(String path) {
    return cacheablePaths.any((cachePath) => path.contains(cachePath));
  }

  // 生成缓存键
  String _generateCacheKey(RequestOptions options) {
    final queryParams = options.queryParameters.isEmpty
        ? ''
        : '?${_mapToQueryString(options.queryParameters)}';
    return '${AppConstants.cacheKeyPrefix}${options.path}$queryParams';
  }

  // 将Map转换为查询字符串
  String _mapToQueryString(Map<String, dynamic> params) {
    return params.entries
        .map((e) => '${e.key}=${e.value}')
        .join('&');
  }
}