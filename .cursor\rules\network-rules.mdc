---
description: 
globs: 
alwaysApply: false
---
# 网络规范

## API 集成指南
- 基础 URL: `http://**************:8088/userservice/userservice/`
- 响应格式: `{data, resultCode, resultMsg}`
- 所有请求使用 POST 方法
- 请求头信息：
  ```json
  {
    "Content-type": "application/json",
    "Accept": "application/json",
    "token": "Bearer {token}"
  }
  ```

## 网络客户端配置
- 使用 `Dio` 作为 HTTP 客户端
- 在 `lib/core/network/api/dio_client.dart` 中实现 `DioClient` 类
- 配置基础 URL、超时设置、默认头信息等
- 注册所有拦截器

## 拦截器实现
### 日志拦截器
- 记录请求和响应的详细信息
- 在开发环境中启用，生产环境禁用

### 认证拦截器
- 处理 token 的添加、刷新和过期逻辑
- 在请求头中添加 token
- 处理 token 过期的情况，自动刷新 token 并重试请求

### 错误处理拦截器
- 统一处理网络错误和服务器错误
- 转换错误为应用内的异常类型

### 缓存拦截器
- 实现请求缓存策略
- 在无网络或指定情况下使用缓存数据

## 网络状态监控
- 实现 `NetworkInfo` 接口检查网络连接状态
- 在请求前检查网络状态，避免无网络时发送请求

## 错误处理
- 定义清晰的异常类型：
  - `ServerException`: 服务器错误
  - `CacheException`: 缓存错误
  - `NetworkException`: 网络错误
- 定义对应的失败类型：
  - `ServerFailure`: 服务器失败
  - `CacheFailure`: 缓存失败
  - `NetworkFailure`: 网络失败

## 性能优化
- 实现请求缓存
- 实现请求合并
- 实现请求取消
- 实现请求重试
- 实现请求超时处理
- 实现请求优先级
- 实现请求队列 
- 实现请求取消
- 实现请求重试
- 实现请求超时处理
- 实现请求优先级
- 实现请求队列 