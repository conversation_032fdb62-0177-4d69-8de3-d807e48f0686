/// -----
/// free_internship_exempt_list_bloc.dart
///
/// 免实习申请列表BLoC
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/logger.dart';
import '../../../domain/usecases/get_free_internship_exempt_list_usecase.dart';
import 'free_internship_exempt_list_event.dart';
import 'free_internship_exempt_list_state.dart';

/// 免实习申请列表BLoC
/// 
/// 管理免实习申请列表的状态和业务逻辑
class FreeInternshipExemptListBloc extends Bloc<FreeInternshipExemptListEvent, FreeInternshipExemptListState> {
  final GetFreeInternshipExemptListUseCase _getFreeInternshipExemptListUseCase;

  FreeInternshipExemptListBloc({
    required GetFreeInternshipExemptListUseCase getFreeInternshipExemptListUseCase,
  })  : _getFreeInternshipExemptListUseCase = getFreeInternshipExemptListUseCase,
        super(FreeInternshipExemptListInitial()) {
    on<LoadFreeInternshipExemptListEvent>(_onLoadFreeInternshipExemptList);
    on<RefreshFreeInternshipExemptListEvent>(_onRefreshFreeInternshipExemptList);
  }

  /// 处理加载免实习申请列表事件
  Future<void> _onLoadFreeInternshipExemptList(
    LoadFreeInternshipExemptListEvent event,
    Emitter<FreeInternshipExemptListState> emit,
  ) async {
    try {
      Logger.info('FreeInternshipExemptListBloc', '开始加载免实习申请列表 - planId: ${event.planId}, type: ${event.type}');
      
      emit(FreeInternshipExemptListLoading());

      final result = await _getFreeInternshipExemptListUseCase(
        GetFreeInternshipExemptListParams(
          planId: event.planId,
          type: event.type,
        ),
      );

      result.fold(
        (failure) {
          Logger.error('FreeInternshipExemptListBloc', '加载免实习申请列表失败: ${failure.message}');
          emit(FreeInternshipExemptListError(message: failure.message));
        },
        (exempts) {
          Logger.info('FreeInternshipExemptListBloc', '成功加载${exempts.length}个免实习申请');
          emit(FreeInternshipExemptListLoaded(
            exempts: exempts,
            planId: event.planId,
            type: event.type,
          ));
        },
      );
    } catch (e) {
      Logger.error('FreeInternshipExemptListBloc', '加载免实习申请列表时发生未知错误: $e');
      emit(const FreeInternshipExemptListError(message: '加载免实习申请列表失败，请稍后重试'));
    }
  }

  /// 处理刷新免实习申请列表事件
  Future<void> _onRefreshFreeInternshipExemptList(
    RefreshFreeInternshipExemptListEvent event,
    Emitter<FreeInternshipExemptListState> emit,
  ) async {
    try {
      Logger.info('FreeInternshipExemptListBloc', '开始刷新免实习申请列表 - planId: ${event.planId}, type: ${event.type}');
      
      // 如果当前状态是已加载状态，则显示刷新状态
      if (state is FreeInternshipExemptListLoaded) {
        final currentState = state as FreeInternshipExemptListLoaded;
        emit(FreeInternshipExemptListRefreshing(
          exempts: currentState.exempts,
          planId: currentState.planId,
          type: currentState.type,
        ));
      } else {
        emit(FreeInternshipExemptListLoading());
      }

      final result = await _getFreeInternshipExemptListUseCase(
        GetFreeInternshipExemptListParams(
          planId: event.planId,
          type: event.type,
        ),
      );

      result.fold(
        (failure) {
          Logger.error('FreeInternshipExemptListBloc', '刷新免实习申请列表失败: ${failure.message}');
          emit(FreeInternshipExemptListError(message: failure.message));
        },
        (exempts) {
          Logger.info('FreeInternshipExemptListBloc', '成功刷新${exempts.length}个免实习申请');
          emit(FreeInternshipExemptListLoaded(
            exempts: exempts,
            planId: event.planId,
            type: event.type,
          ));
        },
      );
    } catch (e) {
      Logger.error('FreeInternshipExemptListBloc', '刷新免实习申请列表时发生未知错误: $e');
      emit(const FreeInternshipExemptListError(message: '刷新免实习申请列表失败，请稍后重试'));
    }
  }
}
