/// -----
/// role_info_state.dart
/// 
/// 角色信息页面状态类
///
/// <AUTHOR>
/// @date 2025-05-20
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 角色信息状态基类
abstract class RoleInfoState extends Equatable {
  const RoleInfoState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class RoleInfoInitial extends RoleInfoState {
  const RoleInfoInitial();
}

/// 加载中状态
class RoleInfoLoading extends RoleInfoState {
  const RoleInfoLoading();
}

/// 认证成功状态
class RoleInfoAuthenticateSuccess extends RoleInfoState {
  const RoleInfoAuthenticateSuccess();
}

/// 认证失败状态
class RoleInfoAuthenticateFailure extends RoleInfoState {
  final String message;

  const RoleInfoAuthenticateFailure(this.message);

  @override
  List<Object?> get props => [message];
}
