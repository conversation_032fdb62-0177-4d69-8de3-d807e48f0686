/// -----------------------------------------------------------------------------
/// home_screen.dart
///
/// 主页面组件，作为应用的主要入口，管理底部导航和不同功能模块的展示
///
/// 包含以下功能：
/// 1. 底部导航栏，用于切换不同的主要功能页面
/// 2. 首页内容展示，包括轮播图、公告栏和功能模块
/// 3. 数据页面展示
/// 4. 个人中心页面展示
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/data/presentation/screens/data_screen.dart';
import 'package:flutter_demo/features/home/<USER>/widgets/home_page_content.dart';
import 'package:flutter_demo/features/profile/presentation/screens/profile_screen.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 主页面组件
///
/// 作为应用的主要入口，管理底部导航和不同功能模块的展示
class HomeScreen extends StatefulWidget {

  /// 构造函数
  const HomeScreen({
    Key? key,
    this.userType,
    required this.navigationShell,
  }) : super(key: key);

  /// 用户类型
  final String? userType;

  /// 导航壳，用于控制StatefulShellRoute的导航
  final StatefulNavigationShell navigationShell;

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

/// 主页面状态管理类
///
/// 管理底部导航栏状态和页面切换
class _HomeScreenState extends State<HomeScreen> {
  /// 用户类型
  String? _userType;

  /// 页面是否已初始化
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
    _loadUserType();
  }

  /// 加载用户类型
  ///
  /// 从本地存储中获取用户类型
  Future<void> _loadUserType() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _userType = prefs.getString(AppConstants.userTypeKey);
      setState(() {
        _initialized = true;
      });
    } catch (e) {
      debugPrint('加载用户类型失败: $e');
      // 如果加载失败，使用默认初始化
      _userType = widget.userType;
      setState(() {
        _initialized = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 如果用户类型尚未加载完成，显示加载指示器
    if (!_initialized) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // 使用StatefulNavigationShell控制的导航
    return Scaffold(
      // 使用navigationShell.currentChild显示当前页面
      body: widget.navigationShell,
      bottomNavigationBar: BottomNavigationBar(
        // 当前选中的索引需要根据用户类型进行调整
        currentIndex: _getUserAdjustedIndex(widget.navigationShell.currentIndex),
        // 点击底部导航栏项时使用navigationShell切换分支
        onTap: (index) {
          // 根据用户类型和当前点击的索引确定要切换到哪个分支
          if (_userType == '2') {
            // 教师用户：有三个选项卡（首页、数据、我的）
            widget.navigationShell.goBranch(index);
          } else {
            // 非教师用户：只有两个选项卡（首页、我的）
            if (index == 0) {
              // 首页
              widget.navigationShell.goBranch(0);
            } else if (index == 1) {
              // 我的（对于非教师用户，“我的”对应分支索引为2）
              widget.navigationShell.goBranch(2);
            }
          }
        },
        items: _buildNavItems(),
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        backgroundColor: Colors.white,
        elevation: 8,
      ),
    );
  }

  /// 根据用户类型构建底部导航栏项目
  /// 根据用户类型调整导航索引
  ///
  /// 对于非教师用户，如果当前分支索引是2（我的），则返回1
  int _getUserAdjustedIndex(int navigationShellIndex) {
    // 如果是教师用户，直接返回原始索引
    if (_userType == '2') {
      return navigationShellIndex;
    }

    // 如果是非教师用户
    if (navigationShellIndex == 0) {
      // 首页索引保持不变
      return 0;
    } else if (navigationShellIndex == 2) {
      // 如果是“我的”页面（分支索引为2），则在导航栏中显示为索引为1
      return 1;
    }

    // 默认情况返回0
    return 0;
  }

  List<BottomNavigationBarItem> _buildNavItems() {
    // 基础导航项：首页和我的
    final List<BottomNavigationBarItem> items = [
      BottomNavigationBarItem(
        icon: Image.asset(
          'assets/images/home_unselect_icon.png',
          width: 24,
          height: 24,
        ),
        activeIcon: Image.asset(
          'assets/images/home_select_icon.png',
          width: 24,
          height: 24,
        ),
        label: '首页',
      ),
    ];

    // 只有教师角色(userType=2)才显示数据选项
    if (_userType == '2') {
      items.add(
        BottomNavigationBarItem(
          icon: Image.asset(
            'assets/images/data_unselect_icon.png',
            width: 24,
            height: 24,
          ),
          activeIcon: Image.asset(
            'assets/images/data_select_icon.png',
            width: 24,
            height: 24,
          ),
          label: '数据',
        ),
      );
    }

    // 添加个人中心选项
    items.add(
      BottomNavigationBarItem(
        icon: Image.asset(
          'assets/images/me_unselect_icon.png',
          width: 24,
          height: 24,
        ),
        activeIcon: Image.asset(
          'assets/images/me_select_icon.png',
          width: 24,
          height: 24,
        ),
        label: '我的',
      ),
    );

    return items;
  }
}
