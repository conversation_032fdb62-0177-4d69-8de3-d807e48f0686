import 'package:equatable/equatable.dart';

/// 注册事件基类
///
/// 所有注册相关的事件都应继承此类
abstract class RegisterEvent extends Equatable {
  const RegisterEvent();

  @override
  List<Object?> get props => [];
}

/// 注册请求事件
///
/// 当用户尝试注册时触发
class RegisterRequestEvent extends RegisterEvent {
  final String phone;
  final String code;
  final String password;

  const RegisterRequestEvent({
    required this.phone,
    required this.code,
    required this.password,
  });

  @override
  List<Object?> get props => [phone, code, password];
}

/// 发送验证码事件
///
/// 当用户请求发送验证码时触发
class SendVerificationCodeEvent extends RegisterEvent {
  final String phone;

  const SendVerificationCodeEvent({
    required this.phone,
  });

  @override
  List<Object?> get props => [phone];
}
