/// -----
/// get_file_approval_list_usecase.dart
///
/// 获取文件审批列表用例
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../../../../core/utils/logger.dart';
import '../entities/file_approval_list_item.dart';
import '../repositories/file_approval_repository.dart';

/// 获取文件审批列表用例
class GetFileApprovalListUseCase implements UseCase<List<FileApprovalListItem>, GetFileApprovalListParams> {
  final FileApprovalRepository _repository;

  GetFileApprovalListUseCase(this._repository);

  @override
  Future<Either<Failure, List<FileApprovalListItem>>> call(GetFileApprovalListParams params) async {
    Logger.info('GetFileApprovalListUseCase', '执行获取文件审批列表用例，planId: ${params.planId}');

    final result = await _repository.getFileApprovalList(params.planId);

    return result.fold(
      (failure) {
        Logger.error('GetFileApprovalListUseCase', '获取文件审批列表失败: ${failure.message}');
        return Left(failure);
      },
      (fileApprovalList) {
        Logger.info('GetFileApprovalListUseCase', '成功获取${fileApprovalList.length}个文件审批项');
        return Right(fileApprovalList);
      },
    );
  }
}

/// 获取文件审批列表参数
class GetFileApprovalListParams extends Equatable {
  final String planId;

  const GetFileApprovalListParams({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];

  @override
  String toString() {
    return 'GetFileApprovalListParams{planId: $planId}';
  }
}
