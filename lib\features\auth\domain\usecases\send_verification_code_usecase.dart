/// -----
/// send_verification_code_usecase.dart
///
/// 发送验证码用例，处理发送验证码的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/usecases/usecase.dart';
import '../repositories/auth_repository.dart';

class SendVerificationCodeUseCase implements UseCase<String, SendVerificationCodeParams> {
  final AuthRepository repository;

  SendVerificationCodeUseCase(this.repository);

  @override
  Future<Either<Failure, String>> call(SendVerificationCodeParams params) async {
    try {
      final response = await repository.sendVerificationCode(
        params.phone,
        deviceId: params.deviceId,
      );
      return Right(response);
    } catch (e) {
      return Left(ServerFailure(e.toString()));
    }
  }
}

/// 验证码参数
///
/// 包含发送验证码所需的参数
class SendVerificationCodeParams extends Equatable {
  final String phone;
  final String deviceId;

  const SendVerificationCodeParams({
    required this.phone,
    required this.deviceId,
  });

  @override
  List<Object?> get props => [phone, deviceId];
}
