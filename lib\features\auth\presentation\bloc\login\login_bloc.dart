import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/usecases/get_current_user_usecase.dart';
import '../../../domain/usecases/login_usecase.dart';
import '../../../domain/usecases/logout_usecase.dart';
import 'login_event.dart';
import 'login_state.dart';

/// 登录 BLoC
///
/// 处理登录相关的业务逻辑
class LoginBloc extends Bloc<LoginEvent, LoginState> {
  final LoginUseCase _loginUseCase;
  final LogoutUseCase _logoutUseCase;
  final GetCurrentUserUseCase _getCurrentUserUseCase;

  LoginBloc({
    required LoginUseCase loginUseCase,
    required LogoutUseCase logoutUseCase,
    required GetCurrentUserUseCase getCurrentUserUseCase,
  })  : _loginUseCase = loginUseCase,
        _logoutUseCase = logoutUseCase,
        _getCurrentUserUseCase = getCurrentUserUseCase,
        super(LoginInitialState()) {
    on<LoginRequestEvent>(_onLoginRequest);
    on<LogoutEvent>(_onLogout);
    on<CheckLoginStatusEvent>(_onCheckLoginStatus);
    on<GetCurrentUserEvent>(_onGetCurrentUser);
  }

  /// 处理登录请求事件
  Future<void> _onLoginRequest(
    LoginRequestEvent event,
    Emitter<LoginState> emit,
  ) async {
    emit(LoginLoadingState());

    final result = await _loginUseCase(
      LoginParams(
        phone: event.phone,
        password: event.password,
      ),
    );

    result.fold(
      (failure) => emit(LoginFailureState(failure.message)),
      (user) => emit(LoginSuccessState(user)),
    );
  }

  /// 处理退出登录事件
  Future<void> _onLogout(
    LogoutEvent event,
    Emitter<LoginState> emit,
  ) async {
    emit(LoginLoadingState());

    final result = await _logoutUseCase();

    result.fold(
      (failure) => emit(LogoutFailureState(failure.message)),
      (_) => emit(LogoutSuccessState()),
    );
  }

  /// 处理检查登录状态事件
  Future<void> _onCheckLoginStatus(
    CheckLoginStatusEvent event,
    Emitter<LoginState> emit,
  ) async {
    emit(LoginLoadingState());

    final result = await _getCurrentUserUseCase();

    result.fold(
      (failure) => emit(NotLoggedInState()),
      (user) {
        if (user != null) {
          emit(LoggedInState(user));
        } else {
          emit(NotLoggedInState());
        }
      },
    );
  }

  /// 处理获取当前用户事件
  Future<void> _onGetCurrentUser(
    GetCurrentUserEvent event,
    Emitter<LoginState> emit,
  ) async {
    emit(LoginLoadingState());

    final result = await _getCurrentUserUseCase();

    result.fold(
      (failure) => emit(LoginFailureState(failure.message)),
      (user) {
        if (user != null) {
          emit(LoggedInState(user));
        } else {
          emit(NotLoggedInState());
        }
      },
    );
  }
}
