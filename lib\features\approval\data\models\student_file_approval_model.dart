/// -----
/// student_file_approval_model.dart
///
/// 学生文件审批数据模型
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/entities/student_file_approval.dart';

/// 学生文件审批数据模型
class StudentFileApprovalModel extends StudentFileApproval {
  const StudentFileApprovalModel({
    required super.studentId,
    required super.studentName,
    required super.fileList,
  });

  /// 从 JSON 创建模型
  factory StudentFileApprovalModel.fromJson(Map<String, dynamic> json) {
    final fileListJson = json['fileList'] as List<dynamic>? ?? [];
    final fileList = fileListJson
        .map((fileJson) => FileApprovalDetailModel.fromJson(fileJson as Map<String, dynamic>))
        .toList();

    return StudentFileApprovalModel(
      studentId: json['studentId']?.toString() ?? '',
      studentName: json['studentName']?.toString() ?? '',
      fileList: fileList,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'studentId': studentId,
      'studentName': studentName,
      'fileList': fileList.map((file) => (file as FileApprovalDetailModel).toJson()).toList(),
    };
  }

  /// 转换为实体
  StudentFileApproval toEntity() {
    return StudentFileApproval(
      studentId: studentId,
      studentName: studentName,
      fileList: fileList.map((file) => (file as FileApprovalDetailModel).toEntity()).toList(),
    );
  }

  /// 从实体创建模型
  factory StudentFileApprovalModel.fromEntity(StudentFileApproval entity) {
    return StudentFileApprovalModel(
      studentId: entity.studentId,
      studentName: entity.studentName,
      fileList: entity.fileList.map((file) => FileApprovalDetailModel.fromEntity(file)).toList(),
    );
  }

  @override
  String toString() {
    return 'StudentFileApprovalModel{studentId: $studentId, studentName: $studentName, fileList: ${fileList.length} files}';
  }
}

/// 文件审批详情数据模型
class FileApprovalDetailModel extends FileApprovalDetail {
  const FileApprovalDetailModel({
    required super.id,
    required super.planId,
    required super.studentId,
    required super.fileName,
    required super.fileType,
    required super.fileCode,
    required super.fileUrl,
    // 文件状态（0:未上传, 1:已上传, 2:已审核, 3:已驳回）
    required super.fileStatus,
    super.approveName,
    super.teacherId,
    super.approveRole,
    super.approveRoleName,
    super.remark,
    required super.createPerson,
    required super.createTime,
    super.updatePerson,
    super.updateTime,
  });

  /// 从 JSON 创建模型
  factory FileApprovalDetailModel.fromJson(Map<String, dynamic> json) {
    return FileApprovalDetailModel(
      id: json['id']?.toString() ?? '',
      planId: json['planId']?.toString() ?? '',
      studentId: json['studentId']?.toString() ?? '',
      fileName: json['fileName']?.toString() ?? '',
      fileType: json['fileType']?.toString() ?? '',
      fileCode: json['fileCode'] ?? 0,
      fileUrl: json['fileUrl']?.toString() ?? '',
      fileStatus: json['fileStatus'] ?? 1,
      approveName: json['approveName']?.toString(),
      teacherId: json['teacherId']?.toString(),
      approveRole: json['approveRole']?.toString(),
      approveRoleName: json['approveRoleName']?.toString(),
      remark: json['remark']?.toString(),
      createPerson: json['createPerson']?.toString() ?? '',
      createTime: json['createTime'] ?? 0,
      updatePerson: json['updatePerson']?.toString(),
      updateTime: json['updateTime'],
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'planId': planId,
      'studentId': studentId,
      'fileName': fileName,
      'fileType': fileType,
      'fileCode': fileCode,
      'fileUrl': fileUrl,
      'fileStatus': fileStatus,
      'approveName': approveName,
      'teacherId': teacherId,
      'approveRole': approveRole,
      'approveRoleName': approveRoleName,
      'remark': remark,
      'createPerson': createPerson,
      'createTime': createTime,
      'updatePerson': updatePerson,
      'updateTime': updateTime,
    };
  }

  /// 转换为实体
  FileApprovalDetail toEntity() {
    return FileApprovalDetail(
      id: id,
      planId: planId,
      studentId: studentId,
      fileName: fileName,
      fileType: fileType,
      fileCode: fileCode,
      fileUrl: fileUrl,
      fileStatus: fileStatus,
      approveName: approveName,
      teacherId: teacherId,
      approveRole: approveRole,
      approveRoleName: approveRoleName,
      remark: remark,
      createPerson: createPerson,
      createTime: createTime,
      updatePerson: updatePerson,
      updateTime: updateTime,
    );
  }

  /// 从实体创建模型
  factory FileApprovalDetailModel.fromEntity(FileApprovalDetail entity) {
    return FileApprovalDetailModel(
      id: entity.id,
      planId: entity.planId,
      studentId: entity.studentId,
      fileName: entity.fileName,
      fileType: entity.fileType,
      fileCode: entity.fileCode,
      fileUrl: entity.fileUrl,
      fileStatus: entity.fileStatus,
      approveName: entity.approveName,
      teacherId: entity.teacherId,
      approveRole: entity.approveRole,
      approveRoleName: entity.approveRoleName,
      remark: entity.remark,
      createPerson: entity.createPerson,
      createTime: entity.createTime,
      updatePerson: entity.updatePerson,
      updateTime: entity.updateTime,
    );
  }

  @override
  String toString() {
    return 'FileApprovalDetailModel{id: $id, fileName: $fileName, fileType: $fileType, fileStatus: $fileStatus}';
  }
}
