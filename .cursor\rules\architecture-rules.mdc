# 架构规范

## 整体架构
- 采用 Clean Architecture 架构
- 分为表现层(Presentation)、领域层(Domain)和数据层(Data)
- 使用 BLoC 进行状态管理
- 使用 go_router 进行路由管理
- 使用 GetIt 进行依赖注入

## 文件结构
```
lib/
  ├── core/                # 核心工具和通用组件
  │   ├── common/          # 通用组件
  │   ├── config/          # 配置文件
  │   ├── constants/       # 常量定义
  │   ├── error/           # 错误处理
  │   ├── network/         # 网络相关
  │   ├── router/          # 路由配置
  │   ├── storage/         # 本地存储
  │   ├── theme/           # 主题配置
  │   ├── utils/           # 工具类
  │   └── widgets/         # 共享UI组件
  └── features/            # 功能模块
      ├── auth/            # 认证功能
      ├── home/            # 首页功能
      ├── internship/      # 实习功能
      └── ...
```

## 依赖注入规范
- 使用 GetIt 进行依赖注入
- 在应用启动时注册所有依赖
- 按模块组织依赖注册
- 遵循依赖倒置原则

## 状态管理规范
- 使用 BLoC 模式管理状态
- 将 UI 与业务逻辑分离
- 通过事件驱动状态变化
- 保持状态不可变

## 路由管理规范
- 使用 go_router 进行声明式路由
- 集中管理所有路由定义
- 统一页面转场动画
- 使用路由抽象层简化导航

---
 
- 统一页面转场动画
- 使用路由抽象层简化导航

---
 