# 文件审批预览页面修改说明

## 修改概述

根据用户需求，对文件审批预览页面进行了以下主要修改：

1. **修改数据传递方式**：从传递单独字段改为传递完整的 `FileApprovalDetail` 对象
2. **根据 fileStatus 显示审批印章**：支持已审核(2)和已驳回(3)状态的印章显示
3. **实现网络PDF加载**：从网络URL加载PDF文件而不是本地assets
4. **实现真实的审批接口调用**：调用 `/v1/internship/teacher/file/approve` 接口

## 详细修改内容

### 1. 修改跳转逻辑 (file_approval_list_screen.dart)

**修改前：**
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => FileApprovalPreviewScreen(
      fileId: fileDetail.id,
      fileName: fileDetail.fileName,
      studentId: fileDetail.studentId,
      studentName: studentName,
      approvalStatus: approvalStatus,
      filePreviewUrl: fileDetail.fileUrl,
    ),
  ),
);
```

**修改后：**
```dart
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => FileApprovalPreviewScreen(
      fileDetail: fileDetail,
      studentName: studentName,
    ),
  ),
);
```

### 2. 修改页面构造函数 (file_approval_preview_screen.dart)

**修改前：**
```dart
class FileApprovalPreviewScreen extends StatefulWidget {
  final String fileId;
  final String fileName;
  final String studentId;
  final String studentName;
  final ApprovalStatus approvalStatus;
  final String? filePreviewUrl;
  // ...
}
```

**修改后：**
```dart
class FileApprovalPreviewScreen extends StatefulWidget {
  const FileApprovalPreviewScreen({
    required this.fileDetail,
    required this.studentName,
    super.key,
  });

  final FileApprovalDetail fileDetail;
  final String studentName;
  // ...
}
```

### 3. 添加状态映射方法

根据 `fileStatus` 数值映射到 `ApprovalStatus` 枚举：

```dart
ApprovalStatus get _approvalStatus {
  switch (widget.fileDetail.fileStatus) {
    case 0: // 未上传
    case 1: // 已上传
      return ApprovalStatus.pending;
    case 2: // 已审核
      return ApprovalStatus.approved;
    case 3: // 已驳回
      return ApprovalStatus.rejected;
    default:
      return ApprovalStatus.pending;
  }
}
```

### 4. 实现网络PDF加载

**修改前：**
```dart
// 从assets加载PDF文件
final ByteData data = await rootBundle.load('assets/pdf/sample.pdf');
final Uint8List bytes = data.buffer.asUint8List();
```

**修改后：**
```dart
final fileUrl = widget.fileDetail.fileUrl;
if (fileUrl.isEmpty) {
  setState(() {
    _pdfError = 'PDF文件URL为空';
    _isLoadingPdf = false;
  });
  return;
}

// 从网络下载PDF文件
final dioClient = GetIt.instance<DioClient>();
final response = await dioClient.dioInstance.get(
  fileUrl,
  options: Options(responseType: ResponseType.bytes),
);
```

### 5. 实现审批接口调用

#### 通过审批：
```dart
final dioClient = GetIt.instance<DioClient>();
await dioClient.post(
  'internshipservice/v1/internship/teacher/file/approve',
  data: {
    'fileStatus': 2, // 2表示通过
    'id': widget.fileDetail.id,
    'remark': '审批通过',
  },
);
```

#### 驳回审批：
```dart
final dioClient = GetIt.instance<DioClient>();
await dioClient.post(
  'internshipservice/v1/internship/teacher/file/approve',
  data: {
    'fileStatus': 3, // 3表示驳回
    'id': widget.fileDetail.id,
    'remark': reason, // 用户输入的驳回原因
  },
);
```

## API 接口说明

### 审批接口
- **URL**: `/v1/internship/teacher/file/approve`
- **方法**: POST
- **请求参数**:
  ```json
  {
    "fileStatus": 2, // 2:通过, 3:驳回
    "id": "文件ID",
    "remark": "备注说明（如驳回原因等）"
  }
  ```
- **响应数据**:
  ```json
  {
    "data": 0,
    "resultCode": "string",
    "resultMsg": "string"
  }
  ```

## 文件状态说明

- `0`: 未上传
- `1`: 已上传
- `2`: 已审核（通过）
- `3`: 已驳回

## 审批印章显示逻辑

- **fileStatus = 2**: 显示通过印章 (`assets/images/passed_icon.png`)
- **fileStatus = 3**: 显示驳回印章 (`assets/images/rejected_icon.png`)
- **其他状态**: 不显示印章

## 注意事项

1. **网络权限**: 确保应用有网络访问权限以下载PDF文件
2. **错误处理**: 添加了完整的错误处理和用户提示
3. **状态管理**: 使用 `mounted` 检查避免在组件销毁后使用 `BuildContext`
4. **文件清理**: 自动清理下载的临时PDF文件
5. **用户体验**: 添加了加载状态和重试功能

## 路由修复

由于修改了 `FileApprovalPreviewScreen` 的构造函数，也需要修复 `app_router.dart` 中的路由定义：

```dart
// 创建临时的 FileApprovalDetail 对象
final fileDetail = FileApprovalDetail(
  id: fileId,
  planId: '', // 从路由参数无法获取，使用空字符串
  studentId: studentId,
  fileName: fileName,
  fileType: fileType,
  fileCode: fileCode,
  fileUrl: fileUrl,
  fileStatus: fileStatus,
  createPerson: '',
  createTime: DateTime.now().millisecondsSinceEpoch,
);

return FileApprovalPreviewScreen(
  fileDetail: fileDetail,
  studentName: studentName,
);
```

## 编译状态

✅ **编译成功** - 项目已成功编译，没有错误

## 测试建议

1. 测试不同 fileStatus 的文件显示效果
2. 测试网络PDF文件加载功能
3. 测试审批通过和驳回功能
4. 测试网络异常情况的处理
5. 测试驳回原因输入功能

## 完成状态

- ✅ 修改数据传递方式
- ✅ 实现根据 fileStatus 显示审批印章
- ✅ 实现网络PDF加载
- ✅ 实现审批接口调用
- ✅ 修复路由配置
- ✅ 代码质量优化
- ✅ 编译测试通过
