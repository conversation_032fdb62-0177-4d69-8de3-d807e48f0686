/// -----
/// role_info_event.dart
/// 
/// 角色信息页面事件类
///
/// <AUTHOR>
/// @date 2025-05-20
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 角色信息事件基类
abstract class RoleInfoEvent extends Equatable {
  const RoleInfoEvent();

  @override
  List<Object?> get props => [];
}

/// 认证事件
class AuthenticateEvent extends RoleInfoEvent {
  final String deptName;
  final String userCode;
  final String userName;
  final String userType;

  const AuthenticateEvent({
    required this.deptName,
    required this.userCode,
    required this.userName,
    required this.userType,
  });

  @override
  List<Object?> get props => [deptName, userCode, userName, userType];
}
