/// -----
/// pdf_navigator.dart
/// 
/// PDF阅读器导航工具类，提供PDF阅读相关的导航方法
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_demo/core/router/route_constants.dart';

/// PDF阅读器导航工具类
class PdfNavigator {
  /// 打开PDF阅读器
  /// 
  /// [context] 上下文
  /// [pdfPath] PDF文件路径或URL
  /// [title] 页面标题，默认为"文件阅读"
  static void openPdfViewer(
    BuildContext context, {
    required String pdfPath,
    String title = '文件阅读',
  }) {
    if (pdfPath.isEmpty) {
      // 如果路径为空，显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('PDF文件路径不能为空'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // 构建查询参数
    final queryParams = {
      'path': pdfPath,
      'title': title,
    };

    // 构建完整的路由路径
    final uri = Uri(
      path: AppRoutes.fileApprovalPreviewPdf,
      queryParameters: queryParams,
    );

    // 导航到PDF阅读器页面
    context.push(uri.toString());
  }

  /// 根据文件ID打开对应的PDF文件
  /// 
  /// [context] 上下文
  /// [fileId] 文件ID
  /// [fileName] 文件名称，用作页面标题
  static void openPdfByFileId(
    BuildContext context, {
    required String fileId,
    String? fileName,
  }) {
    // 根据文件ID获取PDF路径（这里使用模拟数据，实际应该从API获取）
    final pdfPath = _getPdfPathByFileId(fileId);
    final title = fileName ?? _getFileNameByFileId(fileId);

    openPdfViewer(
      context,
      pdfPath: pdfPath,
      title: title,
    );
  }

  /// 根据文件ID获取PDF路径（模拟数据）
  ///
  /// 实际项目中应该从API或数据库获取真实的文件路径
  static String _getPdfPathByFileId(String fileId) {
    // 模拟数据映射 - 使用本地示例PDF文件进行测试
    final Map<String, String> filePathMap = {
      'tripartite_agreement': 'assets/pdf/sample.pdf',
      'parent_notice': 'assets/pdf/sample.pdf',
      'insurance': 'assets/pdf/sample.pdf',
      'self_agreement': 'assets/pdf/sample.pdf',
    };

    // 实际项目中的网络文件示例（注释掉，供参考）
    // final Map<String, String> filePathMap = {
    //   'tripartite_agreement': 'https://example.com/files/tripartite_agreement.pdf',
    //   'parent_notice': 'https://example.com/files/parent_notice.pdf',
    //   'insurance': 'https://example.com/files/insurance.pdf',
    //   'self_agreement': 'https://example.com/files/self_agreement.pdf',
    // };

    return filePathMap[fileId] ?? 'assets/pdf/sample.pdf';
  }

  /// 根据文件ID获取文件名称（模拟数据）
  /// 
  /// 实际项目中应该从API或数据库获取真实的文件名称
  static String _getFileNameByFileId(String fileId) {
    // 模拟数据映射
    final Map<String, String> fileNameMap = {
      'tripartite_agreement': '三方协议',
      'parent_notice': '告家长通知书',
      'insurance': '实习保险单',
      'self_agreement': '自助协议申请表',
    };

    return fileNameMap[fileId] ?? '文件阅读';
  }
}
