/// -----
/// reset_password_event.dart
///
/// 重置密码事件类，定义重置密码流程中的各种事件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 重置密码事件
abstract class ResetPasswordEvent extends Equatable {
  const ResetPasswordEvent();

  @override
  List<Object?> get props => [];
}

/// 发送验证码事件
class SendVerificationCodeEvent extends ResetPasswordEvent {

  const SendVerificationCodeEvent({
    required this.phone,
    required this.deviceId,
  });
  final String phone;
  final String deviceId;

  @override
  List<Object?> get props => [phone, deviceId];
}

/// 重置密码事件
class ResetPasswordSubmittedEvent extends ResetPasswordEvent {

  const ResetPasswordSubmittedEvent({
    required this.phone,
    required this.code,
    required this.newPassword,
    required this.deviceId,
  });
  final String phone;
  final String code;
  final String newPassword;
  final String deviceId;

  @override
  List<Object?> get props => [phone, code, newPassword, deviceId];
}
