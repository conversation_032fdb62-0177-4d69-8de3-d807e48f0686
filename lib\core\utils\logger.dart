import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_demo/core/config/env_config.dart';
import 'package:flutter_demo/core/constants/constants.dart';

/// 日志级别枚举
enum LogLevel {
  /// 详细日志，仅在开发环境使用
  verbose(0, 'VERBOSE'),
  
  /// 调试日志，用于开发和测试环境
  debug(1, 'DEBUG'),
  
  /// 信息日志，记录重要事件
  info(2, 'INFO'),
  
  /// 警告日志，记录可能的问题
  warning(3, 'WARNING'),
  
  /// 错误日志，记录严重问题
  error(4, 'ERROR'),
  
  /// 致命错误，记录导致应用崩溃的问题
  fatal(5, 'FATAL');
  
  final int value;
  final String name;
  
  const LogLevel(this.value, this.name);
}

/// 日志工具类
///
/// 提供统一的日志记录接口，支持不同级别的日志、标签、异常和堆栈跟踪
class Logger {
  /// 单例实例
  static final Logger _instance = Logger._internal();
  
  /// 工厂构造函数
  factory Logger() => _instance;
  
  /// 私有构造函数
  Logger._internal();
  
  /// 当前环境配置
  EnvConfig? _envConfig;
  
  /// 当前日志级别
  LogLevel _currentLevel = LogLevel.verbose;
  
  /// 是否启用文件日志
  bool _enableFileLogging = false;
  
  /// 日志文件路径
  String? _logFilePath;
  
  /// 日志文件最大大小（字节）
  int _maxFileSize = 5 * 1024 * 1024; // 5MB
  
  /// 日期格式化器
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss.SSS');
  
  /// 初始化日志工具
  ///
  /// [envConfig] - 环境配置
  /// [minLevel] - 最小日志级别
  /// [enableFileLogging] - 是否启用文件日志
  /// [maxFileSize] - 日志文件最大大小（字节）
  Future<void> init({
    required EnvConfig envConfig,
    LogLevel? minLevel,
    bool enableFileLogging = false,
    int? maxFileSize,
  }) async {
    _envConfig = envConfig;
    
    // 根据环境设置日志级别
    if (minLevel != null) {
      _currentLevel = minLevel;
    } else {
      _currentLevel = _getDefaultLogLevel(envConfig.envType);
    }
    
    // 设置文件日志
    _enableFileLogging = enableFileLogging;
    if (maxFileSize != null) {
      _maxFileSize = maxFileSize;
    }
    
    if (_enableFileLogging) {
      await _initLogFile();
    }
    
    info('Logger', '日志系统初始化完成，当前级别: ${_currentLevel.name}，环境: ${envConfig.envType.name}');
  }
  
  /// 获取默认日志级别
  LogLevel _getDefaultLogLevel(EnvType envType) {
    switch (envType) {
      case EnvType.dev:
        return LogLevel.verbose;
      case EnvType.test:
        return LogLevel.debug;
      case EnvType.prod:
        return LogLevel.warning;
    }
  }
  
  /// 初始化日志文件
  Future<void> _initLogFile() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/logs');
      
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }
      
      final now = DateTime.now();
      final fileName = 'app_log_${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}.log';
      _logFilePath = '${logDir.path}/$fileName';
      
      // 检查日志文件大小
      final file = File(_logFilePath!);
      if (await file.exists()) {
        final fileSize = await file.length();
        if (fileSize > _maxFileSize) {
          await _rotateLogFile(file);
        }
      }
      
      debug('Logger', '日志文件初始化完成: $_logFilePath');
    } catch (e, s) {
      debugPrint('初始化日志文件失败: $e');
      debugPrint('$s');
    }
  }
  
  /// 轮转日志文件
  Future<void> _rotateLogFile(File file) async {
    try {
      final now = DateTime.now();
      final backupFileName = 'app_log_${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour}${now.minute}${now.second}.log';
      final directory = file.parent;
      final backupPath = '${directory.path}/$backupFileName';
      
      await file.rename(backupPath);
      debug('Logger', '日志文件已轮转: $backupPath');
    } catch (e) {
      debugPrint('轮转日志文件失败: $e');
    }
  }
  
  /// 记录详细日志
  static void verbose(String tag, String message, {Object? exception, StackTrace? stackTrace}) {
    _instance._log(LogLevel.verbose, tag, message, exception, stackTrace);
  }
  
  /// 记录调试日志
  static void debug(String tag, String message, {Object? exception, StackTrace? stackTrace}) {
    _instance._log(LogLevel.debug, tag, message, exception, stackTrace);
  }
  
  /// 记录信息日志
  static void info(String tag, String message, {Object? exception, StackTrace? stackTrace}) {
    _instance._log(LogLevel.info, tag, message, exception, stackTrace);
  }
  
  /// 记录警告日志
  static void warning(String tag, String message, {Object? exception, StackTrace? stackTrace}) {
    _instance._log(LogLevel.warning, tag, message, exception, stackTrace);
  }
  
  /// 记录错误日志
  static void error(String tag, String message, {Object? exception, StackTrace? stackTrace}) {
    _instance._log(LogLevel.error, tag, message, exception, stackTrace);
  }
  
  /// 记录致命错误日志
  static void fatal(String tag, String message, {Object? exception, StackTrace? stackTrace}) {
    _instance._log(LogLevel.fatal, tag, message, exception, stackTrace);
  }
  
  /// 内部日志记录方法
  void _log(LogLevel level, String tag, String message, Object? exception, StackTrace? stackTrace) {
    // 检查日志级别
    if (level.value < _currentLevel.value) {
      return;
    }
    
    final now = DateTime.now();
    final timeString = _dateFormat.format(now);
    
    // 构建日志消息
    String logMessage = '[${level.name}] [$timeString] [$tag] $message';
    
    // 添加异常信息
    if (exception != null) {
      logMessage += ' - Exception: $exception';
    }
    
    // 添加堆栈跟踪
    if (stackTrace != null && (level == LogLevel.error || level == LogLevel.fatal)) {
      logMessage += '\n$stackTrace';
    }
    
    // 控制台输出
    _printToConsole(level, logMessage);
    
    // 文件输出
    if (_enableFileLogging) {
      _writeToFile(logMessage);
    }
  }
  
  /// 输出到控制台
  void _printToConsole(LogLevel level, String message) {
    switch (level) {
      case LogLevel.verbose:
      case LogLevel.debug:
        debugPrint(message);
        break;
      case LogLevel.info:
        debugPrint('\x1B[32m$message\x1B[0m'); // 绿色
        break;
      case LogLevel.warning:
        debugPrint('\x1B[33m$message\x1B[0m'); // 黄色
        break;
      case LogLevel.error:
      case LogLevel.fatal:
        debugPrint('\x1B[31m$message\x1B[0m'); // 红色
        break;
    }
  }
  
  /// 写入到文件
  Future<void> _writeToFile(String message) async {
    if (_logFilePath == null) return;
    
    try {
      final file = File(_logFilePath!);
      await file.writeAsString('$message\n', mode: FileMode.append);
      
      // 检查文件大小
      final fileSize = await file.length();
      if (fileSize > _maxFileSize) {
        await _rotateLogFile(file);
        await _initLogFile();
      }
    } catch (e) {
      debugPrint('写入日志文件失败: $e');
    }
  }
  
  /// 脱敏处理
  static String maskSensitiveInfo(String text, {int visibleChars = 3, int visibleEndChars = 0}) {
    if (text.isEmpty) return '';
    if (text.length <= visibleChars) return '*' * text.length;
    
    final start = text.substring(0, visibleChars);
    final middle = '*' * (text.length - visibleChars - visibleEndChars);
    final end = visibleEndChars > 0 ? text.substring(text.length - visibleEndChars) : '';
    
    return start + middle + end;
  }
  
  /// 格式化JSON
  static String prettyJson(dynamic json) {
    try {
      if (json is Map || json is List) {
        const encoder = JsonEncoder.withIndent('  ');
        return encoder.convert(json);
      } else if (json is String) {
        try {
          const encoder = JsonEncoder.withIndent('  ');
          return encoder.convert(jsonDecode(json));
        } on FormatException {
          return json;
        }
      }
      return json.toString();
    } catch (e) {
      return json.toString();
    }
  }
}
