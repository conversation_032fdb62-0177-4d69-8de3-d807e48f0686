/// -----
/// file_approval_list_state.dart
///
/// 文件审批列表状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../../domain/entities/file_approval_list_item.dart';

/// 文件审批列表状态基类
abstract class FileApprovalListState extends Equatable {
  const FileApprovalListState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class FileApprovalListInitial extends FileApprovalListState {
  const FileApprovalListInitial();
}

/// 加载中状态
class FileApprovalListLoading extends FileApprovalListState {
  const FileApprovalListLoading();
}

/// 加载成功状态
class FileApprovalListLoaded extends FileApprovalListState {
  final List<FileApprovalListItem> fileApprovalList;

  const FileApprovalListLoaded({
    required this.fileApprovalList,
  });

  @override
  List<Object?> get props => [fileApprovalList];

  @override
  String toString() {
    return 'FileApprovalListLoaded{fileApprovalList: ${fileApprovalList.length} items}';
  }
}

/// 加载失败状态
class FileApprovalListError extends FileApprovalListState {
  final String message;

  const FileApprovalListError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];

  @override
  String toString() {
    return 'FileApprovalListError{message: $message}';
  }
}
