/// -----
/// insurance_info.dart
/// 
/// 实习保险信息实体类
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 实习保险信息实体类
///
/// 包含保险名称、保险单号、购买方、种类、起止时间等信息
class InsuranceInfo extends Equatable {
  /// 保险名称
  final String name;
  
  /// 保险单号
  final String policyNumber;
  
  /// 保险购买方
  final String purchaser;
  
  /// 保险种类
  final String type;
  
  /// 保险起止时间
  final DateTimeRange insurancePeriod;
  
  /// 实习起止时间
  final DateTimeRange internshipPeriod;
  
  /// 未覆盖天数
  final int uncoveredDays;
  
  /// 保险合同文件路径
  final String? contractFilePath;
  
  /// 保险合同文件名
  final String? contractFileName;

  const InsuranceInfo({
    required this.name,
    required this.policyNumber,
    required this.purchaser,
    required this.type,
    required this.insurancePeriod,
    required this.internshipPeriod,
    required this.uncoveredDays,
    this.contractFilePath,
    this.contractFileName,
  });

  @override
  List<Object?> get props => [
    name,
    policyNumber,
    purchaser,
    type,
    insurancePeriod,
    internshipPeriod,
    uncoveredDays,
    contractFilePath,
    contractFileName,
  ];
}

/// 日期时间范围
///
/// 包含开始和结束日期
class DateTimeRange extends Equatable {
  /// 开始日期
  final DateTime start;
  
  /// 结束日期
  final DateTime end;

  const DateTimeRange({
    required this.start,
    required this.end,
  });

  @override
  List<Object> get props => [start, end];
}
