/// -----
/// weekly_report.dart
/// 
/// 周报模型，定义周报特有的属性
/// 
/// <AUTHOR>
/// @date 2025-06-17
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';

class WeeklyReport extends BaseReport {
  final DateTime startDate;
  final DateTime endDate;
  final String weekSummary;
  final String achievements;
  final String problems;
  final String nextWeekPlan;

  const WeeklyReport({
    required super.id,
    required super.userId,
    required super.userName,
    required super.courseName,
    super.title,
    required super.createdAt,
    required super.status,
    super.isLate = false,
    super.rating,
    super.teacherComment,
    super.teacherName,
    super.commentTime,
    required this.startDate,
    required this.endDate,
    required this.weekSummary,
    required this.achievements,
    required this.problems,
    required this.nextWeekPlan,
    super.infoTitle,
    super.contentTitle
  });

  @override
  String get content => weekSummary;

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'courseName': courseName,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'status': status.toString(),
      'isLate': isLate,
      'rating': rating,
      'teacherComment': teacherComment,
      'teacherName': teacherName,
      'commentTime': commentTime?.toIso8601String(),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'weekSummary': weekSummary,
      'achievements': achievements,
      'problems': problems,
      'nextWeekPlan': nextWeekPlan,
      'infoTitle': infoTitle,
      'contentTitle': contentTitle,
    };
  }

  factory WeeklyReport.fromJson(Map<String, dynamic> json) => WeeklyReport(
    id: json['id'],
    status: ReportStatus.values.firstWhere(
          (e) => e.toString() == json['status'],
    ),
    userId: json['userId'],
    userName: json['userName'],
    createdAt: DateTime.parse(json['createdAt']),
    startDate: DateTime.parse(json['startDate']),
    endDate: DateTime.parse(json['endDate']),
    weekSummary: json['weekSummary'],
    achievements: json['achievements'],
    problems: json['problems'],
    nextWeekPlan: json['nextWeekPlan'],
    courseName: json['courseName'],
    title: json['title'],
    isLate: json['isLate'],
    rating: json['rating'],
    teacherComment: json['teacherComment'],
    teacherName: json['teacherName'],
    commentTime: json['commentTime'] != null
        ? DateTime.parse(json['commentTime'])
        : null,
    infoTitle: json['infoTitle'],
    contentTitle: json['contentTitle'],
  );
}