/// -----
/// student_header.dart
///
/// 学生信息头部组件，用于显示学生头像和姓名
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StudentHeader extends StatelessWidget {
  final String name;
  final String? avatar;

  const StudentHeader({
    Key? key,
    required this.name,
    this.avatar,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // 头像
          CircleAvatar(
            radius: 28.r,
            backgroundColor: Colors.grey[200],
            backgroundImage: avatar != null && avatar!.isNotEmpty
                ? NetworkImage(avatar!) as ImageProvider
                : const AssetImage('assets/images/default_avatar.png'),
            child: avatar == null || avatar!.isEmpty
                ? const Icon(
                    Icons.person,
                    size: 22, // 相应调整图标大小
                    color: Colors.grey,
                  )
                : null,
          ),

          const SizedBox(width: 12),

          // 姓名
          Text(
            name,
            style: TextStyle(
              fontSize: 28.sp,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
