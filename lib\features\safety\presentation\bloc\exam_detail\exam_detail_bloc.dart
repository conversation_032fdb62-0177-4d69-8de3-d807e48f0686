/// -----
/// exam_detail_bloc.dart
/// 
/// 考试详情BLoC
///
/// <AUTHOR>
/// @date 2025-06-19
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/safety/domain/usecases/get_exam_detail_usecase.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/exam_detail/exam_detail_event.dart';
import 'package:flutter_demo/features/safety/presentation/bloc/exam_detail/exam_detail_state.dart';

/// 考试详情BLoC
///
/// 处理考试详情相关的业务逻辑
class ExamDetailBloc extends Bloc<ExamDetailEvent, ExamDetailState> {
  /// 获取考试详情用例
  final GetExamDetailUseCase getExamDetailUseCase;

  /// 构造函数
  ExamDetailBloc({
    required this.getExamDetailUseCase,
  }) : super(ExamDetailInitial()) {
    on<LoadExamDetailEvent>(_onLoadExamDetail);
    on<RefreshExamDetailEvent>(_onRefreshExamDetail);
  }

  /// 处理加载考试详情事件
  Future<void> _onLoadExamDetail(
    LoadExamDetailEvent event,
    Emitter<ExamDetailState> emit,
  ) async {
    emit(ExamDetailLoading());
    try {
      final examDetail = await getExamDetailUseCase(event.recordId);
      emit(ExamDetailLoaded(examDetail: examDetail));
    } catch (e) {
      emit(ExamDetailError(message: '加载考试详情失败: ${e.toString()}'));
    }
  }

  /// 处理刷新考试详情事件
  Future<void> _onRefreshExamDetail(
    RefreshExamDetailEvent event,
    Emitter<ExamDetailState> emit,
  ) async {
    // 如果当前状态是已加载状态，则显示刷新中状态
    if (state is ExamDetailLoaded) {
      final currentState = state as ExamDetailLoaded;
      emit(ExamDetailRefreshing(previousData: currentState.examDetail));
    } else {
      emit(ExamDetailLoading());
    }

    try {
      final examDetail = await getExamDetailUseCase(event.recordId);
      emit(ExamDetailLoaded(examDetail: examDetail));
    } catch (e) {
      // 如果之前有数据，恢复到之前的状态
      if (state is ExamDetailRefreshing) {
        final currentState = state as ExamDetailRefreshing;
        emit(ExamDetailLoaded(examDetail: currentState.previousData));
      } else {
        emit(ExamDetailError(message: '刷新考试详情失败: ${e.toString()}'));
      }
    }
  }
}
