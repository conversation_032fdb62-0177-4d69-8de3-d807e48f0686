/// -----
/// student_file_approval_list_bloc.dart
///
/// 学生文件审批列表 BLoC
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/logger.dart';
import '../../../domain/usecases/get_student_file_approval_list_usecase.dart';
import 'student_file_approval_list_event.dart';
import 'student_file_approval_list_state.dart';

/// 学生文件审批列表 BLoC
class StudentFileApprovalListBloc extends Bloc<StudentFileApprovalListEvent, StudentFileApprovalListState> {
  final GetStudentFileApprovalListUseCase _getStudentFileApprovalListUseCase;

  StudentFileApprovalListBloc({
    required GetStudentFileApprovalListUseCase getStudentFileApprovalListUseCase,
  })  : _getStudentFileApprovalListUseCase = getStudentFileApprovalListUseCase,
        super(const StudentFileApprovalListInitial()) {
    on<LoadStudentFileApprovalListEvent>(_onLoadStudentFileApprovalList);
    on<RefreshStudentFileApprovalListEvent>(_onRefreshStudentFileApprovalList);
  }

  /// 处理加载学生文件审批列表事件
  Future<void> _onLoadStudentFileApprovalList(
    LoadStudentFileApprovalListEvent event,
    Emitter<StudentFileApprovalListState> emit,
  ) async {
    Logger.info('StudentFileApprovalListBloc', '开始加载学生文件审批列表，planId: ${event.planId}, type: ${event.type}');
    
    emit(const StudentFileApprovalListLoading());

    final result = await _getStudentFileApprovalListUseCase(
      GetStudentFileApprovalListParams(planId: event.planId, type: event.type),
    );

    result.fold(
      (failure) {
        Logger.error('StudentFileApprovalListBloc', '加载学生文件审批列表失败: ${failure.message}');
        emit(StudentFileApprovalListError(message: failure.message));
      },
      (studentFileApprovalList) {
        Logger.info('StudentFileApprovalListBloc', '成功加载${studentFileApprovalList.length}个学生文件审批项');
        emit(StudentFileApprovalListLoaded(studentFileApprovalList: studentFileApprovalList));
      },
    );
  }

  /// 处理刷新学生文件审批列表事件
  Future<void> _onRefreshStudentFileApprovalList(
    RefreshStudentFileApprovalListEvent event,
    Emitter<StudentFileApprovalListState> emit,
  ) async {
    Logger.info('StudentFileApprovalListBloc', '开始刷新学生文件审批列表，planId: ${event.planId}, type: ${event.type}');

    // 刷新时不显示加载状态，保持当前状态
    final result = await _getStudentFileApprovalListUseCase(
      GetStudentFileApprovalListParams(planId: event.planId, type: event.type),
    );

    result.fold(
      (failure) {
        Logger.error('StudentFileApprovalListBloc', '刷新学生文件审批列表失败: ${failure.message}');
        emit(StudentFileApprovalListError(message: failure.message));
      },
      (studentFileApprovalList) {
        Logger.info('StudentFileApprovalListBloc', '成功刷新${studentFileApprovalList.length}个学生文件审批项');
        emit(StudentFileApprovalListLoaded(studentFileApprovalList: studentFileApprovalList));
      },
    );
  }
}
