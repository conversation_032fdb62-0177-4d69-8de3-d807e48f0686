/// -----
/// student_file_approval_repository_impl.dart
///
/// 学生文件审批仓库实现
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/error/failures/auth_failure.dart';
import '../../../../core/error/exceptions/auth_exception.dart';
import '../../../../core/error/exceptions/server_exception.dart';
import '../../../../core/error/exceptions/network_exception.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/student_file_approval.dart';
import '../../domain/repositories/student_file_approval_repository.dart';
import '../datasources/remote/student_file_approval_remote_data_source.dart';

/// 学生文件审批仓库实现
class StudentFileApprovalRepositoryImpl implements StudentFileApprovalRepository {
  final StudentFileApprovalRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  StudentFileApprovalRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<StudentFileApproval>>> getStudentFileApprovalList(
    String planId,
    int type,
  ) async {
    Logger.info('StudentFileApprovalRepository', '开始获取学生文件审批列表，planId: $planId, type: $type');

    if (await _networkInfo.isConnected) {
      try {
        final studentFileApprovalModels = await _remoteDataSource.getStudentFileApprovalList(planId, type);
        final studentFileApprovalList = studentFileApprovalModels.map((model) => model.toEntity()).toList();
        Logger.info('StudentFileApprovalRepository', '成功获取学生文件审批列表');
        return Right(studentFileApprovalList);
      } on UnauthorizedException catch (e) {
        Logger.error('StudentFileApprovalRepository', '认证异常: ${e.message}');
        return Left(UnauthorizedFailure(e.message));
      } on ForbiddenException catch (e) {
        Logger.error('StudentFileApprovalRepository', '权限异常: ${e.message}');
        return Left(ForbiddenFailure(e.message));
      } on ServerException catch (e) {
        Logger.error('StudentFileApprovalRepository', '服务器异常: ${e.message}');
        return Left(ServerFailure(e.message));
      } on NetworkException catch (e) {
        Logger.error('StudentFileApprovalRepository', '网络异常: ${e.message}');
        return Left(NetworkFailure(e.message));
      } catch (e) {
        Logger.error('StudentFileApprovalRepository', '未知异常: $e');
        return Left(ServerFailure('获取学生文件审批列表失败: $e'));
      }
    } else {
      Logger.warning('StudentFileApprovalRepository', '网络未连接');
      return const Left(NetworkFailure('网络未连接，请检查网络设置'));
    }
  }
}
