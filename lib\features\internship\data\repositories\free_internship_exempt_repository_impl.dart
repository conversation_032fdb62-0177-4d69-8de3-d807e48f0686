/// -----
/// free_internship_exempt_repository_impl.dart
///
/// 免实习申请仓库实现
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../../../../core/error/failures/auth_failure.dart';
import '../../../../core/error/exceptions/auth_exception.dart';
import '../../../../core/error/exceptions/server_exception.dart';
import '../../../../core/error/exceptions/network_exception.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/utils/logger.dart';
import '../../domain/entities/free_internship_exempt.dart';
import '../../domain/entities/free_internship_approval_request.dart';
import '../../domain/repositories/free_internship_exempt_repository.dart';
import '../datasources/remote/free_internship_exempt_remote_data_source.dart';
import '../models/free_internship_approval_request_model.dart';

/// 免实习申请仓库实现
///
/// 实现免实习申请数据访问的具体逻辑，包括网络检查和错误处理
class FreeInternshipExemptRepositoryImpl implements FreeInternshipExemptRepository {
  final FreeInternshipExemptRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;

  FreeInternshipExemptRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
  );

  @override
  Future<Either<Failure, List<FreeInternshipExempt>>> getFreeInternshipExemptList({
    required int planId,
    required int type,
  }) async {
    try {
      Logger.info('FreeInternshipExemptRepository', '开始获取免实习申请列表 - planId: $planId, type: $type');

      // 检查网络连接
      if (!await _networkInfo.isConnected) {
        Logger.warning('FreeInternshipExemptRepository', '网络连接不可用');
        return const Left(NetworkFailure('网络连接不可用，请检查您的网络设置'));
      }

      // 从远程数据源获取数据
      final exemptModels = await _remoteDataSource.getFreeInternshipExemptList(
        planId: planId,
        type: type,
      );

      // 转换为实体类
      final exempts = exemptModels.map((model) => model.toEntity()).toList();

      Logger.info('FreeInternshipExemptRepository', '成功获取${exempts.length}个免实习申请');
      return Right(exempts);
    } on UnauthorizedException catch (e) {
      Logger.error('FreeInternshipExemptRepository', '认证异常: ${e.message}');
      return Left(UnauthorizedFailure(e.message));
    } on ForbiddenException catch (e) {
      Logger.error('FreeInternshipExemptRepository', '权限异常: ${e.message}');
      return Left(ForbiddenFailure(e.message));
    } on ServerException catch (e) {
      Logger.error('FreeInternshipExemptRepository', '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error('FreeInternshipExemptRepository', '网络异常: ${e.message}');
      return Left(NetworkFailure(e.message));
    } on Exception catch (e) {
      Logger.error('FreeInternshipExemptRepository', '未知异常: $e');
      return Left(ServerFailure('获取免实习申请列表失败: $e'));
    }
  }

  @override
  Future<Either<Failure, void>> approveFreeInternshipExempt(FreeInternshipApprovalRequest request) async {
    try {
      Logger.info('FreeInternshipExemptRepository', '开始审批免实习申请 - ID: ${request.id}, 状态: ${request.status}');

      // 检查网络连接
      if (!await _networkInfo.isConnected) {
        Logger.warning('FreeInternshipExemptRepository', '网络连接不可用');
        return const Left(NetworkFailure('网络连接不可用，请检查您的网络设置'));
      }

      // 转换为数据模型
      final requestModel = FreeInternshipApprovalRequestModel.fromEntity(request);

      // 调用远程数据源
      await _remoteDataSource.approveFreeInternshipExempt(requestModel);

      Logger.info('FreeInternshipExemptRepository', '审批免实习申请成功');
      return const Right(null);
    } on UnauthorizedException catch (e) {
      Logger.error('FreeInternshipExemptRepository', '认证异常: ${e.message}');
      return Left(UnauthorizedFailure(e.message));
    } on ForbiddenException catch (e) {
      Logger.error('FreeInternshipExemptRepository', '权限异常: ${e.message}');
      return Left(ForbiddenFailure(e.message));
    } on ServerException catch (e) {
      Logger.error('FreeInternshipExemptRepository', '服务器异常: ${e.message}');
      return Left(ServerFailure(e.message));
    } on NetworkException catch (e) {
      Logger.error('FreeInternshipExemptRepository', '网络异常: ${e.message}');
      return Left(NetworkFailure(e.message));
    } on Exception catch (e) {
      Logger.error('FreeInternshipExemptRepository', '未知异常: $e');
      return Left(ServerFailure('审批免实习申请失败: $e'));
    }
  }
}
