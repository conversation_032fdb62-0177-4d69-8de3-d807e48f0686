import 'package:equatable/equatable.dart';
import '../../../domain/entities/user.dart';

/// 登录状态基类
///
/// 所有登录相关的状态都应继承此类
abstract class LoginState extends Equatable {
  const LoginState();

  @override
  List<Object?> get props => [];
}

/// 登录初始状态
///
/// 表示登录流程的初始状态
class LoginInitialState extends LoginState {}

/// 登录加载状态
///
/// 表示正在进行登录操作
class LoginLoadingState extends LoginState {}

/// 登录成功状态
///
/// 表示登录操作成功
class LoginSuccessState extends LoginState {
  final User user;

  const LoginSuccessState(this.user);

  @override
  List<Object?> get props => [user];
}

/// 登录失败状态
///
/// 表示登录操作失败
class LoginFailureState extends LoginState {
  final String message;

  const LoginFailureState(this.message);

  @override
  List<Object?> get props => [message];
}

/// 已登录状态
///
/// 表示用户已经登录
class LoggedInState extends LoginState {
  final User user;

  const LoggedInState(this.user);

  @override
  List<Object?> get props => [user];
}

/// 未登录状态
///
/// 表示用户未登录
class NotLoggedInState extends LoginState {}

/// 退出登录成功状态
///
/// 表示退出登录操作成功
class LogoutSuccessState extends LoginState {}

/// 退出登录失败状态
///
/// 表示退出登录操作失败
class LogoutFailureState extends LoginState {
  final String message;

  const LogoutFailureState(this.message);

  @override
  List<Object?> get props => [message];
}
