/// -----
/// failure.dart
///
/// 错误基类，用于封装所有错误类型
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 错误基类
///
/// 用于封装所有错误类型
abstract class Failure extends Equatable {
  /// 错误消息
  final String message;

  /// 构造函数
  const Failure({required this.message});

  @override
  List<Object?> get props => [message];
}

/// 服务器错误
class ServerFailure extends Failure {
  const ServerFailure(String message) : super(message: message);
}

/// 缓存错误
class CacheFailure extends Failure {
  const CacheFailure(String message) : super(message: message);
}

/// 网络错误
class NetworkFailure extends Failure {
  const NetworkFailure(String message) : super(message: message);
}
