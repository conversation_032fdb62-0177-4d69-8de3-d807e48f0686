/// -----
/// date_field.dart
///
/// 日期选择字段组件
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/form_field_item.dart';
import 'package:flutter_demo/features/internship/presentation/utils/dialog_utils.dart';

/// 日期选择字段组件
class DateField extends StatelessWidget {
  /// 字段标签
  final String label;
  
  /// 当前值
  final String value;
  
  /// 占位符文本
  final String placeholder;
  
  /// 值变化回调
  final Function(String) onChanged;
  
  /// 是否显示分隔线
  final bool showDivider;
  
  /// 初始日期
  final DateTime? initialDate;
  
  /// 最早日期
  final DateTime? firstDate;
  
  /// 最晚日期
  final DateTime? lastDate;

  const DateField({
    super.key,
    required this.label,
    required this.value,
    this.placeholder = '请选择日期',
    required this.onChanged,
    this.showDivider = true,
    this.initialDate,
    this.firstDate,
    this.lastDate,
  });

  @override
  Widget build(BuildContext context) {
    return FormFieldItem(
      label: label,
      value: value.isEmpty ? placeholder : value,
      type: FormFieldType.date,
      showDivider: showDivider,
      onTap: () => _showDatePicker(context),
    );
  }

  /// 显示日期选择器
  void _showDatePicker(BuildContext context) {
    DialogUtils.showDatePickerDialog(
      context,
      title: label,
      onSelected: onChanged,
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
    );
  }
}
