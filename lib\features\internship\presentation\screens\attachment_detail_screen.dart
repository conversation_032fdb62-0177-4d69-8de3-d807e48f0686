import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';

class AttachmentDetailScreen extends StatelessWidget {
  final String studentId;
  final String attachmentName;
  final String attachmentUrl;

  const AttachmentDetailScreen({
    Key? key,
    required this.studentId,
    required this.attachmentName,
    required this.attachmentUrl,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: CustomAppBar(
        title: attachmentName,
        centerTitle: true,
        showBackButton: true,
        actions: [
          IconButton(
            onPressed: () {
              // TODO: 实现下载功能
            },
            icon: const Icon(Icons.download),
          ),
        ],
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // TODO: 根据文件类型显示不同的预览
            const Icon(
              Icons.insert_drive_file,
              size: 100,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              attachmentName,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '文件大小：2.5MB',
              style: TextStyle(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: 实现预览功能
              },
              icon: const Icon(Icons.remove_red_eye),
              label: const Text('预览文件'),
            ),
          ],
        ),
      ),
    );
  }
} 