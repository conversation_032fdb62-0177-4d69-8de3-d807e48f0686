/// -----
/// internship_complaint_screen.dart
/// 
/// 实习投诉页面，提供用户提交实习相关的投诉功能，支持文本描述和图片上传。
///
/// <AUTHOR>
/// @date 2025-05-22
/// @copyright Copyright 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

/// 实习投诉页面
/// 
/// 该页面允许用户提交实习相关的投诉，包括文字描述和上传图片。
/// 主要功能：
/// - 输入投诉内容
/// - 上传相关图片（最多6张）
/// - 提交投诉信息
class InternshipComplaintScreen extends StatefulWidget {
  /// 创建实习投诉页面
  /// 
  /// [key] 用于控制widget如何替换树中的另一个widget
  const InternshipComplaintScreen({Key? key}) : super(key: key);

  @override
  State<InternshipComplaintScreen> createState() => _InternshipComplaintScreenState();
  
  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DiagnosticsProperty<Key?>('key', key));
  }
}

/// 实习投诉页面的状态类
/// 
/// 管理投诉页面的状态，包括：
/// - 投诉内容输入控制
/// - 图片选择和管理
/// - 表单验证和提交
class _InternshipComplaintScreenState extends State<InternshipComplaintScreen> {
  /// 投诉内容输入控制器
  final TextEditingController _complaintController = TextEditingController();
  
  /// 已选择的图片文件列表
  final List<File> _selectedImages = [];
  
  /// 最大可上传图片数量
  static const int _maxImages = 6;
  
  /// 投诉内容最大字数限制
  static const int _maxTextLength = 1000;
  
  @override
  void dispose() {
    _complaintController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '实习投诉',
        backgroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Container(
          margin: const EdgeInsets.only(top: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 投诉内容输入区域
              _buildComplaintInput(),
              
              const SizedBox(height: 20),
              
              // 图片上传区域
              _buildImageUploadSection(),
              
              // 提交按钮
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建投诉内容输入框
  /// 
  /// 返回一个带有多行文本输入功能的组件，用于输入投诉内容。
  /// 包含字数统计和最大字数限制功能。
  /// 
  /// @return 返回构建好的文本输入组件
  Widget _buildComplaintInput() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(bottom: 20, right: 20),
      child: Stack(
        children: [
          TextField(
            controller: _complaintController,
            maxLines: 8,
            maxLength: _maxTextLength,
            decoration: const InputDecoration(
              hintText: '请输入您的投诉内容',
              filled: false,
              fillColor: Colors.white,
              contentPadding: EdgeInsets.all(12),
              hintStyle: TextStyle(color: AppTheme.black666, fontSize: 16),
              border: InputBorder.none,
              enabledBorder: InputBorder.none,
              focusedBorder: InputBorder.none,
              counterText: '', // 隐藏默认的计数器
            ),
            style: const TextStyle(
              fontSize: 16,
              color: AppTheme.black333,
              height: 1.5,
            ),
            onChanged: (value) {
              setState(() {});
            },
          ),
          // 自定义计数器
          Positioned(
            right: 0,
            bottom: 0,
            child: Text(
              '${_complaintController.text.length}/$_maxTextLength',
              style: TextStyle(
                color: _complaintController.text.length > _maxTextLength 
                    ? Colors.red 
                    : AppTheme.black666,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建图片上传区域
  /// 
  /// 包含标题和图片网格布局，显示已上传的图片和添加按钮。
  /// 
  /// @return 返回图片上传区域组件
  Widget _buildImageUploadSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: const EdgeInsets.only(left: 10,bottom: 10),
          child: const Text(
            '上传图片(最多6张)',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: AppTheme.black999,
            ),
          ),
        ),
        const SizedBox(height: 12),
        _buildImageGrid(),
      ],
    );
  }
  
  /// 构建图片网格布局
  /// 
  /// 以网格形式显示已选择的图片和添加按钮。
  /// 每行显示3张图片，最多显示6张。
  /// 
  /// @return 返回图片网格组件
  Widget _buildImageGrid() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 10),
      child: GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 3,
        mainAxisSpacing: 10,
        crossAxisSpacing: 10,
        childAspectRatio: 1,
        children: [
          ..._selectedImages.map(_buildImageItem),
          if (_selectedImages.length < _maxImages)
            _buildAddImageButton(),
        ],
      ),
    );
  }
  
  /// 构建单个图片项
  /// 
  /// 显示已选择的图片，并提供删除功能。
  /// 
  /// @param image 要显示的图片文件
  /// @return 返回图片项组件
  Widget _buildImageItem(File image) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
            image: DecorationImage(
              image: FileImage(image),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -10,
          right: -10,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedImages.remove(image);
              });
            },
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
  
  /// 构建添加图片按钮
  /// 
  /// 当图片数量未达到上限时显示，点击后打开图片选择器。
  /// 
  /// @return 返回添加图片按钮组件
  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Icon(
            Icons.add,
            size: 40,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }
  
  /// 构建提交按钮
  /// 
  /// 提交按钮在表单验证通过后启用。
  /// 
  /// @return 返回提交按钮组件
  Widget _buildSubmitButton() {
    return Container(
      margin: const EdgeInsets.only(left:15,right:15,top: 30),
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _submitComplaint,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          '提交',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
  
  /// 从设备相册或相机选择图片
  /// 
  /// 使用image_picker插件打开图片选择器，支持从相册选择或拍照。
  /// 选择的图片会被添加到图片列表中。
  /// 
  /// @throws 如果选择图片时发生错误，会抛出异常
  Future<void> _pickImage() async {
    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);
      
      if (pickedFile != null) {
        setState(() {
          _selectedImages.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      // 处理异常情况
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择图片失败，请重试')),
      );
    }
  }
  
  /// 提交投诉
  /// 
  /// 验证表单数据，如果验证通过则提交投诉信息。
  /// 提交成功或失败后会显示相应的提示信息。
  /// 
  /// 表单验证规则：
  /// - 投诉内容不能为空
  /// - 投诉内容长度不能超过1000字
  void _submitComplaint() {
    if (_complaintController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入投诉内容')),
      );
      return;
    }
    
    // 这里可以实现提交投诉的逻辑
    // 例如调用API发送投诉内容和图片
    
    // 模拟提交成功
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('投诉已提交')),
    );
    
    // 提交成功后返回上一页
    Future.delayed(const Duration(seconds: 1), () {
      Navigator.pop(context);
    });
  }
} 