/// -----
/// approval_tab_bar.dart
///
/// 审批标签栏组件，用于展示待审批和已审批的标签，支持在待审批标签上显示数量统计
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';

/// 审批标签栏组件
///
/// 用于展示待审批和已审批的标签，支持在待审批标签上显示数量统计
/// 支持自定义标签文本、标签样式、指示器样式等
class ApprovalTabBar extends StatelessWidget {
  /// 标签控制器
  final TabController controller;

  /// 待审批数量
  final int pendingCount;

  /// 待审批标签文本
  final String pendingText;

  /// 已审批标签文本
  final String approvedText;

  /// 标签栏背景颜色
  final Color backgroundColor;

  /// 选中标签颜色
  final Color? selectedLabelColor;

  /// 未选中标签颜色
  final Color? unselectedLabelColor;

  /// 指示器颜色
  final Color? indicatorColor;

  /// 指示器粗细
  final double indicatorWeight;

  /// 指示器大小
  final TabBarIndicatorSize indicatorSize;

  /// 是否显示数字统计为单独的标签
  ///
  /// 如果为true，则在待审批标签右上角显示数字统计
  /// 如果为false，则在待审批标签文本中显示数字统计，如"待审批 (5)"
  final bool showCountAsBadge;

  /// 数字统计标签的颜色
  final Color badgeColor;

  /// 数字统计标签的文本颜色
  final Color badgeTextColor;

  /// 选中标签的文本样式
  final TextStyle? labelStyle;

  /// 未选中标签的文本样式
  final TextStyle? unselectedLabelStyle;

  /// 创建审批标签栏组件
  ///
  /// [controller] 标签控制器，必须提供
  /// [pendingCount] 待审批数量，默认为0
  /// [pendingText] 待审批标签文本，默认为"待审批"
  /// [approvedText] 已审批标签文本，默认为"已审批"
  /// [backgroundColor] 标签栏背景颜色，默认为白色
  /// [selectedLabelColor] 选中标签颜色，默认为主题色
  /// [unselectedLabelColor] 未选中标签颜色，默认为灰色
  /// [indicatorColor] 指示器颜色，默认为主题色
  /// [indicatorWeight] 指示器粗细，默认为3.0
  /// [indicatorSize] 指示器大小，默认为TabBarIndicatorSize.label
  /// [showCountAsBadge] 是否显示数字统计为单独的标签，默认为true
  /// [badgeColor] 数字统计标签的颜色，默认为红色
  /// [badgeTextColor] 数字统计标签的文本颜色，默认为白色
  /// [labelStyle] 选中标签的文本样式
  /// [unselectedLabelStyle] 未选中标签的文本样式
  const ApprovalTabBar({
    Key? key,
    required this.controller,
    this.pendingCount = 0,
    this.pendingText = '待审批',
    this.approvedText = '已审批',
    this.backgroundColor = Colors.white,
    this.selectedLabelColor,
    this.unselectedLabelColor,
    this.indicatorColor,
    this.indicatorWeight = 3.0,
    this.indicatorSize = TabBarIndicatorSize.label,
    this.showCountAsBadge = true,
    this.badgeColor = Colors.red,
    this.badgeTextColor = Colors.white,
    this.labelStyle,
    this.unselectedLabelStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: backgroundColor,
      child: TabBar(
        controller: controller,
        indicatorColor: indicatorColor ?? AppTheme.primaryColor,
        labelColor: selectedLabelColor ?? AppTheme.primaryColor,
        unselectedLabelColor: unselectedLabelColor ?? Colors.grey,
        labelStyle: labelStyle ?? const TextStyle(fontWeight: FontWeight.bold),
        unselectedLabelStyle: unselectedLabelStyle ?? const TextStyle(fontWeight: FontWeight.normal),
        indicatorWeight: indicatorWeight,
        indicatorSize: indicatorSize,
        // 设置指示器宽度为40px
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(
            width: indicatorWeight,
            color: indicatorColor ?? AppTheme.primaryColor,
          ),
          insets: const EdgeInsets.symmetric(horizontal: 30), // 调整指示器宽度约为40px
        ),
        tabs: [
          _buildPendingTab(),
          Tab(text: approvedText),
        ],
      ),
    );
  }

  /// 构建待审批标签
  Widget _buildPendingTab() {
    if (showCountAsBadge) {
      return Tab(
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Text(pendingText),
            if (pendingCount > 0)
              Positioned(
                right: -15,
                top: -8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
                  decoration: BoxDecoration(
                    color: badgeColor,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    '$pendingCount',
                    style: TextStyle(
                      color: badgeTextColor,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      );
    } else {
      return Tab(text: '$pendingText (${pendingCount > 0 ? pendingCount : 0})');
    }
  }
}
