/// -----
/// plan_list_bloc.dart
/// 
/// 实习计划列表BLoC，处理实习计划列表相关的业务逻辑
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/plan/data/models/internship_plan_info.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/plan_list_event.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/plan_list_state.dart';

/// 实习计划列表BLoC
///
/// 处理实习计划列表相关的业务逻辑，包括加载列表、申请实习、申请免实习等
class PlanListBloc extends Bloc<PlanListEvent, PlanListState> {
  PlanListBloc() : super(PlanListInitial()) {
    on<LoadPlanListEvent>(_onLoadPlanList);
    on<RefreshPlanListEvent>(_onRefreshPlanList);
    on<ApplyInternshipEvent>(_onApplyInternship);
    on<ApplyExemptionEvent>(_onApplyExemption);
  }

  /// 处理加载实习计划列表事件
  Future<void> _onLoadPlanList(
    LoadPlanListEvent event,
    Emitter<PlanListState> emit,
  ) async {
    emit(PlanListLoading());
    
    try {
      // TODO: 从仓库获取实际数据
      // 这里使用模拟数据
      await Future.delayed(const Duration(milliseconds: 800));
      
      final plans = InternshipPlanInfo.sampleDataList();
      
      emit(PlanListLoaded(plans: plans));
    } catch (e) {
      emit(PlanListError(message: '加载实习计划失败: ${e.toString()}'));
    }
  }

  /// 处理刷新实习计划列表事件
  Future<void> _onRefreshPlanList(
    RefreshPlanListEvent event,
    Emitter<PlanListState> emit,
  ) async {
    try {
      // TODO: 从仓库获取实际数据
      // 这里使用模拟数据
      await Future.delayed(const Duration(milliseconds: 800));
      
      final plans = InternshipPlanInfo.sampleDataList();
      
      emit(PlanListLoaded(plans: plans));
    } catch (e) {
      emit(PlanListError(message: '刷新实习计划失败: ${e.toString()}'));
    }
  }

  /// 处理申请实习事件
  Future<void> _onApplyInternship(
    ApplyInternshipEvent event,
    Emitter<PlanListState> emit,
  ) async {
    final currentState = state;
    if (currentState is PlanListLoaded) {
      emit(PlanListApplying(planId: event.planId));
      
      try {
        // TODO: 调用实际的申请实习API
        await Future.delayed(const Duration(milliseconds: 1000));
        
        // 重新加载列表
        add(const LoadPlanListEvent());
      } catch (e) {
        emit(PlanListError(message: '申请实习失败: ${e.toString()}'));
        // 恢复之前的状态
        emit(currentState);
      }
    }
  }

  /// 处理申请免实习事件
  Future<void> _onApplyExemption(
    ApplyExemptionEvent event,
    Emitter<PlanListState> emit,
  ) async {
    final currentState = state;
    if (currentState is PlanListLoaded) {
      emit(PlanListExempting(planId: event.planId));
      
      try {
        // TODO: 调用实际的申请免实习API
        await Future.delayed(const Duration(milliseconds: 1000));
        
        // 重新加载列表
        add(const LoadPlanListEvent());
      } catch (e) {
        emit(PlanListError(message: '申请免实习失败: ${e.toString()}'));
        // 恢复之前的状态
        emit(currentState);
      }
    }
  }
}
