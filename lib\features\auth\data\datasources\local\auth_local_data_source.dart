import 'dart:convert';
import '../../../../../core/constants/constants.dart';
import '../../../../../core/error/exceptions/cache_exception.dart';
import '../../../../../core/storage/local_storage.dart';
import '../../models/user_model.dart';

/// 认证本地数据源接口
///
/// 定义与本地存储交互的方法
abstract class AuthLocalDataSource {
  /// 缓存用户信息
  ///
  /// 参数：[user] 用户模型
  /// 返回：bool 是否成功
  /// 抛出：CacheException 缓存异常
  Future<bool> cacheUser(UserModel user);

  /// 获取缓存的用户信息
  ///
  /// 返回：UserModel? 用户模型，如果没有缓存则返回 null
  /// 抛出：CacheException 缓存异常
  UserModel? getCachedUser();

  /// 缓存令牌
  ///
  /// 参数：[token] 令牌
  /// 返回：bool 是否成功
  /// 抛出：CacheException 缓存异常
  Future<bool> cacheToken(String token);

  /// 获取缓存的令牌
  ///
  /// 返回：String? 令牌，如果没有缓存则返回 null
  /// 抛出：CacheException 缓存异常
  String? getCachedToken();

  /// 缓存用户类型
  ///
  /// 参数：[userType] 用户类型
  /// 返回：bool 是否成功
  /// 抛出：CacheException 缓存异常
  Future<bool> cacheUserType(String userType);

  /// 获取缓存的用户类型
  ///
  /// 返回：int? 用户类型，如果没有缓存则返回 null
  /// 抛出：CacheException 缓存异常
  String? getCachedUserType();

  /// 清除缓存
  ///
  /// 返回：bool 是否成功
  /// 抛出：CacheException 缓存异常
  Future<bool> clearCache();
}

/// 认证本地数据源实现
///
/// 实现与本地存储交互的方法
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final LocalStorage _localStorage;

  AuthLocalDataSourceImpl(this._localStorage);

  @override
  Future<bool> cacheUser(UserModel user) async {
    try {
      final userJson = json.encode(user.toJson());
      return await _localStorage.setString(AppConstants.userKey, userJson);
    } catch (e) {
      throw CacheException('缓存用户信息失败: $e');
    }
  }

  @override
  UserModel? getCachedUser() {
    try {
      final userJson = _localStorage.getString(AppConstants.userKey);
      if (userJson == null) {
        return null;
      }
      return UserModel.fromJson(json.decode(userJson));
    } catch (e) {
      throw CacheException('获取缓存的用户信息失败: $e');
    }
  }

  @override
  Future<bool> cacheToken(String token) async {
    try {
      return await _localStorage.setString(AppConstants.tokenKey, token);
    } catch (e) {
      throw CacheException('缓存令牌失败: $e');
    }
  }

  @override
  String? getCachedToken() {
    try {
      return _localStorage.getString(AppConstants.tokenKey);
    } catch (e) {
      throw CacheException('获取缓存的令牌失败: $e');
    }
  }

  @override
  Future<bool> cacheUserType(String userType) async {
    try {
      return await _localStorage.setString(AppConstants.userTypeKey, userType);
    } catch (e) {
      throw CacheException('缓存用户类型失败: $e');
    }
  }

  @override
  String? getCachedUserType() {
    try {
      return _localStorage.getString(AppConstants.userTypeKey);
    } catch (e) {
      throw CacheException('获取缓存的用户类型失败: $e');
    }
  }

  @override
  Future<bool> clearCache() async {
    try {
      // 清除所有与用户相关的数据
      await _localStorage.remove(AppConstants.userKey);
      await _localStorage.remove(AppConstants.tokenKey);
      await _localStorage.remove(AppConstants.userTypeKey);
      await _localStorage.remove(AppConstants.phoneKey);
      await _localStorage.remove(AppConstants.passwordKey);
      await _localStorage.remove(AppConstants.isLoggedInKey);

      return true;
    } catch (e) {
      throw CacheException('清除缓存失败: $e');
    }
  }
}
