/// -----
/// internship_change_detail_screen.dart
/// 
/// 实习信息变更申请详情页面，展示实习信息变更申请的详细信息
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/company_info_form.dart';
import 'package:flutter_demo/core/widgets/job_position_info.dart';
import 'package:flutter_demo/core/widgets/internship_info_form.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/data/models/internship_change_model.dart';

class InternshipChangeDetailScreen extends StatefulWidget {
  final String? changeId;
  
  const InternshipChangeDetailScreen({
    Key? key,
    this.changeId,
  }) : super(key: key);

  @override
  State<InternshipChangeDetailScreen> createState() => _InternshipChangeDetailScreenState();
}

class _InternshipChangeDetailScreenState extends State<InternshipChangeDetailScreen> {
  late InternshipChangeModel _changeModel;
  late CompanyInfo _companyInfo;
  late InternshipInfo _internshipInfo;
  bool _showAttachments = false;
  final String _courseName = '2021级市场销售2023-2024实习学年第二学期岗位实习';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  void _loadData() {
    // 在实际场景中，应该根据传入的changeId从API获取数据
    // 这里使用模拟数据
    final List<InternshipChangeModel> sampleData = InternshipChangeModel.getSampleData();
    _changeModel = sampleData.firstWhere(
      (model) => model.id == (widget.changeId ?? '1'),
      orElse: () => sampleData.first,
    );
    
    // 构造公司信息
    _companyInfo = CompanyInfo(
      name: '由 ${_changeModel.oldCompany} 变更为 ${_changeModel.newCompany}',
      creditCode: '91310000332546552F',
      size: '100-499人',
      type: '民营（私营）企业',
      industry: '软件和信息技术服务业',
      location: '海外/其他/其他',
      address: '缅甸',
      contactPerson: '易民',
      contactPhone: '18627171276',
      email: '<EMAIL>',
      zipCode: '<EMAIL>',
      leaveReason: _changeModel.changeReason,
    );
    
    // 构造实习信息
    _internshipInfo = InternshipInfo(
      startTime: '2023-01-10',
      endTime: '2023-12-21',
      internshipStyle: '自主联系',
      professionalMatch: '基本匹配',
      stipend: '3500',
      accommodationType: '自行安排',
      accommodationAddress: '曙光',
      accommodationRegion: '湖北省/武汉市/洪山区',
      providesMeals: false,
      hasSpecialCircumstances: false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: const CustomAppBar(
        title: '实习信息变更申请',
        centerTitle: true,
        showBackButton: true,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 课程头部
            CourseHeaderSection(
              courseName: _courseName,
              initialExpanded: false,
            ),
            
            const SizedBox(height: 10),
            
            // 用户头像和姓名
            _buildUserHeader(),

            const SizedBox(height: 10),
            
            // 企业信息
            CompanyInfoForm(
              companyInfo: _companyInfo,
              onCompanyInfoChanged: (info) {
                // 实际应用中处理数据更新
              },
              showLeaveReason: false,
              showVerifiedIcon: true,
              // readOnly: true,
            ),
            
            const SizedBox(height: 10),
            
            // 岗位信息
            JobPositionInfo(
              jobType: '普通劳动合同就业',
              jobAgreement: '普通劳动合同就业',
              department: '由 ${_changeModel.oldDepartment} 变更为 ${_changeModel.newDepartment}',
              position: '由 ${_changeModel.oldPosition} 变更为 ${_changeModel.newPosition}',
              positionCategory: '技术人员',
              trainingFee: '3500',
              isProfessionalMatch: true,
              agreementNumber: '585758',
            ),
            
            const SizedBox(height: 10),
            
            // 实习信息
            InternshipInfoForm(
              internshipInfo: _internshipInfo,
              onInternshipInfoChanged: (info) {
                // 实际应用中处理数据更新
              },
              showChangeHighlight: true,
              readOnly: true,
            ),
            
            const SizedBox(height: 10),
            
            // 附件
            _buildAttachmentsSection(),
            
            const SizedBox(height: 10),
            
            // 变更理由
            _buildChangeReasonSection(),
            
            const SizedBox(height: 70),
          ],
        ),
      ),
      bottomNavigationBar: _changeModel.status == '待审批' ? _buildBottomButtons() : null,
    );
  }

  // 用户头像和姓名
  Widget _buildUserHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundColor: Colors.grey[300],
            child: Text(
              _changeModel.studentName.isNotEmpty ? _changeModel.studentName.substring(0, 1) : '',
              style: const TextStyle(
                fontSize: 20,
                color: Colors.black87,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _changeModel.studentName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              GestureDetector(
                onTap: () {
                  // 添加拨打电话功能
                },
                child: Row(
                  children: [
                    Text(
                      _changeModel.phoneNumber,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      Icons.phone,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getStatusColor(_changeModel.status).withOpacity(0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _changeModel.status,
              style: TextStyle(
                color: _getStatusColor(_changeModel.status),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 附件部分
  Widget _buildAttachmentsSection() {
    if (_changeModel.attachments.isEmpty) {
      return const SizedBox.shrink();
    }
    
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Text(
                '附件',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () {
                  setState(() {
                    _showAttachments = !_showAttachments;
                  });
                },
                child: Row(
                  children: [
                    Text(_showAttachments ? '收起' : '查看附件'),
                    Icon(_showAttachments ? Icons.expand_less : Icons.expand_more),
                  ],
                ),
              ),
            ],
          ),
          if (_showAttachments) ...[
            const SizedBox(height: 16),
            ..._changeModel.attachments.map((attachment) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(attachment['name'] ?? ''),
                  const SizedBox(height: 8),
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[300]!),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.insert_drive_file_outlined,
                            size: 40,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            attachment['name'] ?? '',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
              );
            }).toList(),
          ],
        ],
      ),
    );
  }

  // 变更理由部分
  Widget _buildChangeReasonSection() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '变更理由',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            _changeModel.changeReason,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                '申请时间：${_changeModel.applyDate}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 底部按钮
  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                _showRejectDialog();
              },
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: Colors.red,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('驳回'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                _showApproveDialog();
              },
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: AppTheme.primaryColor,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text('通过'),
            ),
          ),
        ],
      ),
    );
  }

  // 显示驳回对话框
  void _showRejectDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认驳回'),
        content: const Text('您确定要驳回该实习信息变更申请吗？'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('申请已驳回')),
              );
              Navigator.pop(context);
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }

  // 显示通过对话框
  void _showApproveDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认通过'),
        content: const Text('您确定要通过该实习信息变更申请吗？'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('申请已通过')),
              );
              Navigator.pop(context);
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }

  // 根据状态获取颜色
  Color _getStatusColor(String status) {
    switch (status) {
      case '待审批':
        return Colors.orange;
      case '已通过':
        return Colors.green;
      case '已驳回':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
} 