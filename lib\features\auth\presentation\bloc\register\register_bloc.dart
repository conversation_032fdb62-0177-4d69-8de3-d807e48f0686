import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/device_utils.dart';
import '../../../domain/usecases/register_usecase.dart';
import '../../../domain/usecases/send_verification_code_usecase.dart';
import 'register_event.dart';
import 'register_state.dart';

/// 注册 BLoC
///
/// 处理注册相关的业务逻辑
class RegisterBloc extends Bloc<RegisterEvent, RegisterState> {
  final RegisterUseCase _registerUseCase;
  final SendVerificationCodeUseCase _sendVerificationCodeUseCase;
  Timer? _countdownTimer;
  int _countdownTime = 0;

  RegisterBloc({
    required RegisterUseCase registerUseCase,
    required SendVerificationCodeUseCase sendVerificationCodeUseCase,
  })  : _registerUseCase = registerUseCase,
        _sendVerificationCodeUseCase = sendVerificationCodeUseCase,
        super(RegisterInitialState()) {
    on<RegisterRequestEvent>(_onRegisterRequest);
    on<SendVerificationCodeEvent>(_onSendVerificationCode);
  }

  /// 处理注册请求事件
  Future<void> _onRegisterRequest(
    RegisterRequestEvent event,
    Emitter<RegisterState> emit,
  ) async {
    emit(RegisterLoadingState());

    final result = await _registerUseCase(
      RegisterParams(
        phone: event.phone,
        code: event.code,
        password: event.password,
      ),
    );

    result.fold(
      (failure) => emit(RegisterFailureState(failure.message)),
      (_) => emit(RegisterSuccessState()),
    );
  }

  /// 处理发送验证码事件
  Future<void> _onSendVerificationCode(
    SendVerificationCodeEvent event,
    Emitter<RegisterState> emit,
  ) async {
    // 验证手机号
    if (event.phone.isEmpty || !RegExp(r'^1[3-9]\d{9}$').hasMatch(event.phone)) {
      emit(const VerificationCodeFailureState('请输入有效的手机号'));
      return;
    }

    emit(VerificationCodeSendingState());

    try {
      // 获取设备ID
      final deviceId = await DeviceUtils.getDeviceId();

      // 调用发送验证码用例
      final result = await _sendVerificationCodeUseCase(
        SendVerificationCodeParams(
          phone: event.phone,
          deviceId: deviceId,
        ),
      );

      result.fold(
        (failure) => emit(VerificationCodeFailureState(failure.message)),
        (success) {
          // 开始倒计时
          _countdownTime = 60;
          emit(VerificationCodeSentState(_countdownTime));

          // 创建倒计时定时器
          _countdownTimer?.cancel();
          _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
            _countdownTime--;
            if (_countdownTime <= 0) {
              timer.cancel();
              _countdownTimer = null;
            }
            if (!isClosed) {
              emit(VerificationCodeSentState(_countdownTime));
            }
          });
        },
      );
    } on Exception catch (e) {
      emit(VerificationCodeFailureState('发送验证码失败: $e'));
    }
  }

  @override
  Future<void> close() {
    _countdownTimer?.cancel();
    return super.close();
  }
}
