/// 实习信息实体类
///
/// 用于表示和管理实习的详细信息，包括实习时间、类型、薪资、住宿等信息。
/// 实现了JSON序列化和反序列化方法，便于网络传输和本地存储。
///
/// 示例代码：
/// ```dart
/// final internship = InternshipInfo(
///   startDate: '2023-01-01',
///   endDate: '2023-06-30',
///   // ...其他参数
/// );
/// ```
class InternshipInfo {
  /// 实习开始日期（格式：'YYYY-MM-DD'）
  final String startDate;
  /// 实习结束日期（格式：'YYYY-MM-DD'）
  final String endDate;
  /// 实习类型（如：'自主联系'）
  final String internshipType;
  /// 专业匹配度（如：'基本匹配'）
  final String professionalMatch;
  /// 实习薪资（单位：元）
  final String salary;
  /// 住宿类型（如：'自行安排'）
  final String accommodationType;
  /// 住宿区域（省/市/区）
  final String accommodationArea;
  /// 详细住宿地址
  final String accommodationAddress;
  /// 是否提供餐食（'是'/'否'）
  final String provideMeals;
  /// 特殊情况说明（如：'否' 或其他说明）
  final String specialCircumstances;

  /// 构造函数
  ///
  /// 创建一个[InternshipInfo]实例
  ///
  /// 所有参数都是必需的，用于完整描述实习信息。
  const InternshipInfo({
    required this.startDate,
    required this.endDate,
    required this.internshipType,
    required this.professionalMatch,
    required this.salary,
    required this.accommodationType,
    required this.accommodationArea,
    required this.accommodationAddress,
    required this.provideMeals,
    required this.specialCircumstances,
  });

  /// 从JSON Map创建[InternshipInfo]实例
  ///
  /// 用于从服务器响应或本地存储中反序列化实习信息
  ///
  /// 参数:
  ///   - [json]: 包含实习信息的JSON对象
  factory InternshipInfo.fromJson(Map<String, dynamic> json) {
    return InternshipInfo(
      startDate: json['startDate'] ?? '',
      endDate: json['endDate'] ?? '',
      internshipType: json['internshipType'] ?? '',
      professionalMatch: json['professionalMatch'] ?? '',
      salary: json['salary'] ?? '',
      accommodationType: json['accommodationType'] ?? '',
      accommodationArea: json['accommodationArea'] ?? '',
      accommodationAddress: json['accommodationAddress'] ?? '',
      provideMeals: json['provideMeals'] ?? '',
      specialCircumstances: json['specialCircumstances'] ?? '',
    );
  }

  /// 将[InternshipInfo]实例转换为JSON Map
  ///
  /// 用于将实习信息序列化为JSON格式，便于网络传输或本地存储
  ///
  /// 返回:
  ///   包含实习信息的Map对象
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate,
      'endDate': endDate,
      'internshipType': internshipType,
      'professionalMatch': professionalMatch,
      'salary': salary,
      'accommodationType': accommodationType,
      'accommodationArea': accommodationArea,
      'accommodationAddress': accommodationAddress,
      'provideMeals': provideMeals,
      'specialCircumstances': specialCircumstances,
    };
  }

  /// 获取示例数据
  ///
  /// 用于开发和测试，返回一个预定义的[InternshipInfo]实例
  ///
  /// 返回:
  ///   包含示例数据的[InternshipInfo]实例
  static InternshipInfo sampleData() {
    return InternshipInfo(
      startDate: '2023-01-10',
      endDate: '2023-12-21',
      internshipType: '自主联系',
      professionalMatch: '基本匹配',
      salary: '3500',
      accommodationType: '自行安排',
      accommodationArea: '湖北省/武汉市/洪山区',
      accommodationAddress: '曙光',
      provideMeals: '否',
      specialCircumstances: '否',
    );
  }
} 