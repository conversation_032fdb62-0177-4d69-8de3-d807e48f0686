/// -----
/// plan_list_state.dart
/// 
/// 实习计划列表状态定义
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/plan/data/models/internship_plan_info.dart';

/// 实习计划列表状态基类
abstract class PlanListState extends Equatable {
  const PlanListState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class PlanListInitial extends PlanListState {}

/// 加载中状态
class PlanListLoading extends PlanListState {}

/// 加载成功状态
class PlanListLoaded extends PlanListState {
  /// 实习计划列表
  final List<InternshipPlanInfo> plans;

  const PlanListLoaded({required this.plans});

  @override
  List<Object?> get props => [plans];
}

/// 加载失败状态
class PlanListError extends PlanListState {
  /// 错误信息
  final String message;

  const PlanListError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// 申请实习处理中状态
class PlanListApplying extends PlanListState {
  /// 实习计划ID
  final String planId;

  const PlanListApplying({required this.planId});

  @override
  List<Object?> get props => [planId];
}

/// 申请免实习处理中状态
class PlanListExempting extends PlanListState {
  /// 实习计划ID
  final String planId;

  const PlanListExempting({required this.planId});

  @override
  List<Object?> get props => [planId];
}
