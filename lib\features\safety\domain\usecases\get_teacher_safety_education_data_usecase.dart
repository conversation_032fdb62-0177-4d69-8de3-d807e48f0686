/// -----
/// get_teacher_safety_education_data_usecase.dart
/// 
/// 获取教师端安全教育考试数据用例
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_demo/core/error/failures/failure.dart';

import '../../../../core/usecases/usecase.dart';
import '../models/teacher_safety_education_response.dart';
import '../repositories/teacher_safety_education_repository.dart';

/// 获取教师端安全教育考试数据用例
/// 
/// 封装获取教师端安全教育考试数据的业务逻辑
class GetTeacherSafetyEducationDataUseCase implements UseCase<TeacherSafetyEducationResponse, GetTeacherSafetyEducationDataParams> {
  final TeacherSafetyEducationRepository _repository;

  GetTeacherSafetyEducationDataUseCase(this._repository);

  @override
  Future<Either<Failure, TeacherSafetyEducationResponse>> call(GetTeacherSafetyEducationDataParams params) async {
    return await _repository.getTeacherSafetyEducationData(params.planId);
  }
}

/// 获取教师端安全教育考试数据用例参数
/// 
/// 包含获取数据所需的实习计划ID
class GetTeacherSafetyEducationDataParams extends Equatable {
  /// 实习计划ID
  final String planId;

  const GetTeacherSafetyEducationDataParams({
    required this.planId,
  });

  @override
  List<Object?> get props => [planId];
}
