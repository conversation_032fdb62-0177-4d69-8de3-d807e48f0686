import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/company_logo.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

/// -----
/// about_us_screen.dart
/// 
/// 关于我们页面，显示应用版本信息和公司信息
///
/// <AUTHOR>
/// @date 2025-05-28
/// @copyright Copyright © 2025 亿硕教育
/// -----

class AboutUsScreen extends StatefulWidget {
  const AboutUsScreen({Key? key}) : super(key: key);

  @override
  State<AboutUsScreen> createState() => _AboutUsScreenState();
}

class _AboutUsScreenState extends State<AboutUsScreen> {
  String _version = '加载中...';
  
  @override
  void initState() {
    super.initState();
    // _loadAppVersion();
  }
  
  /// 加载应用版本号
  Future<void> _loadAppVersion() async {

  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: const CustomAppBar(
        title: '关于我们',
        backgroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 主要内容区域
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 62.h),
                  // Logo
                  Image.asset(
                    'assets/images/logo_icon.png',
                    width: 144.w,
                    height: 144.h,
                    fit: BoxFit.cover,
                  ),
                  // 平台名称
                  _buildCompanyName(),
                  SizedBox(height: 60.h),
                  // 信息卡片
                  _buildInfoCard(),
                ],
              ),
            ),
          ),

          // 底部区域
          Column(
            children: [
              // 用户协议
              _buildUserAgreement(),
              SizedBox(height: 15.h),
              // 版权信息
              _buildCopyright(),
              SizedBox(height: 30.h),
            ],
          ),
        ],
      ),
    );
  }

  // 公司名称
  Widget _buildCompanyName() {
    return const Text(
      'AI实习管理平台',
      style: TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color(0xFF333333),
      ),
    );
  }

  // 信息卡片
  Widget _buildInfoCard() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.symmetric(horizontal: 25.w),
      padding: EdgeInsets.symmetric(vertical: 40.h, horizontal: 40.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInfoItem('客服热线：', '400-0000-000'),
          SizedBox(height: 15),
          _buildInfoItem('客服邮箱：', '<EMAIL>'),
          SizedBox(height: 15),
          _buildInfoItem('当前版本：', _version),
        ],
      ),
    );
  }

  // 信息项
  Widget _buildInfoItem(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFF999999),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 24.sp,
            color: const Color(0xFF999999),
          ),
        ),
      ],
    );
  }

  // 用户协议
  Widget _buildUserAgreement() {
    return GestureDetector(
      onTap: _openUserAgreement,
      child: Text(
        '《用户服务协议》',
        style: TextStyle(
          fontSize: 24.sp,
          color: AppTheme.blue2165f6,
          decoration: TextDecoration.underline,
        ),
      ),
    );
  }

  // 版权信息
  Widget _buildCopyright() {
    return Text(
      'COPYRIGHT @ 优品汇 2015 - 2017',
      style: TextStyle(
        fontSize: 22.sp,
        color: const Color(0xFF999999),
      ),
    );
  }

  // 打开用户协议
  Future<void> _openUserAgreement() async {
    const url = 'https://www.example.com/user-agreement';
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      debugPrint('Could not launch $url');
    }
  }
}