/// -----
/// get_exemption_record_usecase.dart
///
/// 获取免实习记录用例
/// 处理获取免实习记录的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:dartz/dartz.dart';
import 'package:flutter_demo/core/error/failures/failure.dart';
import 'package:flutter_demo/core/usecases/usecase.dart';
import '../../data/models/exemption_record_model.dart';
import '../repositories/exemption_records_repository.dart';

/// 获取免实习记录用例参数
class GetExemptionRecordParams {
  /// 实习计划ID
  final String planId;

  const GetExemptionRecordParams({
    required this.planId,
  });
}

/// 获取免实习记录用例
///
/// 处理获取学生免实习记录的业务逻辑
class GetExemptionRecordUseCase implements UseCase<ExemptionRecordModel?, GetExemptionRecordParams> {
  final ExemptionRecordsRepository _repository;

  const GetExemptionRecordUseCase({
    required ExemptionRecordsRepository repository,
  }) : _repository = repository;

  @override
  Future<Either<Failure, ExemptionRecordModel?>> call(GetExemptionRecordParams params) async {
    return await _repository.getExemptionRecord(
      planId: params.planId,
    );
  }
}
