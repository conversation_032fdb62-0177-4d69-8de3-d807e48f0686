/// -----
/// exemption_application_repository_impl.dart
///
/// 免实习申请仓库实现
/// 实现免实习申请相关的业务操作
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// 第三方库
import 'package:dartz/dartz.dart';

// 项目内部库
import 'package:flutter_demo/core/error/exceptions/auth_exception.dart';
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/error/failures/auth_failure.dart';
import 'package:flutter_demo/core/error/failures/failure.dart';
import 'package:flutter_demo/core/network/network_info.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/plan/data/datasources/exemption_application_data_source.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_request_model.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_response_model.dart';
import 'package:flutter_demo/features/plan/domain/repositories/exemption_application_repository.dart';

/// 免实习申请仓库实现
///
/// 实现免实习申请相关的业务操作，处理网络状态和异常
class ExemptionApplicationRepositoryImpl implements ExemptionApplicationRepository {
  static const String _tag = 'ExemptionApplicationRepository';
  
  final ExemptionApplicationDataSource _dataSource;
  final NetworkInfo _networkInfo;

  const ExemptionApplicationRepositoryImpl({
    required ExemptionApplicationDataSource dataSource,
    required NetworkInfo networkInfo,
  }) : _dataSource = dataSource, _networkInfo = networkInfo;

  @override
  Future<Either<Failure, ExemptionApplicationResponseModel>> submitExemptionApplication(
    ExemptionApplicationRequestModel request,
  ) async {
    Logger.info(_tag, '开始提交免实习申请，请求数据: ${request.toString()}');

    if (await _networkInfo.isConnected) {
      try {
        final response = await _dataSource.submitExemptionApplication(request);
        Logger.info(_tag, '成功提交免实习申请');
        return Right(response);
      } on UnauthorizedException catch (e) {
        Logger.error(_tag, '认证异常: ${e.message}');
        return Left(UnauthorizedFailure(e.message));
      } on ForbiddenException catch (e) {
        Logger.error(_tag, '权限异常: ${e.message}');
        return Left(ForbiddenFailure(e.message));
      } on ServerException catch (e) {
        Logger.error(_tag, '服务器异常: ${e.message}');
        return Left(ServerFailure(e.message));
      } catch (e) {
        Logger.error(_tag, '未知异常: $e');
        return Left(ServerFailure('提交免实习申请失败: $e'));
      }
    } else {
      Logger.warning(_tag, '网络连接不可用');
      return const Left(NetworkFailure('网络连接不可用，请检查网络设置'));
    }
  }
}
