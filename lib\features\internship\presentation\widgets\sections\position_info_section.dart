/// -----
/// position_info_section.dart
///
/// 岗位信息部分组件
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/features/internship/data/models/position_info.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_bloc.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/internship_application_event.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/text_field.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/dropdown_field.dart';
import 'package:flutter_demo/features/internship/presentation/widgets/fields/location_field.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 岗位信息部分组件
class PositionInfoSection extends StatelessWidget {
  /// 岗位信息
  final PositionInfo positionInfo;

  const PositionInfoSection({
    super.key,
    required this.positionInfo,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          child: Text(
            '岗位信息',
            style: TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
        ),

        // 部门
        TextInputField(
          label: '部门',
          value: positionInfo.department,
          placeholder: '请输入部门',
          onChanged: (value) => _updatePositionInfo(context, 'department', value),
        ),

        // 岗位名称
        TextInputField(
          label: '岗位名称',
          value: positionInfo.name,
          placeholder: '请输入岗位名称',
          onChanged: (value) => _updatePositionInfo(context, 'name', value),
        ),

        // 岗位类别
        DropdownField(
          label: '岗位类别',
          value: positionInfo.type,
          items: const [
            '管理人员',
            '技术人员',
            '市场营销人员',
            '财务人员',
            '行政人员'
          ],
          placeholder: '请选择岗位类别',
          onChanged: (value) => _updatePositionInfo(context, 'type', value),
        ),

        // 企业老师
        TextInputField(
          label: '企业老师',
          value: positionInfo.supervisor,
          placeholder: '请输入企业老师',
          onChanged: (value) => _updatePositionInfo(context, 'supervisor', value),
        ),

        // 企业老师联系电话
        TextInputField(
          label: '企业老师联系电话',
          value: positionInfo.supervisorPhone,
          placeholder: '请输入企业老师联系电话',
          keyboardType: TextInputType.phone,
          onChanged: (value) => _updatePositionInfo(context, 'supervisorPhone', value),
        ),

        // 岗位所在省市
        LocationField(
          label: '岗位所在省市',
          value: positionInfo.location,
          placeholder: '请选择省市区',
          onChanged: (value) => context.read<InternshipApplicationBloc>().add(
            SelectLocationEvent('positionLocation', value),
          ),
        ),

        // 详细地址
        TextInputField(
          label: '详细地址',
          value: positionInfo.address,
          placeholder: '请输入详细地址',
          showDivider: false,
          onChanged: (value) => _updatePositionInfo(context, 'address', value),
        ),
      ],
    );
  }

  /// 更新岗位信息
  void _updatePositionInfo(BuildContext context, String field, String value) {
    context.read<InternshipApplicationBloc>().add(
      UpdatePositionInfoEvent(field, value),
    );
  }
}
