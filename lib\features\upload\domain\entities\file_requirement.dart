/// -----
/// file_requirement.dart
/// 
/// 文件要求实体，定义文件上传要求的业务模型
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 文件状态枚举
enum FileStatus {
  /// 未上传
  notUploaded(0),
  /// 已上传
  uploaded(1),
  /// 已审核
  reviewed(2),
  /// 已驳回
  rejected(3);

  const FileStatus(this.value);
  final int value;

  /// 从整数值创建文件状态
  static FileStatus fromValue(int value) {
    switch (value) {
      case 0:
        return FileStatus.notUploaded;
      case 1:
        return FileStatus.uploaded;
      case 2:
        return FileStatus.reviewed;
      case 3:
        return FileStatus.rejected;
      default:
        return FileStatus.notUploaded;
    }
  }

  /// 获取状态文本
  String get statusText {
    switch (this) {
      case FileStatus.notUploaded:
        return '未上传';
      case FileStatus.uploaded:
        return '已上传';
      case FileStatus.reviewed:
        return '已审核';
      case FileStatus.rejected:
        return '已驳回';
    }
  }

  /// 获取状态背景色
  int get statusBackgroundColor {
    switch (this) {
      case FileStatus.notUploaded:
        return 0xFFE5E5E5;
      case FileStatus.uploaded:
        return 0xFFE6F0FF;
      case FileStatus.reviewed:
        return 0xFFE6FFF3;
      case FileStatus.rejected:
        return 0xFFFFE6E6;
    }
  }

  /// 获取状态文字颜色
  int get statusTextColor {
    switch (this) {
      case FileStatus.notUploaded:
        return 0xFFB0B0B0;
      case FileStatus.uploaded:
        return 0xFF3B8BFF;
      case FileStatus.reviewed:
        return 0xFF1AC29A;
      case FileStatus.rejected:
        return 0xFFFF4D4F;
    }
  }
}

/// 文件要求实体
/// 
/// 表示学生需要上传的文件类型和状态
class FileRequirement extends Equatable {
  /// 文件ID
  final String? id;
  
  /// 文件类型名称
  final String fileType;
  
  /// 文件代码
  final int fileCode;
  
  /// 文件状态
  final FileStatus fileStatus;

  const FileRequirement({
    this.id,
    required this.fileType,
    required this.fileCode,
    required this.fileStatus,
  });

  /// 获取文件图标路径
  String get iconPath {
    // 根据文件代码或状态返回不同的图标
    switch (fileCode % 4) {
      case 1:
        return 'assets/images/upload_blue_doc_icon.png';
      case 2:
        return 'assets/images/upload_pink_doc_icon.png';
      case 3:
        return 'assets/images/upload_purple_doc_icon.png';
      case 0:
        return 'assets/images/upload_orange_doc_icon.png';
      default:
        return 'assets/images/upload_blue_doc_icon.png';
    }
  }

  @override
  List<Object?> get props => [id, fileType, fileCode, fileStatus];

  @override
  String toString() {
    return 'FileRequirement(id: $id, fileType: $fileType, fileCode: $fileCode, fileStatus: $fileStatus)';
  }
}
