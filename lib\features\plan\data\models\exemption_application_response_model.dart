/// -----
/// exemption_application_response_model.dart
///
/// 免实习申请响应数据模型
/// 用于解析服务器返回的免实习申请响应数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 免实习申请响应数据模型
///
/// 根据API接口定义的响应数据结构
class ExemptionApplicationResponseModel {
  /// 响应数据（通常是申请记录ID）
  final int data;
  
  /// 响应状态码
  final String resultCode;
  
  /// 响应消息
  final String resultMsg;

  const ExemptionApplicationResponseModel({
    required this.data,
    required this.resultCode,
    required this.resultMsg,
  });

  /// 从JSON创建实例
  factory ExemptionApplicationResponseModel.fromJson(Map<String, dynamic> json) {
    return ExemptionApplicationResponseModel(
      data: json['data'] ?? 0,
      resultCode: json['resultCode'] ?? '',
      resultMsg: json['resultMsg'] ?? '',
    );
  }

  /// 转换为JSON格式
  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'resultCode': resultCode,
      'resultMsg': resultMsg,
    };
  }

  /// 检查响应是否成功
  bool get isSuccess => resultCode == '0' || resultCode == '200';

  /// 创建副本
  ExemptionApplicationResponseModel copyWith({
    int? data,
    String? resultCode,
    String? resultMsg,
  }) {
    return ExemptionApplicationResponseModel(
      data: data ?? this.data,
      resultCode: resultCode ?? this.resultCode,
      resultMsg: resultMsg ?? this.resultMsg,
    );
  }

  @override
  String toString() {
    return 'ExemptionApplicationResponseModel(data: $data, resultCode: $resultCode, resultMsg: $resultMsg)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExemptionApplicationResponseModel &&
        other.data == data &&
        other.resultCode == resultCode &&
        other.resultMsg == resultMsg;
  }

  @override
  int get hashCode {
    return data.hashCode ^ resultCode.hashCode ^ resultMsg.hashCode;
  }
}
