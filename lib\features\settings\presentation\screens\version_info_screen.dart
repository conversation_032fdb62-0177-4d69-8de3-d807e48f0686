import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class VersionInfoScreen extends StatelessWidget {
  const VersionInfoScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '版本说明',
        backgroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildRocketSlogan(),
            const SizedBox(height: 16),
            _buildVersionHistory(),
          ],
        ),
      ),
    );
  }

  // 火箭图标
  Widget _buildRocketSlogan() {
    return Container(
      width: double.infinity,
      // height: 100,

      color: Colors.white,
      child: Column(
        children: [
          <PERSON><PERSON>B<PERSON>(height: 47.h),
          Image.asset(
            'assets/images/setting_update_icon.png',
            width: 134.w,
            height: 134.w,
          ),
          const SizedBox(height: 16),
          _buildSlogan(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  // 标语
  Widget _buildSlogan() {
    return Text(
      '版本不息 优化不止',
      style: TextStyle(
        fontSize: 28.sp,
        fontWeight: FontWeight.w500,
        color: AppTheme.black666,
      ),
    );
  }

  // 版本历史
  Widget _buildVersionHistory() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 25.w),
      child: Column(
        children: [
          _buildVersionItem(
            version: 'V 2.0.0',
            date: '2017-05-01',
            features: [
              '购物满88包邮，48小时退款，30天无忧退货',
              '邀请好友，领走300元',
              '商品标签，一眼Get商品制造商',
              '支持更多优惠券，买买买更优惠',
              '评论显示优化，商品口碑一目了然',
            ],
          ),
          SizedBox(height: 20.h),
          _buildVersionItem(
            version: 'V 1.5.0',
            date: '2017-03-01',
            features: [
              '购物满88包邮，48小时退款，30天无忧退货',
              '邀请好友，领走300元',
              '商品标签，一眼Get商品制造商',
              '支持更多优惠券，买买买更优惠',
              '评论显示优化，商品口碑一目了然',
            ],
          ),
        ],
      ),
    );
  }

  // 版本项
  Widget _buildVersionItem({
    required String version,
    required String date,
    required List<String> features,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(
              left: 25.w,
              right: 25.w,
              top: 25.h,
              bottom: 15.h,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  version,
                  style: TextStyle(
                    fontSize: 30.sp,
                    fontWeight: FontWeight.w500,
                    color: AppTheme.black333,
                  ),
                ),
                Text(
                  date,
                  style: TextStyle(
                    fontSize: 26.sp,
                    color: AppTheme.black999,
                  ),
                ),
              ],
            ),
          ),
          ...features.asMap().entries.map((entry) {
            final index = entry.key + 1;
            final feature = entry.value;
            return _buildFeatureItem(index, feature);
          }).toList(),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  // 特性项
  Widget _buildFeatureItem(int index, String feature) {
    return Padding(
      padding: EdgeInsets.only(
        left: 25.w,
        right: 25.w,
        top: 8.h,
        bottom: 8.h,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$index.',
            style: TextStyle(
              fontSize: 28.sp,
              color: AppTheme.black666,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: 10.w),
          Expanded(
            child: Text(
              feature,
              style: TextStyle(
                fontSize: 28.sp,
                color: AppTheme.black666,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
