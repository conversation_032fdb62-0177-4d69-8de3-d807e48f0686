/// 本地存储接口
///
/// 定义了本地存储的基本操作
abstract class LocalStorage {
  /// 存储字符串
  Future<bool> setString(String key, String value);

  /// 存储布尔值
  Future<bool> setBool(String key, bool value);

  /// 存储整数
  Future<bool> setInt(String key, int value);

  /// 存储双精度浮点数
  Future<bool> setDouble(String key, double value);

  /// 存储字符串列表
  Future<bool> setStringList(String key, List<String> value);

  /// 获取字符串
  String? getString(String key);

  /// 获取布尔值
  bool? getBool(String key);

  /// 获取整数
  int? getInt(String key);

  /// 获取双精度浮点数
  double? getDouble(String key);

  /// 获取字符串列表
  List<String>? getStringList(String key);

  /// 移除指定键的数据
  Future<bool> remove(String key);

  /// 清除所有数据
  Future<bool> clear();

  /// 检查是否包含指定键
  bool containsKey(String key);
}
