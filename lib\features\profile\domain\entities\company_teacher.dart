class CompanyTeacher {
  final String id;
  final String companyName;
  final String name;
  final String gender;
  final String phone;
  final String department;
  final String position;
  final String entryDate;
  final String education;
  final String workExperience;
  final List<String> certificates;

  CompanyTeacher({
    required this.id,
    required this.companyName,
    required this.name,
    required this.gender,
    required this.phone,
    required this.department,
    required this.position,
    required this.entryDate,
    required this.education,
    required this.workExperience,
    this.certificates = const [],
  });

  // 从JSON创建实例
  factory CompanyTeacher.fromJson(Map<String, dynamic> json) {
    return CompanyTeacher(
      id: json['id'],
      companyName: json['companyName'],
      name: json['name'],
      gender: json['gender'],
      phone: json['phone'],
      department: json['department'],
      position: json['position'],
      entryDate: json['entryDate'],
      education: json['education'],
      workExperience: json['workExperience'],
      certificates: List<String>.from(json['certificates'] ?? []),
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'companyName': companyName,
      'name': name,
      'gender': gender,
      'phone': phone,
      'department': department,
      'position': position,
      'entryDate': entryDate,
      'education': education,
      'workExperience': workExperience,
      'certificates': certificates,
    };
  }

  // 复制实例并修改部分属性
  CompanyTeacher copyWith({
    String? id,
    String? companyName,
    String? name,
    String? gender,
    String? phone,
    String? department,
    String? position,
    String? entryDate,
    String? education,
    String? workExperience,
    List<String>? certificates,
  }) {
    return CompanyTeacher(
      id: id ?? this.id,
      companyName: companyName ?? this.companyName,
      name: name ?? this.name,
      gender: gender ?? this.gender,
      phone: phone ?? this.phone,
      department: department ?? this.department,
      position: position ?? this.position,
      entryDate: entryDate ?? this.entryDate,
      education: education ?? this.education,
      workExperience: workExperience ?? this.workExperience,
      certificates: certificates ?? this.certificates,
    );
  }
} 