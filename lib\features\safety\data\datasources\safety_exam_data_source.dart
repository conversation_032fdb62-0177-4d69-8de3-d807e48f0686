/// -----
/// safety_exam_data_source.dart
/// 
/// 安全教育考试数据源
///
/// <AUTHOR>
/// @date 2025-05-23
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/domain/models/exam_record_request.dart';
import 'package:flutter_demo/features/safety/domain/models/exam_detail_response.dart';

/// 安全教育考试数据源接口
///
/// 定义获取安全教育考试题目、保存考试记录和获取考试详情的方法
abstract class SafetyExamDataSource {
  /// 获取安全教育考试题目列表
  ///
  /// [planId] 实习计划ID，可选参数
  Future<List<SafetyExamQuestion>> getExamQuestions({String? planId});

  /// 保存学生考试记录
  ///
  /// [request] 考试记录请求数据
  /// 返回保存结果，成功返回true，失败抛出异常
  Future<bool> saveExamRecord(ExamRecordRequest request);

  /// 获取学生考试详情
  ///
  /// [recordId] 考试记录ID
  /// 返回考试详情数据
  Future<ExamDetailResponse> getExamDetail(String recordId);
}

/// 本地安全教育考试数据源实现（已废弃，使用RemoteSafetyExamDataSource）
///
/// 从本地JSON数据中获取安全教育考试题目
@Deprecated('使用RemoteSafetyExamDataSource替代')
class LocalSafetyExamDataSource implements SafetyExamDataSource {
  @override
  Future<List<SafetyExamQuestion>> getExamQuestions({String? planId}) async {
    // 返回空列表，强制使用远程数据源
    return [];
  }

  @override
  Future<bool> saveExamRecord(ExamRecordRequest request) async {
    // 本地数据源不支持保存，抛出异常
    throw UnsupportedError('本地数据源不支持保存考试记录，请使用RemoteSafetyExamDataSource');
  }

  @override
  Future<ExamDetailResponse> getExamDetail(String recordId) async {
    // 本地数据源不支持获取考试详情，抛出异常
    throw UnsupportedError('本地数据源不支持获取考试详情，请使用RemoteSafetyExamDataSource');
  }
}


