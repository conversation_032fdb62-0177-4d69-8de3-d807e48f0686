/// -----
/// teacher_safety_education_bloc.dart
/// 
/// 教师端安全教育考试BLoC
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/logger.dart';
import '../../../domain/usecases/get_teacher_safety_education_data_usecase.dart';
import 'teacher_safety_education_event.dart';
import 'teacher_safety_education_state.dart';

/// 教师端安全教育考试BLoC
/// 
/// 处理教师端安全教育考试相关的业务逻辑
class TeacherSafetyEducationBloc extends Bloc<TeacherSafetyEducationEvent, TeacherSafetyEducationState> {
  /// 获取教师端安全教育考试数据用例
  final GetTeacherSafetyEducationDataUseCase _getTeacherSafetyEducationDataUseCase;
  
  static const String _tag = 'TeacherSafetyEducationBloc';

  /// 构造函数
  TeacherSafetyEducationBloc({
    required GetTeacherSafetyEducationDataUseCase getTeacherSafetyEducationDataUseCase,
  })  : _getTeacherSafetyEducationDataUseCase = getTeacherSafetyEducationDataUseCase,
        super(const TeacherSafetyEducationInitial()) {
    on<LoadTeacherSafetyEducationDataEvent>(_onLoadTeacherSafetyEducationData);
    on<RefreshTeacherSafetyEducationDataEvent>(_onRefreshTeacherSafetyEducationData);
    on<StudentItemClickedEvent>(_onStudentItemClicked);
  }

  /// 处理加载教师端安全教育考试数据事件
  Future<void> _onLoadTeacherSafetyEducationData(
    LoadTeacherSafetyEducationDataEvent event,
    Emitter<TeacherSafetyEducationState> emit,
  ) async {
    Logger.info(_tag, '开始加载教师端安全教育考试数据，planId: ${event.planId}');
    
    emit(const TeacherSafetyEducationLoading());

    final result = await _getTeacherSafetyEducationDataUseCase(
      GetTeacherSafetyEducationDataParams(planId: event.planId),
    );

    result.fold(
      (failure) {
        Logger.error(_tag, '加载教师端安全教育考试数据失败: ${failure.message}');
        emit(TeacherSafetyEducationError(
          message: failure.message,
          planId: event.planId,
        ));
      },
      (data) {
        Logger.info(_tag, '成功加载教师端安全教育考试数据');
        
        // 检查是否有数据
        if (data.studentList.isEmpty) {
          emit(TeacherSafetyEducationEmpty(planId: event.planId));
        } else {
          emit(TeacherSafetyEducationLoaded(
            data: data,
            planId: event.planId,
          ));
        }
      },
    );
  }

  /// 处理刷新教师端安全教育考试数据事件
  Future<void> _onRefreshTeacherSafetyEducationData(
    RefreshTeacherSafetyEducationDataEvent event,
    Emitter<TeacherSafetyEducationState> emit,
  ) async {
    Logger.info(_tag, '开始刷新教师端安全教育考试数据，planId: ${event.planId}');
    
    // 如果当前状态是已加载状态，则显示刷新中状态
    if (state is TeacherSafetyEducationLoaded) {
      final currentState = state as TeacherSafetyEducationLoaded;
      emit(TeacherSafetyEducationRefreshing(
        previousData: currentState.data,
        planId: event.planId,
      ));
    } else {
      emit(const TeacherSafetyEducationLoading());
    }

    final result = await _getTeacherSafetyEducationDataUseCase(
      GetTeacherSafetyEducationDataParams(planId: event.planId),
    );

    result.fold(
      (failure) {
        Logger.error(_tag, '刷新教师端安全教育考试数据失败: ${failure.message}');
        
        // 如果之前有数据，显示刷新失败状态
        if (state is TeacherSafetyEducationRefreshing) {
          final currentState = state as TeacherSafetyEducationRefreshing;
          emit(TeacherSafetyEducationRefreshError(
            message: failure.message,
            previousData: currentState.previousData,
            planId: event.planId,
          ));
        } else {
          emit(TeacherSafetyEducationError(
            message: failure.message,
            planId: event.planId,
          ));
        }
      },
      (data) {
        Logger.info(_tag, '成功刷新教师端安全教育考试数据');
        
        // 检查是否有数据
        if (data.studentList.isEmpty) {
          emit(TeacherSafetyEducationEmpty(planId: event.planId));
        } else {
          emit(TeacherSafetyEducationLoaded(
            data: data,
            planId: event.planId,
          ));
        }
      },
    );
  }

  /// 处理学生列表项点击事件
  void _onStudentItemClicked(
    StudentItemClickedEvent event,
    Emitter<TeacherSafetyEducationState> emit,
  ) {
    Logger.info(_tag, '学生列表项被点击，studentId: ${event.studentId}, studentName: ${event.studentName}');
    
    // 这里可以添加导航逻辑，或者发出一个导航事件
    // 由于导航通常在UI层处理，这里只记录日志
    // 实际的导航逻辑将在UI层的监听器中处理
  }
}
