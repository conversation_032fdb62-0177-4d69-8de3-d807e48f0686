/// -----
/// custom_text_field.dart
///
/// 通用输入框组件，支持各种输入类型和样式
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomTextField extends StatefulWidget {
  /// 标签文本
  final String? label;

  /// 提示文本
  final String? hintText;

  /// 初始值
  final String? initialValue;

  /// 文本控制器
  final TextEditingController? controller;

  /// 是否为密码输入
  final bool obscureText;

  /// 键盘类型
  final TextInputType keyboardType;

  /// 输入格式化器
  final List<TextInputFormatter>? inputFormatters;

  /// 验证器
  final FormFieldValidator<String>? validator;

  /// 文本变化回调
  final ValueChanged<String>? onChanged;

  /// 最大行数
  final int? maxLines;

  /// 最大长度
  final int? maxLength;

  /// 是否只读
  final bool readOnly;

  /// 后缀图标
  final Widget? suffixIcon;

  /// 前缀图标
  final Widget? prefixIcon;

  /// 内容内边距
  final EdgeInsetsGeometry? contentPadding;

  /// 文本大写方式
  final TextCapitalization textCapitalization;

  /// 文本输入操作
  final TextInputAction? textInputAction;

  /// 点击回调
  final VoidCallback? onTap;

  /// 焦点节点
  final FocusNode? focusNode;

  /// 错误文本
  final String? errorText;

  /// 背景颜色
  final Color? backgroundColor;

  /// 输入框高度
  final double? height;

  /// 边框圆角
  final double? borderRadius;

  /// 文本样式
  final TextStyle? textStyle;

  /// 提示文本样式
  final TextStyle? hintStyle;

  /// 错误文本样式
  final TextStyle? errorStyle;

  /// 标签样式
  final TextStyle? labelStyle;

  /// 是否显示密码切换按钮
  final bool showPasswordToggle;

  /// 是否自动获取焦点
  final bool autofocus;

  const CustomTextField({
    Key? key,
    this.label,
    this.hintText,
    this.initialValue,
    this.controller,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.inputFormatters,
    this.validator,
    this.onChanged,
    this.maxLines = 1,
    this.maxLength,
    this.readOnly = false,
    this.suffixIcon,
    this.prefixIcon,
    this.contentPadding,
    this.textCapitalization = TextCapitalization.none,
    this.textInputAction,
    this.onTap,
    this.focusNode,
    this.errorText,
    this.backgroundColor,
    this.height,
    this.borderRadius,
    this.textStyle,
    this.hintStyle,
    this.errorStyle,
    this.labelStyle,
    this.showPasswordToggle = true,
    this.autofocus = false,
  }) : super(key: key);

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  late FocusNode _focusNode;
  bool _hasFocus = false;
  bool _obscureText = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _obscureText = widget.obscureText;
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _hasFocus = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: widget.labelStyle ?? TextStyle(
              fontSize: 30.sp,
              fontWeight: FontWeight.bold,
              color: AppTheme.black333,
            ),
          ),
          SizedBox(height: 19.h),
        ],
        Container(
          height: widget.height ?? 88.h,
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? Colors.white,
            borderRadius: BorderRadius.circular(widget.borderRadius ?? 10.r),
            border: widget.errorText != null
                ? Border.all(color: Colors.red)
                : (_hasFocus
                    ? Border.all(color: AppTheme.blue2165f6)
                    : null),
          ),
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Row(
            children: [
              if (widget.prefixIcon != null) ...[
                widget.prefixIcon!,
                SizedBox(width: 10.w),
              ],
              Expanded(
                child: TextField(
                  controller: widget.controller,
                  focusNode: _focusNode,
                  obscureText: _obscureText,
                  keyboardType: widget.keyboardType,
                  inputFormatters: widget.inputFormatters,
                  onChanged: widget.onChanged,
                  maxLines: widget.maxLines,
                  maxLength: widget.maxLength,
                  readOnly: widget.readOnly,
                  textCapitalization: widget.textCapitalization,
                  textInputAction: widget.textInputAction,
                  onTap: widget.onTap,
                  autofocus: widget.autofocus,
                  style: widget.textStyle ?? TextStyle(
                    fontSize: 24.sp,
                    color: AppTheme.black333,
                  ),
                  textAlignVertical: TextAlignVertical.center,
                  decoration: InputDecoration(
                    filled: false,
                    hintText: widget.hintText,
                    hintStyle: widget.hintStyle ?? TextStyle(
                      color: AppTheme.black999,
                      fontSize: 24.sp,
                    ),
                    border: InputBorder.none,
                    enabledBorder: InputBorder.none,
                    focusedBorder: InputBorder.none,
                    errorBorder: InputBorder.none,
                    focusedErrorBorder: InputBorder.none,
                    contentPadding: widget.contentPadding ?? EdgeInsets.symmetric(
                      horizontal: 0,
                      vertical: 24.h,
                    ),
                    isDense: true,
                    counterText: '',
                  ),
                ),
              ),
              if (widget.obscureText && widget.showPasswordToggle) ...[
                IconButton(
                  icon: Icon(
                    _obscureText ? Icons.visibility_off : Icons.visibility,
                    color: Colors.grey,
                    size: 22,
                  ),
                  onPressed: () {
                    setState(() {
                      _obscureText = !_obscureText;
                    });
                  },
                ),
              ] else if (widget.suffixIcon != null) ...[
                widget.suffixIcon!,
              ],
            ],
          ),
        ),
        if (widget.errorText != null)
          Padding(
            padding: EdgeInsets.only(top: 8.h, left: 8.w),
            child: Text(
              widget.errorText!,
              style: widget.errorStyle ?? TextStyle(
                color: Colors.red,
                fontSize: 24.sp,
              ),
            ),
          ),
      ],
    );
  }
}