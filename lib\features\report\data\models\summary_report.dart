/// -----
/// summary_report.dart
/// 
/// 总结报告模型，定义总结报告特有的属性
/// 
/// <AUTHOR>
/// @date 2025-06-17
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';

class SummaryReport extends BaseReport {
  final String summary;
  final String achievements;
  final String experience;
  final String problems;
  final String suggestions;

  const SummaryReport({
    required super.id,
    required super.userId,
    required super.userName,
    required super.courseName,
    super.title,
    required super.createdAt,
    required super.status,
    super.isLate = false,
    super.rating,
    super.teacherComment,
    super.teacherName,
    super.commentTime,
    required this.summary,
    required this.achievements,
    required this.experience,
    required this.problems,
    required this.suggestions,
    super.infoTitle,
    super.contentTitle
  });

  @override
  String get content => summary;

  @override
  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'courseName': courseName,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'status': status.toString(),
      'isLate': isLate,
      'rating': rating,
      'teacherComment': teacherComment,
      'teacherName': teacherName,
      'commentTime': commentTime?.toIso8601String(),
      'summary': summary,
      'achievements': achievements,
      'experience': experience,
      'problems': problems,
      'suggestions': suggestions,
      'infoTitle': infoTitle,
      'contentTitle': contentTitle,
    };
  }
} 