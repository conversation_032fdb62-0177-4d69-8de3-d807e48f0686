/// -----
/// internship_application_response_model.dart
///
/// 实习申请API响应数据模型
/// 用于处理服务器返回的实习申请响应数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 实习申请响应模型
///
/// 根据API文档定义的响应格式
/// 包含响应数据、结果代码和结果消息
class InternshipApplicationResponseModel {
  /// 响应数据（通常为申请ID或其他标识）
  final int data;
  
  /// 结果代码
  final String resultCode;
  
  /// 结果消息
  final String resultMsg;

  /// 构造函数
  const InternshipApplicationResponseModel({
    required this.data,
    required this.resultCode,
    required this.resultMsg,
  });

  /// 从JSON Map创建响应模型实例
  ///
  /// 用于从服务器响应中反序列化数据
  factory InternshipApplicationResponseModel.fromJson(Map<String, dynamic> json) {
    return InternshipApplicationResponseModel(
      data: json['data'] ?? 0,
      resultCode: json['resultCode'] ?? '',
      resultMsg: json['resultMsg'] ?? '',
    );
  }

  /// 转换为JSON Map
  ///
  /// 用于序列化响应数据
  Map<String, dynamic> toJson() {
    return {
      'data': data,
      'resultCode': resultCode,
      'resultMsg': resultMsg,
    };
  }

  /// 检查响应是否成功
  ///
  /// 根据项目约定，resultCode为"200"或"0"表示成功
  bool get isSuccess {
    return resultCode == '200' || resultCode == '0';
  }

  /// 获取错误消息
  ///
  /// 如果请求失败，返回错误消息
  String? get errorMessage {
    return isSuccess ? null : resultMsg;
  }
}
