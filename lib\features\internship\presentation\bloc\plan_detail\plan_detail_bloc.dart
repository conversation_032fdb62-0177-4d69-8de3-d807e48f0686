/// -----
/// plan_detail_bloc.dart
/// 
/// 实习计划详情页面BLoC
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../../core/utils/logger.dart';
import '../../../domain/usecases/get_internship_plan_detail_usecase.dart';
import 'plan_detail_event.dart';
import 'plan_detail_state.dart';

/// 实习计划详情页面BLoC
/// 
/// 管理实习计划详情页面的状态和业务逻辑
class PlanDetailBloc extends Bloc<PlanDetailEvent, PlanDetailState> {
  /// 日志标签
  static const String _tag = 'PlanDetailBloc';

  /// 获取实习计划详情用例
  final GetInternshipPlanDetailUseCase _getInternshipPlanDetailUseCase;

  /// 构造函数
  PlanDetailBloc(this._getInternshipPlanDetailUseCase) : super(PlanDetailInitialState()) {
    on<LoadPlanDetailEvent>(_onLoadPlanDetail);
    on<RefreshPlanDetailEvent>(_onRefreshPlanDetail);
  }

  /// 处理加载实习计划详情事件
  Future<void> _onLoadPlanDetail(
    LoadPlanDetailEvent event,
    Emitter<PlanDetailState> emit,
  ) async {
    Logger.info(_tag, '开始加载实习计划详情，ID: ${event.planId}');
    
    emit(PlanDetailLoadingState());

    final result = await _getInternshipPlanDetailUseCase(
      InternshipPlanDetailParams(id: event.planId),
    );

    result.fold(
      (failure) {
        Logger.error(_tag, '加载实习计划详情失败: ${failure.message}');
        emit(PlanDetailErrorState(message: failure.message));
      },
      (plan) {
        Logger.info(_tag, '成功加载实习计划详情: ${plan.planName}');
        emit(PlanDetailLoadedState(plan: plan));
      },
    );
  }

  /// 处理刷新实习计划详情事件
  Future<void> _onRefreshPlanDetail(
    RefreshPlanDetailEvent event,
    Emitter<PlanDetailState> emit,
  ) async {
    Logger.info(_tag, '开始刷新实习计划详情，ID: ${event.planId}');

    // 保存当前状态中的计划数据（如果有的话）
    final currentState = state;
    final previousPlan = currentState is PlanDetailLoadedState ? currentState.plan : null;

    final result = await _getInternshipPlanDetailUseCase(
      InternshipPlanDetailParams(id: event.planId),
    );

    result.fold(
      (failure) {
        Logger.error(_tag, '刷新实习计划详情失败: ${failure.message}');
        emit(PlanDetailRefreshErrorState(
          message: failure.message,
          previousPlan: previousPlan,
        ));
      },
      (plan) {
        Logger.info(_tag, '成功刷新实习计划详情: ${plan.planName}');
        emit(PlanDetailRefreshSuccessState(plan: plan));
      },
    );
  }
}
