import 'package:flutter/material.dart';

/// 沉浸式应用栏组件
///
/// 专为沉浸式状态栏设计，允许背景图片延伸到状态栏区域
/// 不会添加任何背景色或阴影，完全透明
/// 返回按钮样式与CustomAppBar保持一致
class ImmersiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// 标题
  final String title;

  /// 标题颜色
  final Color titleColor;

  /// 标题字体大小
  final double titleFontSize;

  /// 标题字体粗细
  final FontWeight titleFontWeight;

  /// 返回按钮图标颜色
  final Color? backIconColor;

  /// 返回按钮图标大小
  final double backIconSize;

  /// 返回按钮点击回调
  final VoidCallback? onBackPressed;

  /// 自定义左侧组件
  final Widget? leading;

  /// 右侧操作按钮列表
  final List<Widget>? actions;

  /// 应用栏高度
  final double height;

  /// 是否显示返回按钮
  final bool showBackButton;

  /// 标题是否居中
  final bool centerTitle;

  const ImmersiveAppBar({
    Key? key,
    this.title = '',
    this.titleColor = Colors.black,
    this.titleFontSize = 18,
    this.titleFontWeight = FontWeight.bold,
    this.backIconColor,
    this.backIconSize = 20,
    this.onBackPressed,
    this.leading,
    this.actions,
    this.height = kToolbarHeight,
    this.showBackButton = true,
    this.centerTitle = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 获取状态栏高度
    final statusBarHeight = MediaQuery.of(context).padding.top;

    return Container(
      // 不添加任何背景色或装饰
      height: height + statusBarHeight,
      padding: EdgeInsets.only(top: statusBarHeight),
      child: Row(
        children: [
          // 左侧返回按钮
          if (showBackButton)
            leading ??
                IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    size: backIconSize,
                    color: backIconColor ?? Colors.black,
                  ),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                ),
          if (!showBackButton && leading != null) leading!,

          // 标题
          if (title.isNotEmpty)
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  title,
                  style: TextStyle(
                    color: titleColor,
                    fontSize: titleFontSize,
                    fontWeight: titleFontWeight,
                  ),
                  textAlign: centerTitle ? TextAlign.center : TextAlign.start,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),

          // 右侧操作按钮
          if (actions != null) ...actions!,
          if (actions == null || actions!.isEmpty)
            const SizedBox(width: 48), // 保持对称性
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
