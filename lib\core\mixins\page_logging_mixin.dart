/// -----------------------------------------------------------------------------
/// page_logging_mixin.dart
///
/// 页面日志记录Mixin，用于自动打印页面进入和离开的日志
/// 方便开发调试时定位当前页面
///
/// <AUTHOR>
/// @date 2025-06-21
/// @version 1.0
/// @copyright Copyright ©2025 亿硕教育
/// -----------------------------------------------------------------------------

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// 页面日志记录Mixin
/// 
/// 使用方法：
/// ```dart
/// class MyPageState extends State<MyPage> with PageLoggingMixin {
///   @override
///   void initState() {
///     super.initState();
///     logPageEntry(); // 调用这个方法来打印进入页面的日志
///   }
/// }
/// ```
mixin PageLoggingMixin<T extends StatefulWidget> on State<T> {
  /// 是否启用页面日志记录
  /// 可以通过环境变量或配置文件控制
  static const bool _isLoggingEnabled = kDebugMode; // 只在Debug模式下启用

  /// 页面进入时间
  DateTime? _pageEntryTime;

  /// 记录页面进入日志
  void logPageEntry() {
    if (!_isLoggingEnabled) return;
    
    _pageEntryTime = DateTime.now();
    final className = runtimeType.toString();
    final widgetClassName = widget.runtimeType.toString();
    
    debugPrint('📱 [PAGE_ENTRY] 进入页面: $widgetClassName (State: $className)');
    debugPrint('📱 [PAGE_ENTRY] 时间: ${_formatTime(_pageEntryTime!)}');
  }

  /// 记录页面离开日志
  void logPageExit() {
    if (!_isLoggingEnabled) return;
    
    final exitTime = DateTime.now();
    final className = runtimeType.toString();
    final widgetClassName = widget.runtimeType.toString();
    
    debugPrint('📱 [PAGE_EXIT] 离开页面: $widgetClassName (State: $className)');
    debugPrint('📱 [PAGE_EXIT] 时间: ${_formatTime(exitTime)}');
    
    if (_pageEntryTime != null) {
      final duration = exitTime.difference(_pageEntryTime!);
      debugPrint('📱 [PAGE_EXIT] 停留时长: ${_formatDuration(duration)}');
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
           '${time.minute.toString().padLeft(2, '0')}:'
           '${time.second.toString().padLeft(2, '0')}.'
           '${time.millisecond.toString().padLeft(3, '0')}';
  }

  /// 格式化持续时间
  String _formatDuration(Duration duration) {
    if (duration.inMinutes > 0) {
      return '${duration.inMinutes}分${duration.inSeconds % 60}秒';
    } else if (duration.inSeconds > 0) {
      return '${duration.inSeconds}秒${duration.inMilliseconds % 1000}毫秒';
    } else {
      return '${duration.inMilliseconds}毫秒';
    }
  }

  /// 重写dispose方法，自动记录页面离开日志
  @override
  void dispose() {
    logPageExit();
    super.dispose();
  }
}

/// 扩展版本的页面日志记录Mixin
/// 提供更多的日志记录功能
mixin AdvancedPageLoggingMixin<T extends StatefulWidget> on State<T> {
  /// 是否启用页面日志记录
  static const bool _isLoggingEnabled = kDebugMode;

  /// 页面进入时间
  DateTime? _pageEntryTime;

  /// 页面状态变化计数
  int _buildCount = 0;

  /// 记录页面进入日志（带参数信息）
  void logPageEntry({Map<String, dynamic>? params}) {
    if (!_isLoggingEnabled) return;
    
    _pageEntryTime = DateTime.now();
    final className = runtimeType.toString();
    final widgetClassName = widget.runtimeType.toString();
    
    debugPrint('🚀 [PAGE_ENTRY] ==========================================');
    debugPrint('🚀 [PAGE_ENTRY] 页面: $widgetClassName');
    debugPrint('🚀 [PAGE_ENTRY] State类: $className');
    debugPrint('🚀 [PAGE_ENTRY] 时间: ${_formatTime(_pageEntryTime!)}');
    
    if (params != null && params.isNotEmpty) {
      debugPrint('🚀 [PAGE_ENTRY] 参数: $params');
    }
    
    debugPrint('🚀 [PAGE_ENTRY] ==========================================');
  }

  /// 记录页面离开日志
  void logPageExit() {
    if (!_isLoggingEnabled) return;
    
    final exitTime = DateTime.now();
    final className = runtimeType.toString();
    final widgetClassName = widget.runtimeType.toString();
    
    debugPrint('🏁 [PAGE_EXIT] ==========================================');
    debugPrint('🏁 [PAGE_EXIT] 页面: $widgetClassName');
    debugPrint('🏁 [PAGE_EXIT] State类: $className');
    debugPrint('🏁 [PAGE_EXIT] 时间: ${_formatTime(exitTime)}');
    debugPrint('🏁 [PAGE_EXIT] 总构建次数: $_buildCount');
    
    if (_pageEntryTime != null) {
      final duration = exitTime.difference(_pageEntryTime!);
      debugPrint('🏁 [PAGE_EXIT] 停留时长: ${_formatDuration(duration)}');
    }
    
    debugPrint('🏁 [PAGE_EXIT] ==========================================');
  }

  /// 记录构建日志
  void logBuild() {
    if (!_isLoggingEnabled) return;
    
    _buildCount++;
    final widgetClassName = widget.runtimeType.toString();
    debugPrint('🔄 [PAGE_BUILD] $widgetClassName - 第$_buildCount次构建');
  }

  /// 记录自定义事件
  void logEvent(String event, {Map<String, dynamic>? data}) {
    if (!_isLoggingEnabled) return;
    
    final widgetClassName = widget.runtimeType.toString();
    debugPrint('⚡ [PAGE_EVENT] $widgetClassName - $event');
    
    if (data != null && data.isNotEmpty) {
      debugPrint('⚡ [PAGE_EVENT] 数据: $data');
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:'
           '${time.minute.toString().padLeft(2, '0')}:'
           '${time.second.toString().padLeft(2, '0')}.'
           '${time.millisecond.toString().padLeft(3, '0')}';
  }

  /// 格式化持续时间
  String _formatDuration(Duration duration) {
    if (duration.inMinutes > 0) {
      return '${duration.inMinutes}分${duration.inSeconds % 60}秒';
    } else if (duration.inSeconds > 0) {
      return '${duration.inSeconds}秒${duration.inMilliseconds % 1000}毫秒';
    } else {
      return '${duration.inMilliseconds}毫秒';
    }
  }

  /// 重写dispose方法，自动记录页面离开日志
  @override
  void dispose() {
    logPageExit();
    super.dispose();
  }
}
