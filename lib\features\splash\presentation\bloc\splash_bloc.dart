import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import '../../../../core/constants/constants.dart';
import '../../../../core/storage/local_storage.dart';
import '../../../auth/domain/usecases/get_current_user_usecase.dart';
import 'splash_event.dart';
import 'splash_state.dart';

/// 启动页 BLoC
///
/// 处理启动页相关的业务逻辑
class SplashBloc extends Bloc<SplashEvent, SplashState> {
  final GetCurrentUserUseCase _getCurrentUserUseCase;
  final LocalStorage _localStorage;

  SplashBloc({
    required GetCurrentUserUseCase getCurrentUserUseCase,
    required LocalStorage localStorage,
  })  : _getCurrentUserUseCase = getCurrentUserUseCase,
        _localStorage = localStorage,
        super(SplashInitialState()) {
    on<InitializeAppEvent>(_onInitializeApp);
  }

  /// 处理初始化应用事件
  /// 1. 显示启动页
  /// 2. 检查认证状态
  /// 3. 根据认证状态导航到相应页面
  Future<void> _onInitializeApp(
    InitializeAppEvent event,
    Emitter<SplashState> emit,
  ) async {
    emit(AppInitializingState());

    try {
      // 模拟初始化过程（3秒）
      await Future.delayed(const Duration(seconds: 2));
      
      // 检查认证状态
      await _checkAuthStatus(emit);
    } catch (e) {
      emit(InitializationFailureState(e.toString()));
    }
  }

  /// 检查认证状态
  /// 1. 检查 token 是否存在
  /// 2. 检查用户类型是否存在
  /// 3. 检查用户信息是否存在
  Future<void> _checkAuthStatus(Emitter<SplashState> emit) async {
    try {
      // 首先检查token是否存在
      final token = _localStorage.getString(AppConstants.tokenKey);
      Logger.debug('_checkAuthStatus','token: $token');
      if (token == null || token.isEmpty) {
        emit(UnauthenticatedState());
        return;
      }

      // 检查用户是否完成了角色认证
      final userType = _localStorage.getString(AppConstants.userTypeKey);
      Logger.debug('_checkAuthStatus','userType: $userType');
      // 如果用户类型不存在或小于0（未认证），则导航到登录页面
      if (userType == null) {
        emit(UnauthenticatedState());
        return;
      }

      // 然后检查用户信息是否存在
      final result = await _getCurrentUserUseCase();
      Logger.debug('_checkAuthStatus','result: $result');
      return result.fold(
        (failure) => emit(UnauthenticatedState()),
        (user) {
          if (user != null) {
            emit(AuthenticatedState(userType: userType));
          } else {
            emit(UnauthenticatedState());
          }
        },
      );
    } on Exception {
      emit(UnauthenticatedState());
    }
  }
}
