/// -----
/// file_picker_service.dart
///
/// 文件选择服务，处理图片、文档等文件的选择
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_demo/core/services/file_upload_service.dart';

class FilePickerService {
  final ImagePicker _imagePicker = ImagePicker();

  /// 从相册选择图片
  Future<FilePickerResult?> pickImageFromGallery() async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        return _createFilePickerResult(pickedFile.path, pickedFile.name);
      }
      return null;
    } catch (e) {
      throw Exception('选择图片失败: $e');
    }
  }

  /// 拍照
  Future<FilePickerResult?> pickImageFromCamera() async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        return _createFilePickerResult(pickedFile.path, pickedFile.name);
      }
      return null;
    } catch (e) {
      throw Exception('拍照失败: $e');
    }
  }

  /// 选择文档
  Future<FilePickerResult?> pickDocument() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: [
          'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 
          'txt', 'rtf', 'zip', 'rar', '7z'
        ],
        allowMultiple: false,
      );

      return result;
    } catch (e) {
      throw Exception('选择文档失败: $e');
    }
  }

  /// 选择任意文件
  Future<FilePickerResult?> pickAnyFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        final file = result.files.single;
        
        // 检查文件类型是否支持
        if (!FileUploadService.isSupportedFileType(file.name)) {
          throw Exception('不支持的文件类型');
        }
        
        return result;
      }
      return null;
    } catch (e) {
      throw Exception('选择文件失败: $e');
    }
  }

  /// 创建FilePickerResult对象
  FilePickerResult _createFilePickerResult(String path, String name) {
    final file = File(path);
    final size = file.lengthSync();
    
    return FilePickerResult([
      PlatformFile(
        path: path,
        name: name,
        size: size,
        bytes: null,
      ),
    ]);
  }

  /// 验证文件
  Future<ValidationResult> validateFile(String filePath, String fileName) async {
    try {
      final file = File(filePath);
      
      // 检查文件是否存在
      if (!await file.exists()) {
        return ValidationResult(
          isValid: false,
          error: '文件不存在',
        );
      }

      // 检查文件大小（限制10MB）
      final fileSize = await file.length();
      if (fileSize > 10 * 1024 * 1024) {
        return ValidationResult(
          isValid: false,
          error: '文件大小不能超过10MB',
        );
      }

      // 检查文件类型
      if (!FileUploadService.isSupportedFileType(fileName)) {
        return ValidationResult(
          isValid: false,
          error: '不支持的文件类型',
        );
      }

      return ValidationResult(
        isValid: true,
        fileSize: FileUploadService.formatFileSize(fileSize),
      );
    } catch (e) {
      return ValidationResult(
        isValid: false,
        error: '文件验证失败: $e',
      );
    }
  }

  /// 获取文件信息
  Future<FileInfo?> getFileInfo(String filePath) async {
    try {
      final file = File(filePath);
      
      if (!await file.exists()) {
        return null;
      }

      final stat = await file.stat();
      final fileName = file.path.split('/').last;
      
      return FileInfo(
        path: filePath,
        name: fileName,
        size: stat.size,
        formattedSize: FileUploadService.formatFileSize(stat.size),
        lastModified: stat.modified,
        type: _getFileType(fileName),
      );
    } catch (e) {
      return null;
    }
  }

  /// 获取文件类型
  String _getFileType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return 'image';
    } else if (['pdf'].contains(extension)) {
      return 'pdf';
    } else if (['doc', 'docx'].contains(extension)) {
      return 'word';
    } else if (['xls', 'xlsx'].contains(extension)) {
      return 'excel';
    } else if (['ppt', 'pptx'].contains(extension)) {
      return 'powerpoint';
    } else if (['txt', 'rtf'].contains(extension)) {
      return 'text';
    } else if (['zip', 'rar', '7z'].contains(extension)) {
      return 'archive';
    } else {
      return 'unknown';
    }
  }
}

/// 文件验证结果
class ValidationResult {
  final bool isValid;
  final String? error;
  final String? fileSize;

  ValidationResult({
    required this.isValid,
    this.error,
    this.fileSize,
  });
}

/// 文件信息
class FileInfo {
  final String path;
  final String name;
  final int size;
  final String formattedSize;
  final DateTime lastModified;
  final String type;

  FileInfo({
    required this.path,
    required this.name,
    required this.size,
    required this.formattedSize,
    required this.lastModified,
    required this.type,
  });
}
