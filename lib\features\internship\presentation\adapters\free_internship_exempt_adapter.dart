/// -----
/// free_internship_exempt_adapter.dart
///
/// 免实习申请数据适配器
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/approval_list_item.dart';
import '../../domain/entities/free_internship_exempt.dart';

/// 免实习申请数据适配器
///
/// 将免实习申请实体转换为ApprovalItemData接口
class FreeInternshipExemptAdapter implements ApprovalItemData {
  FreeInternshipExemptAdapter(this._exempt) {
    Logger.debug('FreeInternshipExemptAdapter', '创建适配器实例 - 学生: ${_exempt.studentName}, 状态: ${_exempt.statusText}');
  }

  final FreeInternshipExempt _exempt;

  @override
  String get id => _exempt.id.toString();

  @override
  String get studentName => _exempt.studentName;

  @override
  String get studentAvatar => _exempt.avatar;

  @override
  String get status => _exempt.statusText;

  @override
  String get submitTime => _formatTimestamp(_exempt.createTime);

  /// 获取原始实体数据
  FreeInternshipExempt get exempt => _exempt;

  /// 格式化时间戳
  String _formatTimestamp(int timestamp) {
    if (timestamp == 0) {
      return '';
    }

    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
