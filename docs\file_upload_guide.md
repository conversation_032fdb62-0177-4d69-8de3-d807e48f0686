# 文件上传功能使用指南

## 概述

本文档介绍了亿硕教育Flutter项目中的文件上传功能实现，包括文件选择、上传进度显示、错误处理等完整流程。

## 功能特性

### ✅ 已实现功能

1. **文件选择**
   - 支持从相册选择图片
   - 支持拍照
   - 支持选择文档（PDF、Word、Excel、PowerPoint等）
   - 文件类型验证
   - 文件大小限制（10MB）

2. **UI交互**
   - 美观的文件上传界面
   - 上传进度显示
   - 文件列表展示
   - 删除文件功能
   - 错误提示

3. **权限处理**
   - Android权限配置
   - 运行时权限检查（预留接口）

4. **状态管理**
   - 使用BLoC进行状态管理
   - 完整的事件和状态定义
   - 错误处理和用户反馈

## 技术架构

### 核心组件

1. **AttachmentUploader** - 文件上传UI组件
   - 位置：`lib/features/report/presentation/widgets/attachment_uploader.dart`
   - 功能：文件选择界面、上传进度显示、文件列表管理

2. **ReportWriteBloc** - 状态管理
   - 位置：`lib/features/report/presentation/bloc/write/`
   - 功能：处理文件上传事件、管理上传状态

3. **FileUploadService** - 文件上传服务
   - 位置：`lib/core/services/file_upload_service.dart`
   - 功能：实际的文件上传逻辑、进度回调

4. **FilePickerService** - 文件选择服务
   - 位置：`lib/core/services/file_picker_service.dart`
   - 功能：文件选择、验证、信息获取

### 权限配置

已在 `android/app/src/main/AndroidManifest.xml` 中添加必要权限：

```xml
<uses-permission android:name="android.permission.CAMERA"/>
<uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
<uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
<uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
```

## 使用方法

### 在页面中使用AttachmentUploader

```dart
AttachmentUploader(
  attachments: state.attachments,
  onFileSelected: (filePath, fileName, fileSize) {
    // 处理文件选择
    context.read<ReportWriteBloc>().add(AddAttachmentEvent(
      filePath: filePath,
      fileName: fileName,
      fileSize: fileSize,
    ));
  },
  onRemoveFile: (index) {
    // 处理文件删除
    context.read<ReportWriteBloc>().add(RemoveAttachmentEvent(index));
  },
  isUploading: state is ReportWriteUploadingAttachment,
  uploadProgress: state is ReportWriteUploadingAttachment 
      ? state.uploadProgress 
      : 0.0,
  uploadingFileName: state is ReportWriteUploadingAttachment 
      ? state.uploadingFileName 
      : null,
  maxFiles: 1, // 限制文件数量
)
```

### BLoC事件处理

```dart
// 添加文件
context.read<ReportWriteBloc>().add(AddAttachmentEvent(
  filePath: '/path/to/file',
  fileName: 'document.pdf',
  fileSize: '2.5MB',
));

// 删除文件
context.read<ReportWriteBloc>().add(RemoveAttachmentEvent(0));

// 上传文件
context.read<ReportWriteBloc>().add(UploadAttachmentEvent(
  filePath: '/path/to/file',
  fileName: 'document.pdf',
));
```

## 支持的文件类型

### 图片格式
- JPG/JPEG
- PNG
- GIF
- BMP
- WebP

### 文档格式
- PDF
- Word (DOC, DOCX)
- Excel (XLS, XLSX)
- PowerPoint (PPT, PPTX)
- 文本文件 (TXT, RTF)

### 压缩格式
- ZIP
- RAR
- 7Z

## 限制说明

1. **文件大小限制**：单个文件最大10MB
2. **文件数量限制**：每个报告最多上传1个附件
3. **文件类型限制**：仅支持上述列出的文件格式

## 错误处理

系统会在以下情况显示错误提示：

1. 文件大小超过限制
2. 文件类型不支持
3. 网络连接失败
4. 服务器错误
5. 权限不足

## 开发注意事项

### 待完善功能

1. **权限处理**
   - 需要添加 `permission_handler` 依赖
   - 实现运行时权限请求逻辑

2. **实际上传**
   - 当前为模拟上传，需要连接真实API
   - 需要实现文件上传到服务器的逻辑

3. **缓存管理**
   - 实现文件缓存清理
   - 添加上传失败重试机制

### 扩展建议

1. **多文件上传**：修改maxFiles参数支持多文件
2. **拖拽上传**：添加拖拽文件上传功能
3. **预览功能**：添加图片和文档预览
4. **云存储**：集成云存储服务（如阿里云OSS）

## API接口

### 上传接口
```
POST /upload/file
Content-Type: multipart/form-data

参数：
- file: 文件数据
- type: 附件类型 (report_attachment)

响应：
{
  "resultCode": 200,
  "resultMsg": "上传成功",
  "data": {
    "fileUrl": "https://example.com/uploads/file.pdf",
    "fileName": "file.pdf",
    "fileSize": "2.5MB"
  }
}
```

### 删除接口
```
POST /upload/delete

参数：
{
  "fileUrl": "https://example.com/uploads/file.pdf"
}

响应：
{
  "resultCode": 200,
  "resultMsg": "删除成功"
}
```

## 测试建议

1. **功能测试**
   - 测试各种文件类型的选择和上传
   - 测试文件大小限制
   - 测试网络异常情况

2. **UI测试**
   - 测试上传进度显示
   - 测试错误提示
   - 测试文件列表展示

3. **性能测试**
   - 测试大文件上传性能
   - 测试内存使用情况
   - 测试并发上传

## 总结

文件上传功能已基本实现，包括完整的UI界面、状态管理和错误处理。主要待完善的是权限处理和实际的服务器上传逻辑。整体架构遵循Clean Architecture原则，代码结构清晰，易于维护和扩展。
