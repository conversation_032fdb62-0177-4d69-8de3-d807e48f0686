/// -----
/// save_exam_record_usecase.dart
/// 
/// 保存考试记录用例
///
/// <AUTHOR>
/// @date 2025-06-19
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/features/safety/domain/models/exam_record_request.dart';
import 'package:flutter_demo/features/safety/domain/repositories/safety_exam_repository.dart';

/// 保存考试记录用例
///
/// 封装保存学生考试记录的业务逻辑
class SaveExamRecordUseCase {
  /// 安全教育考试仓库
  final SafetyExamRepository repository;

  /// 构造函数
  const SaveExamRecordUseCase({
    required this.repository,
  });

  /// 执行用例，保存学生考试记录
  ///
  /// [request] 考试记录请求数据
  /// 返回保存结果，成功返回true，失败抛出异常
  Future<bool> call(ExamRecordRequest request) async {
    return await repository.saveExamRecord(request);
  }
}
