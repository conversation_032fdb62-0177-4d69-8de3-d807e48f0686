/// -----
/// internship_to_job_screen.dart
/// 
/// 实习转就业申请界面，用于学生提交实习转就业申请
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/features/internship/presentation/screens/internship_to_job_detail_screen.dart';
import 'package:flutter_demo/core/widgets/approval_tab_bar.dart';
import 'package:flutter_demo/core/constants/constants.dart';

class InternshipToJobScreen extends StatefulWidget {
  const InternshipToJobScreen({Key? key}) : super(key: key);

  @override
  State<InternshipToJobScreen> createState() => _InternshipToJobScreenState();
}

class _InternshipToJobScreenState extends State<InternshipToJobScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _tabs = ['待审批 (4)', '已审批'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: '实习转就业申请',
        centerTitle: true,
        showBackButton: true,
      ),
      body: Column(
        children: [
          // 年级下拉选择
          _buildGradeSelector(),
          // 自定义标签栏
          ApprovalTabBar(
            controller: _tabController,
            pendingCount: 4, // 示例
            pendingText: '待审批',
            approvedText: '已审批',
          ),
          // 申请列表
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildApplicationList(isPending: true),
                _buildApplicationList(isPending: false),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showCreateApplicationDialog(context);
        },
        backgroundColor: AppTheme.primaryColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  // 年级选择器
  Widget _buildGradeSelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          const Text(
            '2021级',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(width: 4),
          Icon(
            Icons.arrow_drop_down,
            color: Colors.grey[600],
          ),
        ],
      ),
    );
  }

  // 申请列表
  Widget _buildApplicationList({required bool isPending}) {
    final List<Map<String, String>> dataList = [
      {
        'name': '刘备',
        'avatar': AppConstants.avatar1,
        'employmentType': '普通劳动合同就业',
        'company': '深圳市腾讯计算机系统有限责任公司',
        'city': '湖北武汉市武昌区',
        'department': '技术部',
        'position': 'java开发工程师',
        'status': isPending ? '待审批' : '已审批',
        'time': '2025.03.10 14:30',
      },
      {
        'name': '关羽',
        'avatar': AppConstants.avatar2,
        'employmentType': '普通劳动合同就业',
        'company': '阿里巴巴（中国）有限公司',
        'city': '上海市浦东新区',
        'department': '产品部',
        'position': '产品经理',
        'status': isPending ? '待审批' : '已审批',
        'time': '2025.03.12 09:20',
      },
    ];
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: dataList.length,
      itemBuilder: (context, index) {
        final data = dataList[index];
        return _buildApplicationListItem(data);
      },
    );
  }

  Widget _buildApplicationListItem(Map<String, String> data) {
    final status = data['status'] ?? '';
    Color statusColor;
    switch (status) {
      case '待审批':
        statusColor = Colors.grey;
        break;
      case '已审批':
        statusColor = Colors.green;
        break;
      case '已驳回':
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }
    final avatarUrl = data['avatar'] ?? '';
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey[300],
                  ),
                  child: ClipOval(
                    child: avatarUrl.isNotEmpty
                        ? Image.network(
                            avatarUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                Image.asset(
                                  'assets/images/default_avatar.png',
                                  fit: BoxFit.cover,
                                ),
                          )
                        : Image.asset(
                            'assets/images/default_avatar.png',
                            fit: BoxFit.cover,
                          ),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  data['name'] ?? '',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Text(
                  status,
                  style: TextStyle(
                    fontSize: 14,
                    color: statusColor,
                  ),
                ),
              ],
            ),
            const Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
            ),
            _buildInfoRow('就业类别', data['employmentType'] ?? ''),
            const SizedBox(height: 8),
            _buildInfoRow('就业单位', data['company'] ?? ''),
            const SizedBox(height: 8),
            _buildInfoRow('就业省市', data['city'] ?? ''),
            const SizedBox(height: 8),
            _buildInfoRow('部门/科室', data['department'] ?? ''),
            const SizedBox(height: 8),
            _buildInfoRow('就业岗位', data['position'] ?? ''),
            const SizedBox(height: 16),
            Row(
              children: [
                Text(
                  data['time'] ?? '',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                const Spacer(),
                InkWell(
                  onTap: () {
                    // TODO: 跳转到详情页面
                  },
                  child: const Row(
                    children: [
                      Text(
                        '查看',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        size: 12,
                        color: AppTheme.primaryColor,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  // 显示创建申请对话框
  void _showCreateApplicationDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建实习转就业申请'),
        content: const Text('确定要创建新的实习转就业申请吗？'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 模拟创建成功
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('申请已创建，等待审批'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
} 