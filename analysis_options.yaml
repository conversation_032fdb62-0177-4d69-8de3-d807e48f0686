# 增强的Flutter项目分析配置
# 此文件配置静态分析器，用于检查Dart代码中的错误、警告和lint规则

# 包含Flutter官方推荐的lint规则集
include: package:flutter_lints/flutter.yaml

# 自定义分析器选项
analyzer:
  exclude:
    - "**/*.g.dart"
    - "**/*.freezed.dart"
    - "lib/generated/**"
  errors:
    # 将一些警告提升为错误，以确保代码质量
    invalid_annotation_target: ignore
    missing_required_param: error
    missing_return: error
    must_be_immutable: error
    sort_unnamed_constructors_first: ignore
    invalid_use_of_protected_member: error
    always_use_package_imports: error
    avoid_returning_null_for_future: error
    avoid_slow_async_io: error
    cancel_subscriptions: error
    close_sinks: error
    comment_references: error
    prefer_relative_imports: ignore
    directives_ordering: ignore
    sort_pub_dependencies: ignore

# 自定义lint规则
linter:
  rules:
    # 代码风格
    always_declare_return_types: true
    always_put_control_body_on_new_line: true
    always_put_required_named_parameters_first: true
    always_require_non_null_named_parameters: true
    annotate_overrides: true
    avoid_bool_literals_in_conditional_expressions: true
    avoid_catches_without_on_clauses: true
    avoid_catching_errors: true
    avoid_classes_with_only_static_members: false  # 允许工具类
    avoid_empty_else: true
    avoid_equals_and_hash_code_on_mutable_classes: true
    avoid_escaping_inner_quotes: true
    avoid_field_initializers_in_const_classes: true
    avoid_function_literals_in_foreach_calls: false  # 允许在forEach中使用匿名函数
    avoid_implementing_value_types: true
    avoid_init_to_null: true
    avoid_js_rounded_ints: true
    avoid_null_checks_in_equality_operators: true
    avoid_positional_boolean_parameters: true
    avoid_print: true  # 生产环境中避免使用print
    avoid_private_typedef_functions: true
    avoid_redundant_argument_values: true
    avoid_relative_lib_imports: true
    avoid_renaming_method_parameters: true
    avoid_return_types_on_setters: true
    avoid_returning_null: true
    avoid_returning_null_for_void: true
    avoid_returning_this: true
    avoid_setters_without_getters: true
    avoid_shadowing_type_parameters: true
    avoid_single_cascade_in_expression_statements: true
    avoid_types_as_parameter_names: true
    avoid_types_on_closure_parameters: false  # 允许在闭包参数上指定类型
    avoid_unnecessary_containers: true
    avoid_unused_constructor_parameters: true
    avoid_void_async: true
    avoid_web_libraries_in_flutter: true
    await_only_futures: true
    camel_case_extensions: true
    camel_case_types: true
    cancel_subscriptions: true
    cascade_invocations: false  # 不强制使用级联操作
    close_sinks: true
    comment_references: true
    constant_identifier_names: true
    control_flow_in_finally: true
    curly_braces_in_flow_control_structures: true
    diagnostic_describe_all_properties: false  # 不要求所有属性都有诊断描述
    directives_ordering: true
    empty_catches: true
    empty_constructor_bodies: true
    empty_statements: true
    exhaustive_cases: true
    file_names: true
    flutter_style_todos: true
    hash_and_equals: true
    implementation_imports: true
    join_return_with_assignment: true
    leading_newlines_in_multiline_strings: true
    library_names: true
    library_prefixes: true
    lines_longer_than_80_chars: false  # 不限制行长度为80
    missing_whitespace_between_adjacent_strings: true
    no_adjacent_strings_in_list: true
    no_duplicate_case_values: true
    no_logic_in_create_state: true
    no_runtimeType_toString: true
    non_constant_identifier_names: true
    null_closures: true
    omit_local_variable_types: false  # 允许显式指定局部变量类型
    one_member_abstracts: false  # 允许单成员抽象类
    only_throw_errors: true
    overridden_fields: true
    package_api_docs: true
    package_names: true
    package_prefixed_library_names: true
    parameter_assignments: true
    prefer_adjacent_string_concatenation: true
    prefer_asserts_in_initializer_lists: true
    prefer_asserts_with_message: true
    prefer_collection_literals: true
    prefer_conditional_assignment: true
    prefer_const_constructors: true
    prefer_const_constructors_in_immutables: true
    prefer_const_declarations: true
    prefer_const_literals_to_create_immutables: true
    prefer_constructors_over_static_methods: false  # 允许静态工厂方法
    prefer_contains: true
    prefer_equal_for_default_values: true
    prefer_expression_function_bodies: false  # 允许使用块函数体
    prefer_final_fields: true
    prefer_final_in_for_each: true
    prefer_final_locals: false  # 不强制局部变量使用final
    prefer_for_elements_to_map_fromIterable: true
    prefer_foreach: true
    prefer_function_declarations_over_variables: true
    prefer_generic_function_type_aliases: true
    prefer_if_elements_to_conditional_expressions: true
    prefer_if_null_operators: true
    prefer_initializing_formals: true
    prefer_inlined_adds: true
    prefer_int_literals: true
    prefer_interpolation_to_compose_strings: true
    prefer_is_empty: true
    prefer_is_not_empty: true
    prefer_is_not_operator: true
    prefer_iterable_whereType: true
    prefer_mixin: true
    prefer_null_aware_operators: true
    prefer_relative_imports: false  # 使用包导入而不是相对导入
    prefer_single_quotes: true  # 使用单引号
    prefer_spread_collections: true
    prefer_typing_uninitialized_variables: true
    prefer_void_to_null: true
    provide_deprecation_message: true
    public_member_api_docs: false  # 不强制要求公共成员API文档
    recursive_getters: true
    sized_box_for_whitespace: true
    slash_for_doc_comments: true
    sort_child_properties_last: true
    sort_constructors_first: true
    sort_pub_dependencies: true
    sort_unnamed_constructors_first: true
    test_types_in_equals: true
    throw_in_finally: true
    type_annotate_public_apis: true
    type_init_formals: true
    unawaited_futures: true
    unnecessary_await_in_return: true
    unnecessary_brace_in_string_interps: true
    unnecessary_const: true
    unnecessary_getters_setters: true
    unnecessary_lambdas: true
    unnecessary_new: true
    unnecessary_null_aware_assignments: true
    unnecessary_null_in_if_null_operators: true
    unnecessary_overrides: true
    unnecessary_parenthesis: true
    unnecessary_raw_strings: true
    unnecessary_statements: true
    unnecessary_string_escapes: true
    unnecessary_string_interpolations: true
    unnecessary_this: true
    unrelated_type_equality_checks: true
    unsafe_html: true
    use_full_hex_values_for_flutter_colors: true
    use_function_type_syntax_for_parameters: true
    use_key_in_widget_constructors: true
    use_late_for_private_fields_and_variables: true
    use_raw_strings: true
    use_rethrow_when_possible: true
    use_setters_to_change_properties: true
    use_string_buffers: true
    use_to_and_as_if_applicable: true
    valid_regexps: true
    void_checks: true

# 更多信息请参考: https://dart.dev/guides/language/analysis-options
