/// -----
/// internship_score_state.dart
/// 
/// 实习成绩状态类，定义与实习成绩相关的所有状态
///
/// <AUTHOR>
/// @date 2025-05-21
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/internship/domain/entities/internship_score.dart';

/// 实习成绩状态基类
abstract class InternshipScoreState extends Equatable {
  const InternshipScoreState();
  
  @override
  List<Object?> get props => [];
}

/// 初始状态
class InternshipScoreInitial extends InternshipScoreState {}

/// 加载中状态
class InternshipScoreLoading extends InternshipScoreState {
  /// 旧的实习成绩列表
  final List<InternshipScore> oldScores;
  
  /// 是否为首次加载
  final bool isFirstLoad;
  
  const InternshipScoreLoading({
    this.oldScores = const [],
    this.isFirstLoad = true,
  });
  
  @override
  List<Object?> get props => [oldScores, isFirstLoad];
}

/// 加载成功状态
class InternshipScoreLoaded extends InternshipScoreState {
  /// 实习成绩列表
  final List<InternshipScore> scores;
  
  /// 当前课程ID
  final String courseId;
  
  /// 当前课程名称
  final String courseName;
  
  /// 可用的课程列表
  final List<String> availableCourses;
  
  /// 当前标签索引，0表示待评分，1表示已评分
  final int currentTabIndex;
  
  /// 待评分的数量
  final int pendingCount;
  
  const InternshipScoreLoaded({
    required this.scores,
    required this.courseId,
    required this.courseName,
    this.availableCourses = const [],
    this.currentTabIndex = 0,
    this.pendingCount = 0,
  });
  
  @override
  List<Object?> get props => [
    scores, 
    courseId, 
    courseName, 
    availableCourses, 
    currentTabIndex,
    pendingCount,
  ];
  
  /// 获取待评分列表
  List<InternshipScore> get pendingScores => 
      scores.where((score) => score.score == null).toList();
  
  /// 获取已评分列表
  List<InternshipScore> get ratedScores => 
      scores.where((score) => score.score != null).toList();
  
  /// 创建一个新的状态，可更新部分属性
  InternshipScoreLoaded copyWith({
    List<InternshipScore>? scores,
    String? courseId,
    String? courseName,
    List<String>? availableCourses,
    int? currentTabIndex,
    int? pendingCount,
  }) {
    return InternshipScoreLoaded(
      scores: scores ?? this.scores,
      courseId: courseId ?? this.courseId,
      courseName: courseName ?? this.courseName,
      availableCourses: availableCourses ?? this.availableCourses,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      pendingCount: pendingCount ?? this.pendingCount,
    );
  }
}

/// 加载失败状态
class InternshipScoreError extends InternshipScoreState {
  /// 错误消息
  final String message;
  
  const InternshipScoreError({required this.message});
  
  @override
  List<Object> get props => [message];
}
