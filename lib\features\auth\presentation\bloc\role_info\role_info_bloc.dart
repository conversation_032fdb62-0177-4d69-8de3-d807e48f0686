/// -----
/// role_info_bloc.dart
/// 
/// 角色信息页面BLoC类，处理角色信息相关的业务逻辑
///
/// <AUTHOR>
/// @date 2025-05-20
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import '../../../../../core/error/failures/failure.dart';
import '../../../domain/usecases/authenticate_usecase.dart';
import 'role_info_event.dart';
import 'role_info_state.dart';

/// 角色信息BLoC类
///
/// 处理角色信息相关的业务逻辑
class RoleInfoBloc extends Bloc<RoleInfoEvent, RoleInfoState> {
  final AuthenticateUseCase _authenticateUseCase;

  RoleInfoBloc(this._authenticateUseCase) : super(const RoleInfoInitial()) {
    on<AuthenticateEvent>(_onAuthenticate);
  }

  /// 处理认证事件
  Future<void> _onAuthenticate(
    AuthenticateEvent event,
    Emitter<RoleInfoState> emit,
  ) async {
    // 发射加载状态
    emit(const RoleInfoLoading());

    // 调用认证用例
    final result = await _authenticateUseCase(
      AuthenticateParams(
        deptName: event.deptName,
        userCode: event.userCode,
        userName: event.userName,
        userType: event.userType,
      ),
    );

    // 处理结果
    result.fold(
      (failure) => emit(RoleInfoAuthenticateFailure(_mapFailureToMessage(failure))),
      (resultCode) {
        Logger.debug('resultCode', '认证结果：$resultCode');
        if (resultCode == 0 || resultCode == '0') {
          emit(const RoleInfoAuthenticateSuccess());
        } else {
          emit(const RoleInfoAuthenticateFailure('认证失败，请检查输入信息'));
        }
      },
    );
  }

  /// 将错误映射为消息
  String _mapFailureToMessage(Failure failure) {
    switch (failure.runtimeType) {
      case ServerFailure:
        return failure.message;
      case NetworkFailure:
        return '网络错误，请检查网络连接';
      default:
        return '认证失败，请稍后重试';
    }
  }
}
