/// -----
/// file_approval_list_item_model.dart
///
/// 文件审批列表项数据模型
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/entities/file_approval_list_item.dart';

/// 文件审批列表项数据模型
class FileApprovalListItemModel extends FileApprovalListItem {
  const FileApprovalListItemModel({
    required super.description,
    required super.alreadyCount,
    required super.fileCode,
    required super.fileType,
    required super.shouldCount,
  });

  /// 从 JSON 创建模型
  factory FileApprovalListItemModel.fromJson(Map<String, dynamic> json) {
    return FileApprovalListItemModel(
      description: json['description'] as String? ?? '老师文件审批', // API没有返回description，使用默认值
      alreadyCount: json['alreadyCount'] as int? ?? 0,
      fileCode: json['fileCode'] as int? ?? 0,
      fileType: json['fileType'] as String? ?? '',
      shouldCount: json['shouldCount'] as int? ?? 0,
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'description': description,
      'alreadyCount': alreadyCount,
      'fileCode': fileCode,
      'fileType': fileType,
      'shouldCount': shouldCount,
    };
  }

  /// 转换为实体
  FileApprovalListItem toEntity() {
    return FileApprovalListItem(
      description: description,
      alreadyCount: alreadyCount,
      fileCode: fileCode,
      fileType: fileType,
      shouldCount: shouldCount,
    );
  }

  /// 从实体创建模型
  factory FileApprovalListItemModel.fromEntity(FileApprovalListItem entity) {
    return FileApprovalListItemModel(
      description: entity.description,
      alreadyCount: entity.alreadyCount,
      fileCode: entity.fileCode,
      fileType: entity.fileType,
      shouldCount: entity.shouldCount,
    );
  }

  @override
  String toString() {
    return 'FileApprovalListItemModel{description: $description, alreadyCount: $alreadyCount, fileCode: $fileCode, fileType: $fileType, shouldCount: $shouldCount}';
  }
}
