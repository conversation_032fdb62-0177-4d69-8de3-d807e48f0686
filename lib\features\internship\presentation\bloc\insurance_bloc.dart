/// -----
/// insurance_bloc.dart
/// 
/// 实习保险BLoC，处理实习保险相关的业务逻辑
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/features/internship/domain/entities/insurance_info.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/insurance_event.dart';
import 'package:flutter_demo/features/internship/presentation/bloc/insurance_state.dart';

/// 实习保险BLoC
///
/// 处理实习保险相关的业务逻辑，包括加载保险信息、切换课程等
class InsuranceBloc extends Bloc<InsuranceEvent, InsuranceState> {
  InsuranceBloc() : super(InsuranceInitial()) {
    on<LoadInsuranceInfoEvent>(_onLoadInsuranceInfo);
    on<ChangeCourseEvent>(_onChangeCourse);
    on<ViewInsuranceContractEvent>(_onViewInsuranceContract);
  }

  /// 处理加载保险信息事件
  Future<void> _onLoadInsuranceInfo(
    LoadInsuranceInfoEvent event,
    Emitter<InsuranceState> emit,
  ) async {
    emit(InsuranceLoading());
    
    try {
      // TODO: 从仓库获取实际数据
      // 这里使用模拟数据
      await Future.delayed(const Duration(milliseconds: 800));
      
      final insuranceInfo = InsuranceInfo(
        name: '全国职业院校学生习责任保险',
        policyNumber: '6615042023410111000213',
        purchaser: '学校',
        type: '学生实习责任保险',
        insurancePeriod: DateTimeRange(
          start: DateTime(2023, 7, 1),
          end: DateTime(2024, 6, 30),
        ),
        internshipPeriod: DateTimeRange(
          start: DateTime(2023, 7, 1),
          end: DateTime(2024, 6, 30),
        ),
        uncoveredDays: 5,
        contractFileName: '保险合同.docx',
        contractFilePath: '/storage/documents/保险合同.docx',
      );
      
      final availableCourses = [
        '2021级市场销售2023-2024实习学年第二学期',
        '2022级电子商务2023-2024实习学年第一学期',
        '2020级会计2022-2023实习学年第二学期',
      ];
      
      emit(InsuranceLoaded(
        insuranceInfo: insuranceInfo,
        availableCourses: availableCourses,
        currentCourse: event.courseId,
      ));
    } catch (e) {
      emit(InsuranceError(message: '加载保险信息失败: ${e.toString()}'));
    }
  }

  /// 处理切换课程事件
  Future<void> _onChangeCourse(
    ChangeCourseEvent event,
    Emitter<InsuranceState> emit,
  ) async {
    final currentState = state;
    if (currentState is InsuranceLoaded) {
      emit(InsuranceLoading());
      
      try {
        // 重新加载新课程的保险信息
        add(LoadInsuranceInfoEvent(courseId: event.newCourseId));
      } catch (e) {
        emit(InsuranceError(message: '切换课程失败: ${e.toString()}'));
      }
    }
  }

  /// 处理查看保险合同事件
  void _onViewInsuranceContract(
    ViewInsuranceContractEvent event,
    Emitter<InsuranceState> emit,
  ) {
    emit(InsuranceContractViewing(
      filePath: event.filePath,
      fileName: event.fileName,
    ));
  }
}
