/// -----
/// monthly_report.dart
/// 
/// 月报模型，定义月报特有的属性
/// 
/// <AUTHOR>
/// @date 2025-06-17
/// @version 1.0
/// @copyright Copyright © 2023-2024 亿硕教育
/// -----

import 'package:flutter_demo/features/report/core/enums/report_enums.dart';
import 'package:flutter_demo/features/report/data/models/base_report.dart';

class MonthlyReport extends BaseReport {
  final String monthSummary;
  final String achievements;
  final String problems;
  final String nextMonthPlan;
  final String suggestions;

  const MonthlyReport({
    required super.id,
    required super.userId,
    required super.userName,
    required super.courseName,
    super.title,
    required super.createdAt,
    required super.status,
    super.isLate = false,
    super.rating,
    super.teacherComment,
    super.teacherName,
    super.commentTime,
    required this.monthSummary,
    required this.achievements,
    required this.problems,
    required this.nextMonthPlan,
    required this.suggestions,
    super.infoTitle,
    super.contentTitle
  });

  @override
  String get content => monthSummary;

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'courseName': courseName,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'status': status.toString(),
      'isLate': isLate,
      'rating': rating,
      'teacherComment': teacherComment,
      'teacherName': teacherName,
      'commentTime': commentTime?.toIso8601String(),
      'monthSummary': monthSummary,
      'achievements': achievements,
      'problems': problems,
      'nextMonthPlan': nextMonthPlan,
      'suggestions': suggestions,
      'infoTitle': infoTitle,
      'contentTitle': contentTitle,
    };
  }
} 