import 'package:flutter/material.dart';

class AppTheme {
  // 主要颜色
  static const Color primaryColor = Color(0xFF2979FF); // 更新为蓝色主题
  static const Color secondaryColor = Color(0xFF494F60);
  static const Color backgroundColor = Color(0xFFF8F8F8);
  static const Color errorColor = Colors.redAccent;

  static const Color black333  = Color(0xFF333333);
  static const Color black666  = Color(0xFF666666);
  static const Color black999  = Color(0xFF999999);
  static const Color blue2165f6  = Color(0xFF2165F6);
  // 文本颜色
  static const Color textPrimaryColor = Colors.black87;
  static const Color textSecondaryColor = Colors.black54;
  static const Color textLightColor = Colors.white;
  static const Color textHintColor = black999;


  // 其他颜色
  static const Color dividerColor = Color(0xFFEEEEEE);
  static const Color cardColor = Colors.white;
  static const Color shadowColor = Colors.black12;
  static const Color grayE5 = Color(0xFFE5E5E5);

  // 定义亮色主题
  static ThemeData lightTheme = ThemeData(
    primaryColor: primaryColor,
    scaffoldBackgroundColor: backgroundColor,
    appBarTheme: const AppBarTheme(
      backgroundColor: backgroundColor,
      elevation: 0,
      iconTheme: IconThemeData(color: textPrimaryColor),
      titleTextStyle: TextStyle(
        color: textPrimaryColor,
        fontSize: 18,
        fontWeight: FontWeight.w600,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryColor,
        foregroundColor: textLightColor,
        minimumSize: const Size(double.infinity, 50),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8), // 更新为8px圆角，与登录页面一致
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryColor,
        textStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: const Color(0xFFF5F5F5), // 更新为浅灰色背景，与登录页面一致
      contentPadding: const EdgeInsets.symmetric(
        vertical: 12.0,
        horizontal: 16.0,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0), // 更新为8px圆角
        borderSide: const BorderSide(color: dividerColor),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0), // 更新为8px圆角
        borderSide: const BorderSide(color: primaryColor),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8.0), // 更新为8px圆角
        borderSide: const BorderSide(color: errorColor),
      ),
    ),
    colorScheme: ColorScheme.fromSwatch().copyWith(
      primary: primaryColor,
      secondary: secondaryColor,
      error: errorColor,
    ),
  );
}