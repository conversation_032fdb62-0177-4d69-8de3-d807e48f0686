/// -----
/// state_mapper.dart
/// 
/// 状态映射工具，用于将BLoC状态映射为页面状态
///
/// <AUTHOR>
/// @date 2025-05-27
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_demo/core/widgets/page_state_widget.dart';
import 'package:flutter_demo/features/plan/presentation/bloc/plan_list_state.dart';

/// 状态映射工具类
///
/// 提供将各种BLoC状态映射为PageStateType的方法
class StateMapper {
  /// 私有构造函数，防止实例化
  StateMapper._();
  
  /// 映射实习计划列表状态
  static PageStateType mapPlanListState(PlanListState state) {
    if (state is PlanListLoading) {
      return PageStateType.initialLoading;
    } else if (state is PlanListError) {
      return PageStateType.networkError;
    } else if (state is PlanListLoaded) {
      return state.plans.isEmpty ? PageStateType.empty : PageStateType.content;
    } else if (state is PlanListApplying || state is PlanListExempting) {
      return PageStateType.contentLoading;
    } else {
      return PageStateType.initialLoading;
    }
  }
  
  // 可以根据需要添加其他状态映射方法
  // 例如：
  // static PageStateType mapInternshipListState(InternshipListState state) { ... }
  // static PageStateType mapSafetyExamState(SafetyExamState state) { ... }
  
  /// 通用状态映射方法
  /// 
  /// 根据状态类型自动选择合适的映射方法
  static PageStateType mapState(dynamic state) {
    if (state is PlanListState) {
      return mapPlanListState(state);
    } else {
      // 默认处理
      if (state.toString().contains('Loading')) {
        return PageStateType.initialLoading;
      } else if (state.toString().contains('Error')) {
        return PageStateType.networkError;
      } else if (state.toString().contains('Empty')) {
        return PageStateType.empty;
      } else {
        return PageStateType.content;
      }
    }
  }
}
