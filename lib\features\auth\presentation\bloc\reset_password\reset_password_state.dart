/// -----
/// reset_password_state.dart
///
/// 重置密码状态类，定义重置密码流程中的各种状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';

/// 重置密码状态
abstract class ResetPasswordState extends Equatable {
  const ResetPasswordState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class ResetPasswordInitial extends ResetPasswordState {}

/// 加载状态
class ResetPasswordLoading extends ResetPasswordState {}

/// 验证码发送成功状态
class VerificationCodeSent extends ResetPasswordState {
  final String response;

  const VerificationCodeSent(this.response);

  @override
  List<Object?> get props => [response];
}

/// 密码重置成功状态
class PasswordResetSuccess extends ResetPasswordState {
  final String response;

  const PasswordResetSuccess(this.response);

  @override
  List<Object?> get props => [response];
}

/// 错误状态
class ResetPasswordError extends ResetPasswordState {
  final String message;

  const ResetPasswordError(this.message);

  @override
  List<Object?> get props => [message];
}
