/// -----
/// location_field.dart
///
/// 地址选择字段组件
///
/// <AUTHOR>
/// @date 2025-06-24
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/form_field_item.dart';
import 'package:flutter_demo/features/internship/presentation/utils/dialog_utils.dart';

/// 地址选择字段组件
class LocationField extends StatelessWidget {
  /// 字段标签
  final String label;
  
  /// 当前值
  final String value;
  
  /// 占位符文本
  final String placeholder;
  
  /// 值变化回调
  final Function(String) onChanged;
  
  /// 是否显示分隔线
  final bool showDivider;

  const LocationField({
    super.key,
    required this.label,
    required this.value,
    this.placeholder = '请选择省市区',
    required this.onChanged,
    this.showDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    return FormFieldItem(
      label: label,
      value: value.isEmpty ? placeholder : value,
      type: FormFieldType.location,
      showDivider: showDivider,
      onTap: () => _showLocationPicker(context),
    );
  }

  /// 显示地址选择器
  void _showLocationPicker(BuildContext context) {
    DialogUtils.showLocationPicker(
      context,
      title: label,
      onSelected: onChanged,
    );
  }
}
