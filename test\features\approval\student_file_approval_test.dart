/// -----
/// student_file_approval_test.dart
///
/// 学生文件审批功能测试
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/approval/domain/entities/student_file_approval.dart';
import 'package:flutter_demo/features/approval/data/models/student_file_approval_model.dart';

void main() {
  group('StudentFileApproval', () {
    test('should create entity with correct properties', () {
      // Arrange
      const fileDetail = FileApprovalDetail(
        id: '1933346086595534849',
        planId: '8',
        studentId: '110',
        fileName: '测试附件',
        fileType: '三方协议',
        fileCode: 1,
        fileUrl: 'http://example.com/file.pdf',
        fileStatus: 1,
        createPerson: '刘三姐',
        createTime: 1749780601397,
      );

      const studentApproval = StudentFileApproval(
        studentId: '110',
        studentName: '王小二',
        fileList: [fileDetail],
      );

      // Assert
      expect(studentApproval.studentId, '110');
      expect(studentApproval.studentName, '王小二');
      expect(studentApproval.fileList.length, 1);
      expect(studentApproval.fileList.first.fileName, '测试附件');
    });

    test('should return correct status properties', () {
      // Arrange
      const pendingFile = FileApprovalDetail(
        id: '1',
        planId: '8',
        studentId: '110',
        fileName: '测试文件',
        fileType: '三方协议',
        fileCode: 1,
        fileUrl: 'http://example.com/file.pdf',
        fileStatus: 1, // 待审批
        createPerson: '学生',
        createTime: 1749780601397,
      );

      const approvedFile = FileApprovalDetail(
        id: '2',
        planId: '8',
        studentId: '110',
        fileName: '测试文件',
        fileType: '三方协议',
        fileCode: 1,
        fileUrl: 'http://example.com/file.pdf',
        fileStatus: 2, // 已审批
        createPerson: '学生',
        createTime: 1749780601397,
      );

      // Assert
      expect(pendingFile.isPending, true);
      expect(pendingFile.isApproved, false);
      expect(pendingFile.statusText, '待审批');

      expect(approvedFile.isPending, false);
      expect(approvedFile.isApproved, true);
      expect(approvedFile.statusText, '已审批');
    });
  });

  group('StudentFileApprovalModel', () {
    test('should convert from JSON correctly', () {
      // Arrange
      final json = {
        'studentId': '110',
        'studentName': '王小二',
        'fileList': [
          {
            'id': '1933346086595534849',
            'planId': '8',
            'studentId': '110',
            'fileName': '测试附件',
            'fileType': '三方协议',
            'fileCode': 1,
            'fileUrl': 'http://example.com/file.pdf',
            'fileStatus': 1,
            'approveName': null,
            'teacherId': null,
            'approveRole': null,
            'approveRoleName': null,
            'remark': null,
            'createPerson': '刘三姐',
            'createTime': 1749780601397,
            'updatePerson': null,
            'updateTime': null,
          }
        ]
      };

      // Act
      final model = StudentFileApprovalModel.fromJson(json);

      // Assert
      expect(model.studentId, '110');
      expect(model.studentName, '王小二');
      expect(model.fileList.length, 1);
      expect(model.fileList.first.fileName, '测试附件');
      expect(model.fileList.first.fileType, '三方协议');
      expect(model.fileList.first.fileStatus, 1);
    });

    test('should convert to JSON correctly', () {
      // Arrange
      const fileDetailModel = FileApprovalDetailModel(
        id: '1933346086595534849',
        planId: '8',
        studentId: '110',
        fileName: '测试附件',
        fileType: '三方协议',
        fileCode: 1,
        fileUrl: 'http://example.com/file.pdf',
        fileStatus: 1,
        createPerson: '刘三姐',
        createTime: 1749780601397,
      );

      const model = StudentFileApprovalModel(
        studentId: '110',
        studentName: '王小二',
        fileList: [fileDetailModel],
      );

      // Act
      final json = model.toJson();

      // Assert
      expect(json['studentId'], '110');
      expect(json['studentName'], '王小二');
      expect(json['fileList'], isA<List>());
      expect((json['fileList'] as List).length, 1);
    });

    test('should convert to entity correctly', () {
      // Arrange
      const fileDetailModel = FileApprovalDetailModel(
        id: '1933346086595534849',
        planId: '8',
        studentId: '110',
        fileName: '测试附件',
        fileType: '三方协议',
        fileCode: 1,
        fileUrl: 'http://example.com/file.pdf',
        fileStatus: 1,
        createPerson: '刘三姐',
        createTime: 1749780601397,
      );

      const model = StudentFileApprovalModel(
        studentId: '110',
        studentName: '王小二',
        fileList: [fileDetailModel],
      );

      // Act
      final entity = model.toEntity();

      // Assert
      expect(entity.studentId, '110');
      expect(entity.studentName, '王小二');
      expect(entity.fileList.length, 1);
      expect(entity.fileList.first.fileName, '测试附件');
    });
  });
}
