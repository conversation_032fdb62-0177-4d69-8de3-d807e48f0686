/// -----
/// page_state_widget.dart
/// 
/// 页面状态组件，用于管理页面的不同状态（加载中、空数据、错误等）
///
/// <AUTHOR>
/// @date 2025-05-27
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/loading_widget.dart';

/// 页面状态类型
enum PageStateType {
  /// 初始加载中
  initialLoading,
  
  /// 内容加载中（带骨架屏）
  contentLoading,
  
  /// 空数据
  empty,
  
  /// 网络错误
  networkError,
  
  /// 服务器错误
  serverError,
  
  /// 权限错误
  permissionError,
  
  /// 内容
  content,
  
  /// 自定义
  custom,
}

/// 页面状态组件
/// 
/// 根据不同的状态显示对应的UI，支持自定义状态视图
class PageStateWidget extends StatelessWidget {
  /// 当前页面状态
  final PageStateType state;
  
  /// 内容组件
  final Widget? child;
  
  /// 加载提示文本
  final String? loadingMessage;
  
  /// 空数据提示文本
  final String? emptyMessage;
  
  /// 空数据提示图标
  final IconData? emptyIcon;
  
  /// 错误提示文本
  final String? errorMessage;
  
  /// 错误提示图标
  final IconData? errorIcon;
  
  /// 重试回调
  final VoidCallback? onRetry;
  
  /// 自定义状态构建器
  final Widget Function(PageStateType state)? customBuilder;
  
  /// 是否显示加载骨架屏
  final bool showSkeleton;
  
  /// 骨架屏组件
  final Widget? skeleton;
  
  /// 构造函数
  const PageStateWidget({
    Key? key,
    required this.state,
    this.child,
    this.loadingMessage = '加载中...',
    this.emptyMessage = '暂无数据',
    this.emptyIcon = Icons.inbox_outlined,
    this.errorMessage = '加载失败，请重试',
    this.errorIcon = Icons.error_outline,
    this.onRetry,
    this.customBuilder,
    this.showSkeleton = true,
    this.skeleton,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    // 优先使用自定义构建器
    if (customBuilder != null) {
      return customBuilder!(state);
    }
    
    // 根据状态返回对应的UI
    switch (state) {
      case PageStateType.initialLoading:
        return _buildLoadingView();
      case PageStateType.contentLoading:
        return showSkeleton 
            ? (skeleton ?? _buildDefaultSkeleton()) 
            : _buildLoadingView();
      case PageStateType.empty:
        return _buildEmptyView();
      case PageStateType.networkError:
      case PageStateType.serverError:
      case PageStateType.permissionError:
        return _buildErrorView();
      case PageStateType.content:
        return child ?? const SizedBox.shrink();
      case PageStateType.custom:
        return child ?? const SizedBox.shrink();
    }
  }
  
  /// 构建加载中视图
  Widget _buildLoadingView() {
    return Center(
      child: LoadingWidget(
        message: loadingMessage,
        showCancelButton: false,
      ),
    );
  }
  
  /// 构建默认骨架屏
  Widget _buildDefaultSkeleton() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: 6, // 显示6个骨架项
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题占位
              Container(
                width: 200,
                height: 20,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
              const SizedBox(height: 12),
              // 内容占位
              ...List.generate(3, (i) => Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              )),
            ],
          ),
        );
      },
    );
  }
  
  /// 构建空数据视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            emptyIcon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            emptyMessage!,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white, 
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }
  
  /// 构建错误视图
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            errorIcon,
            size: 64,
            color: Colors.red[300],
          ),
          const SizedBox(height: 16),
          Text(
            errorMessage!,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: AppTheme.primaryColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }
}
