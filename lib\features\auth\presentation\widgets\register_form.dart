import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/constants/constants.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/login_validators.dart';
import '../../../../core/router/app_navigator.dart';
import '../../../../core/widgets/custom_text_field.dart';
import '../bloc/register/register_bloc.dart';
import '../bloc/register/register_event.dart';
import '../bloc/register/register_state.dart';

/// 注册表单
///
/// 显示注册表单，包含手机号、验证码和密码输入框
class RegisterForm extends StatefulWidget {
  /// 注册回调
  final Function(String phone, String code, String password) onRegister;

  const RegisterForm({
    Key? key,
    required this.onRegister,
  }) : super(key: key);

  @override
  State<RegisterForm> createState() => _RegisterFormState();
}

class _RegisterFormState extends State<RegisterForm> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _verificationCodeController = TextEditingController();
  final _passwordController = TextEditingController();

  // 添加焦点节点
  final _phoneFocusNode = FocusNode();
  final _verificationCodeFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  bool _obscureText = true;
  bool _isLoading = false;
  bool _agreeToTerms = false;
  String? _phoneError;
  String? _verificationCodeError;
  String? _passwordError;

  // 焦点状态
  bool _phoneHasFocus = false;
  bool _verificationCodeHasFocus = false;
  bool _passwordHasFocus = false;

  @override
  void initState() {
    super.initState();

    // 添加焦点监听器
    _phoneFocusNode.addListener(_updatePhoneFocus);
    _verificationCodeFocusNode.addListener(_updateVerificationCodeFocus);
    _passwordFocusNode.addListener(_updatePasswordFocus);
  }

  @override
  void dispose() {
    // 释放控制器
    _phoneController.dispose();
    _verificationCodeController.dispose();
    _passwordController.dispose();

    // 移除焦点监听器
    _phoneFocusNode.removeListener(_updatePhoneFocus);
    _verificationCodeFocusNode.removeListener(_updateVerificationCodeFocus);
    _passwordFocusNode.removeListener(_updatePasswordFocus);

    // 释放焦点节点
    _phoneFocusNode.dispose();
    _verificationCodeFocusNode.dispose();
    _passwordFocusNode.dispose();

    super.dispose();
  }

  // 焦点状态更新方法
  void _updatePhoneFocus() {
    setState(() {
      _phoneHasFocus = _phoneFocusNode.hasFocus;
    });
  }

  void _updateVerificationCodeFocus() {
    setState(() {
      _verificationCodeHasFocus = _verificationCodeFocusNode.hasFocus;
    });
  }

  void _updatePasswordFocus() {
    setState(() {
      _passwordHasFocus = _passwordFocusNode.hasFocus;
    });
  }

  void _submitForm() {
    // 验证手机号
    final String? phoneError = Validators.validateMobile(_phoneController.text);
    // 验证验证码
    final String? codeError = Validators.validateVerificationCode(_verificationCodeController.text);
    // 验证密码
    final String? passwordError = Validators.validatePassword(_passwordController.text);

    setState(() {
      _phoneError = phoneError;
      _verificationCodeError = codeError;
      _passwordError = passwordError;
    });

    // 如果有错误，不进行注册
    if (phoneError != null || codeError != null || passwordError != null) {
      return;
    }

    // 检查是否同意用户协议
    if (!_agreeToTerms) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请阅读并同意《用户服务协议》'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // 设置加载状态
    setState(() {
      _isLoading = true;
    });

    // 调用注册回调
    widget.onRegister(
      _phoneController.text,
      _verificationCodeController.text,
      _passwordController.text,
    );

    // 注册后重置加载状态
    setState(() {
      _isLoading = false;
    });
  }

  void _sendVerificationCode() {
    context.read<RegisterBloc>().add(
          SendVerificationCodeEvent(
            phone: _phoneController.text,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RegisterBloc, RegisterState>(
      listener: (context, state) {
        if (state is VerificationCodeFailureState) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is VerificationCodeSentState) {
          if (state.countdownTime == 60) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('验证码发送成功，请注意查收'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      },
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 75.w),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.disabled,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部内边距，考虑AppBar高度
                SizedBox(height: 180.h),

                // 欢迎注册标题
                Row(
                  children: [
                    Text(
                      '欢迎注册',
                      style: TextStyle(
                        fontSize: 48.sp,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF333333),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Image.asset(
                      'assets/images/hand_register_icon.png',
                      width: 48.w,
                      height: 48.h,
                    ),
                  ],
                ),

                SizedBox(height: 60.h),

                // 手机号输入框
                CustomTextField(
                  label: '手机号码',
                  hintText: '请输入手机号码',
                  controller: _phoneController,
                  focusNode: _phoneFocusNode,
                  keyboardType: TextInputType.phone,
                  errorText: _phoneError,
                  onChanged: (value) {
                    setState(() {
                      _phoneError = Validators.validateMobile(value);
                    });
                  },
                ),

                SizedBox(height: 40.h),

                // 验证码输入框
                CustomTextField(
                  label: '短信验证码',
                  hintText: '请输入短信验证码',
                  controller: _verificationCodeController,
                  focusNode: _verificationCodeFocusNode,
                  keyboardType: TextInputType.number,
                  errorText: _verificationCodeError,
                  suffixIcon: BlocBuilder<RegisterBloc, RegisterState>(
                    builder: (context, state) {
                      final bool isCountingDown = state is VerificationCodeSentState && state.countdownTime > 0;
                      final int countdownTime = state is VerificationCodeSentState ? state.countdownTime : 0;
                      final bool isSending = state is VerificationCodeSendingState;

                      return TextButton(
                        onPressed: isCountingDown || isSending ? null : _sendVerificationCode,
                        style: TextButton.styleFrom(
                          minimumSize: Size.zero,
                          padding: EdgeInsets.symmetric(horizontal: 16.w),
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                        child: Text(
                          isSending ? '发送中...' : (isCountingDown ? '${countdownTime}s后重发' : '发送验证码'),
                          style: TextStyle(
                            color: isCountingDown || isSending ? Colors.grey : const Color(0xFF2165F6),
                            fontWeight: FontWeight.w500,
                            fontSize: 28.sp,
                          ),
                        ),
                      );
                    },
                  ),
                  onChanged: (value) {
                    setState(() {
                      _verificationCodeError = Validators.validateVerificationCode(value);
                    });
                  },
                ),

                SizedBox(height: 40.h),

                // 密码输入框
                CustomTextField(
                  label: '设置登录密码',
                  hintText: '请设置登录密码',
                  controller: _passwordController,
                  focusNode: _passwordFocusNode,
                  obscureText: true,
                  errorText: _passwordError,
                  onChanged: (value) {
                    setState(() {
                      _passwordError = Validators.validatePassword(value);
                    });
                  },
                ),

                SizedBox(height: 60.h),

                // 用户协议
                Row(
                  children: [
                    SizedBox(
                      width: 26.w,
                      height: 26.h,
                      child: Checkbox(
                        value: _agreeToTerms,
                        onChanged: (value) {
                          setState(() {
                            _agreeToTerms = value ?? false;
                          });
                        },
                        shape: const CircleBorder(),
                        activeColor: const Color(0xFF2165F6),
                        side: const BorderSide(color: Color(0xFFCCCCCC)),
                      ),
                    ),
                    SizedBox(width: 16.w),
                    Text.rich(
                      TextSpan(
                        text: '已阅读并同意 ',
                        style: TextStyle(
                          color: const Color(0xFF666666),
                          fontSize: 24.sp,
                        ),
                        children: [
                          TextSpan(
                            text: '《用户服务协议》',
                            style: TextStyle(
                              color: const Color(0xFF2165F6),
                              fontSize: 24.sp,
                            ),
                            recognizer: TapGestureRecognizer()
                              ..onTap = () {
                                AppNavigator.goToUserAgreement(context);
                              },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 80.h),

                // 确认按钮
                SizedBox(
                  width: double.infinity,
                  height: 88.h,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _submitForm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2165F6),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      elevation: 0,
                    ),
                    child: _isLoading
                        ? SizedBox(
                            width: 40.w,
                            height: 40.h,
                            child: const CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : Text(
                            '确认',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 32.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                  ),
                ),

                SizedBox(height: 60.h),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
