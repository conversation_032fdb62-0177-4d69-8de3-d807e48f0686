import requests
import json  # 用于美化 JSON 输出

url = "http://**************:8088/internshipservice/v1/internship/student/exempt/applyRecord"
params = {
    "planId ": "8",
}
headers = {
    "token": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMDIyOTUyNyIsImRlcHQiOjE5MjcyNDgzOTY5MDAzNTIwMDAsInVzZXJUeXBlIjoxLCJ1c2VyS2V5IjoiQzY4RUZERkM0RTUyMTFGMEIzNzIwMjQyQzBBODY0MDcifQ.WndmicI_dBVDGVGeWg7UyCXUdtTrjOeYvU1tucb8ShKJg60E-DwIf9Gg8Huu7-cHh6KFzniiqAk40wH1PKfyFg"
}

try:
    response = requests.get(url, params=params, headers=headers)
    response.raise_for_status()  # 检查请求是否成功
    
    # 获取 JSON 数据并美化输出
    json_data = response.json()
    pretty_json = json.dumps(json_data, indent=4, ensure_ascii=False)  # 缩进4个空格，支持中文
    
    print("请求成功！")
    print("状态码:", response.status_code)
    print("美化后的 JSON 响应:")
    print(pretty_json)
    
except requests.exceptions.RequestException as e:
    print("请求失败:", e)