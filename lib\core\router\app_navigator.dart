import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/common/presentation/screens/webview_screen.dart';
import 'route_constants.dart';

/// 应用导航器
///
/// 提供导航方法，简化页面跳转
class AppNavigator {
  /// 导航到启动页
  static void goToSplash(BuildContext context) {
    context.go(AppRoutes.splash);
  }

  /// 导航到登录页
  static void goToLogin(BuildContext context) {
    context.go(AppRoutes.login);
  }

  /// 导航到注册页
  static void goToRegister(BuildContext context) {
    context.go(AppRoutes.register);
  }

  /// 导航到重置密码页
  static void goToResetPassword(BuildContext context) {
    context.go(AppRoutes.resetPassword);
  }

  /// 导航到身份验证页
  static void goToIdentityVerification(BuildContext context) {
    context.go(AppRoutes.identityVerification);
  }

  /// 导航到首页
  static void goToHome(BuildContext context) {
    context.go(AppRoutes.home);
  }

  /// 导航到数据页
  static void goToData(BuildContext context) {
    context.go(AppRoutes.data);
  }

  /// 导航到个人中心
  static void goToProfile(BuildContext context) {
    context.go(AppRoutes.profile);
  }

  /// 导航到实习列表
  static void goToInternshipList(BuildContext context) {
    context.go('${AppRoutes.home}internship');
  }

  /// 导航到实习详情
  static void goToInternshipDetail(BuildContext context, String id) {
    context.go(AppRoutes.internshipDetail.replaceFirst(':id', id));
  }

  /// 导航到实习审批
  static void goToInternshipApproval(BuildContext context, String id) {
    context.go(AppRoutes.internshipApproval.replaceFirst(':id', id));
  }

  /// 导航到报告列表
  static void goToReportList(BuildContext context) {
    context.go('${AppRoutes.home}report');
  }

  /// 导航到报告详情
  static void goToReportDetail(BuildContext context, String id) {
    context.go(AppRoutes.reportDetail.replaceFirst(':id', id));
  }

  /// 导航到报告审批
  static void goToReportApproval(BuildContext context, String id) {
    context.go(AppRoutes.reportApproval.replaceFirst(':id', id));
  }

  /// 导航到报告提交
  static void goToReportSubmit(BuildContext context) {
    context.go(AppRoutes.reportSubmit);
  }

  /// 导航到安全教育列表
  static void goToSafetyList(BuildContext context) {
    context.go('${AppRoutes.home}safety');
  }

  /// 导航到安全教育详情
  static void goToSafetyDetail(BuildContext context, String id) {
    context.go(AppRoutes.safetyDetail.replaceFirst(':id', id));
  }

  /// 导航到设置
  static void goToSettings(BuildContext context) {
    context.go(AppRoutes.settings);
  }

  /// 导航到修改密码
  static void goToChangePassword(BuildContext context) {
    context.go('${AppRoutes.settings}/password');
  }

  /// 导航到用户协议
  static void goToUserAgreement(BuildContext context) {
    // TODO: 实现用户协议页面路由
    // 临时使用WebViewScreen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const WebViewScreen(
          title: '用户协议',
          assetPath: 'assets/html/user_agreement.html',
        ),
      ),
    );
  }

  /// 返回上一页
  static void goBack(BuildContext context) {
    if (context.canPop()) {
      context.pop();
    } else {
      goToHome(context);
    }
  }
}
