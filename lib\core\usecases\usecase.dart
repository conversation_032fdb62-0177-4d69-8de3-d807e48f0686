import 'package:dartz/dartz.dart';
import '../error/failures/failure.dart';

/// 用例接口
///
/// 定义所有用例的通用接口
/// Type参数T表示用例的返回类型
/// Type参数Params表示用例的参数类型
abstract class UseCase<T, Params> {
  /// 调用用例
  ///
  /// 参数：[params] 用例参数
  /// 返回：Either<Failure, T> 表示用例执行结果
  Future<Either<Failure, T>> call(Params params);
}

/// 无参数用例接口
///
/// 定义不需要参数的用例的通用接口
/// Type参数T表示用例的返回类型
abstract class NoParamsUseCase<T> {
  /// 调用用例
  ///
  /// 返回：Either<Failure, T> 表示用例执行结果
  Future<Either<Failure, T>> call();
}

/// 无参数
///
/// 用于不需要参数的用例
class NoParams {
  const NoParams();
}
