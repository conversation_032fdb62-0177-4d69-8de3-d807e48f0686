import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

/// 意见反馈页面
///
/// 用户可以在此页面选择反馈类型、输入反馈内容、上传图片和提供联系方式
/// 支持上传最多6张图片，反馈内容限制100字
class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({Key? key}) : super(key: key);

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  final TextEditingController _feedbackController = TextEditingController();
  final TextEditingController _contactController = TextEditingController();
  final List<File> _selectedImages = [];
  String _selectedFeedbackType = '';
  final int _maxImages = 6;
  final int _maxTextLength = 100;

  @override
  void dispose() {
    _feedbackController.dispose();
    _contactController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '意见反馈',
        backgroundColor: Colors.white,
      ),
      body: GestureDetector(
        // 点击空白区域收起键盘
        onTap: () => FocusScope.of(context).unfocus(),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 反馈类型选择
              _buildFeedbackTypeSelector(),

              const Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),

              // 反馈输入区域
              _buildFeedbackInput(),

              const SizedBox(height: 10),

              // 联系方式
              _buildContactInput(),

              const SizedBox(height: 10),

              // 图片上传
              _buildImageUpload(),

              const SizedBox(height: 30),

              // 提交按钮
              _buildSubmitButton(),
            ],
          ),
        ),
      ),
    );
  }

  // 反馈类型选择器
  Widget _buildFeedbackTypeSelector() {
    return Container(
      color: Colors.white,
      child: ListTile(
        title: Text(
          _selectedFeedbackType.isEmpty ? '请选择反馈类型' : _selectedFeedbackType,
          style: TextStyle(
            color: _selectedFeedbackType.isEmpty ? Colors.grey[400] : Colors.black87,
            fontSize: 16,
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey,
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 20),
        onTap: _showFeedbackTypeDialog,
      ),
    );
  }

  // 反馈输入框
  Widget _buildFeedbackInput() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
      child: TextField(
        controller: _feedbackController,
        maxLines: 5,
        maxLength: _maxTextLength,
        decoration: InputDecoration(
          hintText: '请写下您对AI实习管理平台的感受，我们将认真听取您的意见，努力提供更优质的服务。',
          hintStyle: const TextStyle(
            fontSize: 14,
            color: AppTheme.black999,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
          counterText: '${_feedbackController.text.length}/$_maxTextLength',
          counterStyle: TextStyle(color: Colors.grey[400], fontSize: 12),
          fillColor: Colors.white,
          filled: true,
          // 确保没有默认边距
          enabledBorder: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.zero,
          ),
          focusedBorder: const OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.zero,
          ),
        ),
        style: const TextStyle(
          fontSize: 15,
          color: Colors.black87,
        ),
        onChanged: (text) {
          setState(() {});
        },
      ),
    );
  }

  // 联系方式输入框
  Widget _buildContactInput() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      height: 50,
      child: Row(
        children: [
          const Text(
            '手机/邮箱/QQ',
            style: TextStyle(
              fontSize: 15,
              color: Colors.black87,
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: TextField(
              controller: _contactController,
              decoration: const InputDecoration(
                hintText: '选填',
                hintStyle: TextStyle(color: AppTheme.black999, fontSize: 14),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 10),
                fillColor: Colors.white,
                filled: true,
                isDense: true,
                // 确保没有默认边距
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.zero,
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide.none,
                  borderRadius: BorderRadius.zero,
                ),
              ),
              style: const TextStyle(
                fontSize: 15,
                color: Colors.black87,
              ),
              textAlign: TextAlign.left,
              keyboardType: TextInputType.text,
            ),
          ),
        ],
      ),
    );
  }

  // 图片上传区域
  Widget _buildImageUpload() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '上传图片(最多6张)',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),
          _buildImageGrid(),
        ],
      ),
    );
  }

  // 图片网格
  Widget _buildImageGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      mainAxisSpacing: 10,
      crossAxisSpacing: 10,
      childAspectRatio: 1,
      children: [
        ..._selectedImages.map((image) => _buildImageItem(image)),
        if (_selectedImages.length < _maxImages)
          _buildAddImageButton(),
      ],
    );
  }

  // 图片项
  Widget _buildImageItem(File image) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey[300]!),
            image: DecorationImage(
              image: FileImage(image),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned(
          top: -10,
          right: -10,
          child: GestureDetector(
            onTap: () {
              setState(() {
                _selectedImages.remove(image);
              });
            },
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                size: 16,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 添加图片按钮
  Widget _buildAddImageButton() {
    return GestureDetector(
      onTap: _pickImage,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Center(
          child: Icon(
            Icons.add,
            size: 40,
            color: Colors.grey,
          ),
        ),
      ),
    );
  }

  // 提交按钮
  Widget _buildSubmitButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SizedBox(
        width: double.infinity,
        height: 50,
        child: ElevatedButton(
          onPressed: _submitFeedback,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 0,
          ),
          child: const Text(
            '提交',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }

  // 显示反馈类型选择对话框
  void _showFeedbackTypeDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.only(top: 20, bottom: 30),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Padding(
                padding: EdgeInsets.only(bottom: 20),
                child: Text(
                  '请选择反馈类型',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Divider(height: 1, thickness: 0.5, color: Colors.grey[300]),
              ..._buildFeedbackTypeOptions(),
            ],
          ),
        );
      },
    );
  }

  // 构建反馈类型选项
  List<Widget> _buildFeedbackTypeOptions() {
    final List<String> options = [
      '功能建议',
      '操作问题',
      '内容错误',
      '产品Bug',
      '其他问题',
    ];

    return options.map((option) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: Text(
              option,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 15),
            ),
            dense: true,
            onTap: () {
              setState(() {
                _selectedFeedbackType = option;
              });
              Navigator.pop(context);
            },
          ),
          if (option != options.last)
            Divider(height: 1, thickness: 0.5, color: Colors.grey[300], indent: 20, endIndent: 20),
        ],
      );
    }).toList();
  }

  // 选择图片
  Future<void> _pickImage() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        setState(() {
          _selectedImages.add(File(pickedFile.path));
        });
      }
    } catch (e) {
      debugPrint('Error picking image: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('选择图片失败，请重试')),
      );
    }
  }

  // 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  // 提交反馈
  void _submitFeedback() {
    // 检查是否选择了反馈类型
    if (_selectedFeedbackType.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择反馈类型')),
      );
      return;
    }

    // 检查是否输入了反馈内容
    if (_feedbackController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入反馈内容')),
      );
      return;
    }

    // 这里可以添加提交反馈的逻辑，如发送到服务器等

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('感谢您的反馈，我们会尽快处理！')),
    );

    // 提交成功后返回上一页
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        Navigator.pop(context);
      }
    });
  }
}