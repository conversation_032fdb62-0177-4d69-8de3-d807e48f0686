/// -----
/// auth_failure.dart
/// 
/// 认证相关失败类定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'failure.dart';

/// 认证失败基类
/// 
/// 当认证相关操作失败时使用此失败类型
abstract class AuthFailure extends Failure {
  const AuthFailure(String message) : super(message: message);
}

/// 未授权失败
/// 
/// 当用户未登录或token无效时使用此失败类型
class UnauthorizedFailure extends AuthFailure {
  const UnauthorizedFailure([String message = '未授权，请重新登录']) : super(message);
}

/// Token过期失败
/// 
/// 当token过期需要刷新时使用此失败类型
class TokenExpiredFailure extends AuthFailure {
  const TokenExpiredFailure([String message = 'Token已过期']) : super(message);
}

/// 权限不足失败
/// 
/// 当用户权限不足时使用此失败类型
class ForbiddenFailure extends AuthFailure {
  const ForbiddenFailure([String message = '权限不足，禁止访问']) : super(message);
}
