/// -----
/// approval_list_item.dart
///
/// 通用审批列表项组件，支持不同类型的审批项展示
/// 可用于免实习申请、文件审批等各种审批场景
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// 审批项数据接口
abstract class ApprovalItemData {
  String get id;
  String get studentName;
  String get studentAvatar;
  String get status;
  String get submitTime;
}

/// 通用审批列表项组件
class ApprovalListItem extends StatelessWidget {
  /// 审批项数据
  final ApprovalItemData item;

  /// 是否为待审批状态
  final bool isPending;

  /// 点击回调
  final VoidCallback? onTap;

  /// 查看按钮点击回调
  final VoidCallback? onViewTap;

  /// 自定义内容区域构建器
  final Widget Function(ApprovalItemData item)? contentBuilder;

  /// 自定义附件区域构建器
  final Widget Function(ApprovalItemData item)? attachmentBuilder;

  const ApprovalListItem({
    Key? key,
    required this.item,
    this.isPending = false,
    this.onTap,
    this.onViewTap,
    this.contentBuilder,
    this.attachmentBuilder,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final status = item.status;
    final statusColor = _getStatusColor(status);

    return InkWell(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 学生信息行
              Row(
                children: [
                  // 头像
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey[300],
                    ),
                    child: ClipOval(
                      child: item.studentAvatar.isNotEmpty
                          ? Image.network(
                              item.studentAvatar,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildDefaultAvatar(),
                            )
                          : _buildDefaultAvatar(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // 学生姓名
                  Text(
                    item.studentName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  // 状态标签
                  Text(
                    isPending ? '待审批' : status,
                    style: TextStyle(
                      fontSize: 14,
                      color: isPending ? AppTheme.primaryColor : statusColor,
                    ),
                  ),
                ],
              ),

              // 分割线
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
              ),

              // 自定义内容区域
              if (contentBuilder != null) contentBuilder!(item),

              // 自定义附件区域
              if (attachmentBuilder != null) ...[
                const SizedBox(height: 8),
                attachmentBuilder!(item),
              ],

              // 分割线
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 12),
                child: Divider(height: 1, thickness: 0.5, color: Color(0xFFEEEEEE)),
              ),

              // 时间和查看按钮
              Row(
                children: [
                  Text(
                    '提交时间：${item.submitTime}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  // 查看按钮
                  InkWell(
                    onTap: onViewTap,
                    child: const Row(
                      children: [
                        Text(
                          '查看',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppTheme.primaryColor,
                          ),
                        ),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 12,
                          color: AppTheme.primaryColor,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return SvgPicture.asset(
      'assets/images/default_avatar.svg',
      fit: BoxFit.cover,
    );
  }

  /// 根据状态获取颜色
  Color _getStatusColor(String status) {
    switch (status) {
      case '待审批':
        return AppTheme.primaryColor;
      case '已通过':
        return Colors.grey;
      case '已驳回':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}

/// 信息行构建器
Widget buildInfoRow(String label, String value) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      SizedBox(
        width: 80,
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
          ),
        ),
      ),
      Expanded(
        child: Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
          ),
        ),
      ),
    ],
  );
}

/// 文件预览构建器
Widget buildFilePreview({
  required String fileName,
  String? previewUrl,
  VoidCallback? onTap,
}) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      SizedBox(
        width: 80,
        child: Text(
          '学生上传文件',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
          ),
        ),
      ),
      Expanded(
        child: Align(
          alignment: Alignment.centerLeft,
          child: InkWell(
            onTap: onTap,
            child: Container(
              width: 98,
              height: 98,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!, width: 1),
              ),
              clipBehavior: Clip.antiAlias,
              child: previewUrl != null && previewUrl.isNotEmpty
                  ? Image.network(
                      previewUrl,
                      width: 98,
                      height: 98,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => _buildFileIcon(fileName),
                    )
                  : _buildFileIcon(fileName),
            ),
          ),
        ),
      ),
    ],
  );
}

/// 构建文件图标
Widget _buildFileIcon(String fileName) {
  return Container(
    width: 98,
    height: 98,
    color: Colors.grey[200],
    child: Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.insert_drive_file,
            size: 36,
            color: Colors.grey,
          ),
          const SizedBox(height: 4),
          Text(
            fileName,
            style: const TextStyle(
              fontSize: 12,
              color: AppTheme.primaryColor,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    ),
  );
}
