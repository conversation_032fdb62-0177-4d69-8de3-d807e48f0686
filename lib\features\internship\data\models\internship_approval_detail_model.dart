/// -----
/// internship_approval_detail_model.dart
///
/// 实习审核详情数据模型，用于存储实习审核详情的数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

class InternshipApprovalDetailModel {
  final String id;
  final String studentName;
  String studentAvatar = 'http://gips3.baidu.com/it/u=3886271102,3123389489&fm=3028&app=3028&f=JPEG&fmt=auto?w=1280&h=960';
  final CompanyInfo companyInfo;
  final PositionInfo positionInfo;
  final InternshipInfo internshipInfo;
  final List<AttachmentInfo> attachments;
  final List<ApprovalInfo> approvals;
  final String status; // 待审批、已通过、已驳回

  InternshipApprovalDetailModel({
    required this.id,
    required this.studentName,
    required this.studentAvatar,
    required this.companyInfo,
    required this.positionInfo,
    required this.internshipInfo,
    required this.attachments,
    required this.approvals,
    required this.status,
  });

  // 从JSON创建模型
  factory InternshipApprovalDetailModel.fromJson(Map<String, dynamic> json) {
    return InternshipApprovalDetailModel(
      id: json['id'],
      studentName: json['studentName'],
      studentAvatar: json['studentAvatar'],
      companyInfo: CompanyInfo.fromJson(json['companyInfo']),
      positionInfo: PositionInfo.fromJson(json['positionInfo']),
      internshipInfo: InternshipInfo.fromJson(json['internshipInfo']),
      attachments: (json['attachments'] as List)
          .map((item) => AttachmentInfo.fromJson(item))
          .toList(),
      approvals: (json['approvals'] as List)
          .map((item) => ApprovalInfo.fromJson(item))
          .toList(),
      status: json['status'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'studentName': studentName,
      'studentAvatar': studentAvatar,
      'companyInfo': companyInfo.toJson(),
      'positionInfo': positionInfo.toJson(),
      'internshipInfo': internshipInfo.toJson(),
      'attachments': attachments.map((item) => item.toJson()).toList(),
      'approvals': approvals.map((item) => item.toJson()).toList(),
      'status': status,
    };
  }

  // 复制并修改部分属性
  InternshipApprovalDetailModel copyWith({
    String? id,
    String? studentName,
    String? studentAvatar,
    CompanyInfo? companyInfo,
    PositionInfo? positionInfo,
    InternshipInfo? internshipInfo,
    List<AttachmentInfo>? attachments,
    List<ApprovalInfo>? approvals,
    String? status,
  }) {
    return InternshipApprovalDetailModel(
      id: id ?? this.id,
      studentName: studentName ?? this.studentName,
      studentAvatar: studentAvatar ?? this.studentAvatar,
      companyInfo: companyInfo ?? this.companyInfo,
      positionInfo: positionInfo ?? this.positionInfo,
      internshipInfo: internshipInfo ?? this.internshipInfo,
      attachments: attachments ?? this.attachments,
      approvals: approvals ?? this.approvals,
      status: status ?? this.status,
    );
  }

  // 获取示例数据
  static InternshipApprovalDetailModel getSampleData() {
    return InternshipApprovalDetailModel(
      id: '1',
      studentName: '李成儒',
      studentAvatar: '',
      companyInfo: CompanyInfo(
        name: '湖北省武汉市九州通中药材电子商务有限公司',
        creditCode: '4889745636858671F',
        size: '100-499人',
        type: '中科电子商务',
        location: '湖北省/武汉市/洪山区',
        address: '王家湾阳光大道108号',
        contactPerson: '周华健',
        contactPhone: '18555632456',
        email: '未填写',
        zipCode: '未填写',
      ),
      positionInfo: PositionInfo(
        department: '产品部',
        position: 'UI设计师',
        contactPhone: '18456986578',
        type: '开发人员',
        content: '做设计',
        location: '湖北省/武汉市/洪山区',
        address: '王家湾阳光大道108号',
      ),
      internshipInfo: InternshipInfo(
        startDate: '2025-04-25',
        endDate: '2025-08-25',
        internshipType: '自主联系',
        professionalMatch: '基本匹配',
        salary: '2000',
        accommodationType: '自行',
        accommodationArea: '湖北省/武汉市/洪山区',
        accommodationAddress: '王家湾阳光大道108号',
        provideMeals: '否',
        specialCircumstances: '否',
      ),
      attachments: [
        AttachmentInfo(
          name: '教家长通知书.DOCX',
          url: '',
        ),
        AttachmentInfo(
          name: '三方协议.DOCX',
          url: '',
        ),
      ],
      approvals: [
        ApprovalInfo(
          name: '冯宇明（班主任）',
          status: '待审批',
          date: '2025-04-25 14:30',
          isApproved: false,
        ),
      ],
      status: '待审批',
    );
  }
}

class CompanyInfo {
  final String name;
  final String creditCode;
  final String size;
  final String type;
  final String location;
  final String address;
  final String contactPerson;
  final String contactPhone;
  final String email;
  final String zipCode;

  CompanyInfo({
    required this.name,
    required this.creditCode,
    required this.size,
    required this.type,
    required this.location,
    required this.address,
    required this.contactPerson,
    required this.contactPhone,
    required this.email,
    required this.zipCode,
  });

  // 从JSON创建模型
  factory CompanyInfo.fromJson(Map<String, dynamic> json) {
    return CompanyInfo(
      name: json['name'],
      creditCode: json['creditCode'],
      size: json['size'],
      type: json['type'],
      location: json['location'],
      address: json['address'],
      contactPerson: json['contactPerson'],
      contactPhone: json['contactPhone'],
      email: json['email'],
      zipCode: json['zipCode'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'creditCode': creditCode,
      'size': size,
      'type': type,
      'location': location,
      'address': address,
      'contactPerson': contactPerson,
      'contactPhone': contactPhone,
      'email': email,
      'zipCode': zipCode,
    };
  }
}

class PositionInfo {
  final String department;
  final String position;
  final String contactPhone;
  final String type;
  final String content;
  final String location;
  final String address;

  PositionInfo({
    required this.department,
    required this.position,
    required this.contactPhone,
    required this.type,
    required this.content,
    required this.location,
    required this.address,
  });

  // 从JSON创建模型
  factory PositionInfo.fromJson(Map<String, dynamic> json) {
    return PositionInfo(
      department: json['department'],
      position: json['position'],
      contactPhone: json['contactPhone'],
      type: json['type'],
      content: json['content'],
      location: json['location'],
      address: json['address'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'department': department,
      'position': position,
      'contactPhone': contactPhone,
      'type': type,
      'content': content,
      'location': location,
      'address': address,
    };
  }
}

class InternshipInfo {
  final String startDate;
  final String endDate;
  final String internshipType;
  final String professionalMatch;
  final String salary;
  final String accommodationType;
  final String accommodationArea;
  final String accommodationAddress;
  final String provideMeals;
  final String specialCircumstances;

  InternshipInfo({
    required this.startDate,
    required this.endDate,
    required this.internshipType,
    required this.professionalMatch,
    required this.salary,
    required this.accommodationType,
    required this.accommodationArea,
    required this.accommodationAddress,
    required this.provideMeals,
    required this.specialCircumstances,
  });

  // 从JSON创建模型
  factory InternshipInfo.fromJson(Map<String, dynamic> json) {
    return InternshipInfo(
      startDate: json['startDate'],
      endDate: json['endDate'],
      internshipType: json['internshipType'],
      professionalMatch: json['professionalMatch'],
      salary: json['salary'],
      accommodationType: json['accommodationType'],
      accommodationArea: json['accommodationArea'],
      accommodationAddress: json['accommodationAddress'],
      provideMeals: json['provideMeals'],
      specialCircumstances: json['specialCircumstances'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate,
      'endDate': endDate,
      'internshipType': internshipType,
      'professionalMatch': professionalMatch,
      'salary': salary,
      'accommodationType': accommodationType,
      'accommodationArea': accommodationArea,
      'accommodationAddress': accommodationAddress,
      'provideMeals': provideMeals,
      'specialCircumstances': specialCircumstances,
    };
  }
}

class AttachmentInfo {
  final String name;
  final String url;

  AttachmentInfo({
    required this.name,
    required this.url,
  });

  // 从JSON创建模型
  factory AttachmentInfo.fromJson(Map<String, dynamic> json) {
    return AttachmentInfo(
      name: json['name'],
      url: json['url'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'url': url,
    };
  }
}

class ApprovalInfo {
  final String name;
  final String status;
  final String date;
  final bool isApproved;

  ApprovalInfo({
    required this.name,
    required this.status,
    required this.date,
    required this.isApproved,
  });

  // 从JSON创建模型
  factory ApprovalInfo.fromJson(Map<String, dynamic> json) {
    return ApprovalInfo(
      name: json['name'],
      status: json['status'],
      date: json['date'],
      isApproved: json['isApproved'],
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'status': status,
      'date': date,
      'isApproved': isApproved,
    };
  }
}
