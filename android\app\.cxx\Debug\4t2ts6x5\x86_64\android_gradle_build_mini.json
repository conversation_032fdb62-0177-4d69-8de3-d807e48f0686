{"buildFiles": ["D:\\Dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\flutter_demo\\android\\app\\.cxx\\Debug\\4t2ts6x5\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\flutter_demo\\android\\app\\.cxx\\Debug\\4t2ts6x5\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}