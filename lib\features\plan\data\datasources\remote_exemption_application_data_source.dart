/// -----
/// remote_exemption_application_data_source.dart
///
/// 远程免实习申请数据源实现
/// 通过API调用提交免实习申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

// Flutter 框架库
import 'package:flutter/foundation.dart';

// 项目内部库
import 'package:flutter_demo/core/error/exceptions/server_exception.dart';
import 'package:flutter_demo/core/network/dio_client.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import 'package:flutter_demo/features/plan/data/datasources/exemption_application_data_source.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_request_model.dart';
import 'package:flutter_demo/features/plan/data/models/exemption_application_response_model.dart';

/// 远程免实习申请数据源实现
///
/// 通过HTTP API提交免实习申请
class RemoteExemptionApplicationDataSource implements ExemptionApplicationDataSource {
  static const String _tag = 'RemoteExemptionApplicationDataSource';
  
  final DioClient _dioClient;

  const RemoteExemptionApplicationDataSource({
    required DioClient dioClient,
  }) : _dioClient = dioClient;

  @override
  Future<ExemptionApplicationResponseModel> submitExemptionApplication(
    ExemptionApplicationRequestModel request,
  ) async {
    try {
      Logger.info(_tag, '开始提交免实习申请: ${request.toString()}');

      // 发送POST请求到免实习申请接口
      final response = await _dioClient.post(
        'internshipservice/v1/internship/student/exempt/apply',
        data: request.toJson(),
      );

      Logger.info(_tag, '免实习申请API响应: $response');

      // 解析响应数据
      if (response != null) {
        // DioClient已经处理了基础响应格式，这里直接构造完整响应
        final responseModel = ExemptionApplicationResponseModel(
          data: response is int ? response : (response['data'] ?? 0),
          resultCode: '0', // 成功响应
          resultMsg: 'success',
        );
        
        Logger.info(_tag, '成功提交免实习申请: ${responseModel.toString()}');
        return responseModel;
      } else {
        Logger.error(_tag, '免实习申请API响应为空');
        throw ServerException('提交免实习申请失败：服务器响应为空');
      }
    } catch (e) {
      Logger.error(_tag, '提交免实习申请失败: $e');
      
      if (e is ServerException) {
        rethrow;
      }
      throw ServerException('提交免实习申请失败: $e');
    }
  }
}
