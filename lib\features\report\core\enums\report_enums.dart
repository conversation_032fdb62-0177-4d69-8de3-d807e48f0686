import 'package:flutter/material.dart';

enum ReportType {
  daily,
  weekly,
  monthly,
  summary;

  String get displayName {
    switch (this) {
      case ReportType.daily:
        return '日报';
      case ReportType.weekly:
        return '周报';
      case ReportType.monthly:
        return '月报';
      case ReportType.summary:
        return '总结';
    }
  }
}

enum ReportStatus {
  draft,     // 草稿
  submitted, // 已提交
  approved,  // 已批准
  rejected;  // 已驳回

  String get displayName {
    switch (this) {
      case ReportStatus.draft:
        return '草稿';
      case ReportStatus.submitted:
        return '已提交';
      case ReportStatus.approved:
        return '已批准';
      case ReportStatus.rejected:
        return '已驳回';
    }
  }

  Color get color {
    switch (this) {
      case ReportStatus.draft:
        return Colors.grey;
      case ReportStatus.submitted:
        return Colors.blue;
      case ReportStatus.approved:
        return Colors.green;
      case ReportStatus.rejected:
        return Colors.red;
    }
  }
}