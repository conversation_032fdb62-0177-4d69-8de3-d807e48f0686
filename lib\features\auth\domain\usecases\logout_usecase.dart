import 'package:dartz/dartz.dart';
import '../../../../core/error/failures/failure.dart';
import '../repositories/auth_repository.dart';

/// 退出登录用例
///
/// 处理用户退出登录的业务逻辑
class LogoutUseCase {
  final AuthRepository _repository;

  LogoutUseCase(this._repository);

  /// 调用退出登录用例
  ///
  /// 返回：Either<Failure, bool>，表示退出登录成功或失败
  Future<Either<Failure, bool>> call() async {
    return await _repository.logout();
  }
}
