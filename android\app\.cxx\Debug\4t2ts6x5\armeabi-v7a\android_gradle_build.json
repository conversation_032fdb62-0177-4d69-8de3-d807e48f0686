{"buildFiles": ["D:\\Dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\flutter_demo\\android\\app\\.cxx\\Debug\\4t2ts6x5\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["D:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Projects\\Android\\flutter_demo\\android\\app\\.cxx\\Debug\\4t2ts6x5\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}