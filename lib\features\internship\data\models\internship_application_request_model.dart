/// -----
/// internship_application_request_model.dart
///
/// 实习申请API请求数据模型
/// 用于向服务器提交实习申请数据
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 实习申请请求模型
///
/// 根据API文档 /v1/internship/apply/student/save 接口定义
/// 包含所有必需的字段用于提交实习申请
class InternshipApplicationRequestModel {
  /// 详细地址
  final String address;
  
  /// 市
  final String city;
  
  /// 统一社会信用代码
  final String companyCode;
  
  /// 企业名称
  final String companyName;
  
  /// 企业老师
  final String companyTeacher;
  
  /// 企业邮箱
  final String contactEmail;
  
  /// 企业联系人
  final String contactPerson;
  
  /// 企业联系人电话
  final String contactPhone;
  
  /// 工作内容
  final String description;
  
  /// 所属行业
  final String industry;
  
  /// 详细地址
  final String jobAddress;
  
  /// 岗位类别
  final String jobCategory;
  
  /// 岗位市
  final String jobCity;
  
  /// 岗位部门
  final String jobDept;
  
  /// 岗位结束时间（时间戳）
  final int jobEndTime;
  
  /// 是否提供伙食（0=否，1=是）
  final int jobFood;
  
  /// 住宿类型
  final String jobHouse;
  
  /// 住宿地址
  final String jobHouseAddress;
  
  /// 专业匹配
  final String jobMajor;
  
  /// 岗位名称
  final String jobName;
  
  /// 岗位省
  final String jobProvince;
  
  /// 实习薪资
  final String jobSalary;
  
  /// 实习工作是否有特殊情况（0=无，1=有）
  final int jobSpecial;
  
  /// 岗位开始时间（时间戳）
  final int jobStartTime;
  
  /// 实习方式
  final String jobType;
  
  /// 企业性质（例：国企/民企/外企/事业单位…）
  final String nature;
  
  /// 关联的实习计划ID
  final String planId;
  
  /// 省份
  final String province;
  
  /// 企业规模（例：<100人 / 100-500人 / 500+）
  final String scale;
  
  /// 企业老师电话
  final String teacherPhone;

  /// 构造函数
  const InternshipApplicationRequestModel({
    required this.address,
    required this.city,
    required this.companyCode,
    required this.companyName,
    required this.companyTeacher,
    required this.contactEmail,
    required this.contactPerson,
    required this.contactPhone,
    required this.description,
    required this.industry,
    required this.jobAddress,
    required this.jobCategory,
    required this.jobCity,
    required this.jobDept,
    required this.jobEndTime,
    required this.jobFood,
    required this.jobHouse,
    required this.jobHouseAddress,
    required this.jobMajor,
    required this.jobName,
    required this.jobProvince,
    required this.jobSalary,
    required this.jobSpecial,
    required this.jobStartTime,
    required this.jobType,
    required this.nature,
    required this.planId,
    required this.province,
    required this.scale,
    required this.teacherPhone,
  });

  /// 转换为JSON Map
  ///
  /// 用于API请求时序列化数据
  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'city': city,
      'companyCode': companyCode,
      'companyName': companyName,
      'companyTeacher': companyTeacher,
      'contactEmail': contactEmail,
      'contactPerson': contactPerson,
      'contactPhone': contactPhone,
      'description': description,
      'industry': industry,
      'jobAddress': jobAddress,
      'jobCategory': jobCategory,
      'jobCity': jobCity,
      'jobDept': jobDept,
      'jobEndTime': jobEndTime,
      'jobFood': jobFood,
      'jobHouse': jobHouse,
      'jobHouseAddress': jobHouseAddress,
      'jobMajor': jobMajor,
      'jobName': jobName,
      'jobProvince': jobProvince,
      'jobSalary': jobSalary,
      'jobSpecial': jobSpecial,
      'jobStartTime': jobStartTime,
      'jobType': jobType,
      'nature': nature,
      'planId': planId,
      'province': province,
      'scale': scale,
      'teacherPhone': teacherPhone,
    };
  }

  /// 从现有的数据模型创建请求模型
  ///
  /// 将UI层的数据模型转换为API请求模型
  factory InternshipApplicationRequestModel.fromFormData({
    required String planId,
    required Map<String, dynamic> companyInfo,
    required Map<String, dynamic> positionInfo,
    required Map<String, dynamic> internshipInfo,
  }) {
    // 解析地址信息
    final companyLocation = companyInfo['location'] as String? ?? '';
    final companyLocationParts = companyLocation.split('/');
    final companyProvince = companyLocationParts.isNotEmpty ? companyLocationParts[0] : '';
    final companyCity = companyLocationParts.length > 1 ? companyLocationParts[1] : '';
    
    final positionLocation = positionInfo['location'] as String? ?? '';
    final positionLocationParts = positionLocation.split('/');
    final jobProvince = positionLocationParts.isNotEmpty ? positionLocationParts[0] : '';
    final jobCity = positionLocationParts.length > 1 ? positionLocationParts[1] : '';

    // 解析时间戳
    final startDateStr = internshipInfo['startDate'] as String? ?? '';
    final endDateStr = internshipInfo['endDate'] as String? ?? '';
    
    int jobStartTime = 0;
    int jobEndTime = 0;
    
    try {
      if (startDateStr.isNotEmpty) {
        final startDate = DateTime.parse(startDateStr);
        jobStartTime = startDate.millisecondsSinceEpoch ~/ 1000;
      }
      if (endDateStr.isNotEmpty) {
        final endDate = DateTime.parse(endDateStr);
        jobEndTime = endDate.millisecondsSinceEpoch ~/ 1000;
      }
    } catch (e) {
      // 如果日期解析失败，使用默认值
    }

    // 解析布尔值
    final provideMeals = internshipInfo['provideMeals'] as String? ?? '否';
    final jobFood = provideMeals == '是' ? 1 : 0;
    
    final specialCircumstances = internshipInfo['specialCircumstances'] as String? ?? '无';
    final jobSpecial = specialCircumstances != '无' && specialCircumstances.isNotEmpty ? 1 : 0;

    return InternshipApplicationRequestModel(
      address: companyInfo['address'] as String? ?? '',
      city: companyCity,
      companyCode: companyInfo['creditCode'] as String? ?? '',
      companyName: companyInfo['name'] as String? ?? '',
      companyTeacher: positionInfo['supervisor'] as String? ?? '',
      contactEmail: companyInfo['email'] as String? ?? '',
      contactPerson: companyInfo['contactPerson'] as String? ?? '',
      contactPhone: companyInfo['contactPhone'] as String? ?? '',
      description: positionInfo['description'] as String? ?? '',
      industry: companyInfo['industry'] as String? ?? '',
      jobAddress: positionInfo['address'] as String? ?? '',
      jobCategory: positionInfo['positionCategory'] as String? ?? '',
      jobCity: jobCity,
      jobDept: positionInfo['department'] as String? ?? '',
      jobEndTime: jobEndTime,
      jobFood: jobFood,
      jobHouse: internshipInfo['accommodationType'] as String? ?? '',
      jobHouseAddress: internshipInfo['accommodationAddress'] as String? ?? '',
      jobMajor: internshipInfo['professionalMatch'] as String? ?? '',
      jobName: positionInfo['name'] as String? ?? '',
      jobProvince: jobProvince,
      jobSalary: internshipInfo['salary'] as String? ?? '',
      jobSpecial: jobSpecial,
      jobStartTime: jobStartTime,
      jobType: internshipInfo['internshipType'] as String? ?? '',
      nature: companyInfo['type'] as String? ?? '',
      planId: planId,
      province: companyProvince,
      scale: companyInfo['size'] as String? ?? '',
      teacherPhone: positionInfo['supervisorPhone'] as String? ?? '',
    );
  }
}
