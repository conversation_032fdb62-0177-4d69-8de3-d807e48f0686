/// -----
/// student_file_approval_list_state.dart
///
/// 学生文件审批列表状态
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../../domain/entities/student_file_approval.dart';

/// 学生文件审批列表状态基类
abstract class StudentFileApprovalListState extends Equatable {
  const StudentFileApprovalListState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class StudentFileApprovalListInitial extends StudentFileApprovalListState {
  const StudentFileApprovalListInitial();
}

/// 加载中状态
class StudentFileApprovalListLoading extends StudentFileApprovalListState {
  const StudentFileApprovalListLoading();
}

/// 加载成功状态
class StudentFileApprovalListLoaded extends StudentFileApprovalListState {
  final List<StudentFileApproval> studentFileApprovalList;

  const StudentFileApprovalListLoaded({
    required this.studentFileApprovalList,
  });

  @override
  List<Object?> get props => [studentFileApprovalList];

  @override
  String toString() {
    return 'StudentFileApprovalListLoaded{studentFileApprovalList: ${studentFileApprovalList.length} items}';
  }
}

/// 加载失败状态
class StudentFileApprovalListError extends StudentFileApprovalListState {
  final String message;

  const StudentFileApprovalListError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];

  @override
  String toString() {
    return 'StudentFileApprovalListError{message: $message}';
  }
}
