/// -----
/// exam_score_calculator_test.dart
/// 
/// 安全教育考试分数计算器测试
///
/// <AUTHOR>
/// @date 2025-06-17
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_demo/features/safety/domain/models/safety_exam_question.dart';
import 'package:flutter_demo/features/safety/domain/utils/exam_score_calculator.dart';

void main() {
  group('ExamScoreCalculator', () {
    late List<SafetyExamQuestion> testQuestions;

    setUp(() {
      // 创建测试题目
      testQuestions = [
        // 单选题1
        SafetyExamQuestion(
          id: '1',
          planId: '8',
          title: '单选题1',
          options: [
            ExamOption(id: 'A', content: '选项A'),
            ExamOption(id: 'B', content: '选项B'),
            ExamOption(id: 'C', content: '选项C'),
          ],
          correctAnswers: ['A'],
          number: 1,
        ),
        // 单选题2
        SafetyExamQuestion(
          id: '2',
          planId: '8',
          title: '单选题2',
          options: [
            ExamOption(id: 'A', content: '选项A'),
            ExamOption(id: 'B', content: '选项B'),
          ],
          correctAnswers: ['B'],
          number: 2,
        ),
        // 多选题1
        SafetyExamQuestion(
          id: '3',
          planId: '8',
          title: '多选题1',
          options: [
            ExamOption(id: 'A', content: '选项A'),
            ExamOption(id: 'B', content: '选项B'),
            ExamOption(id: 'C', content: '选项C'),
          ],
          correctAnswers: ['A', 'C'],
          number: 3,
        ),
        // 多选题2
        SafetyExamQuestion(
          id: '4',
          planId: '8',
          title: '多选题2',
          options: [
            ExamOption(id: 'A', content: '选项A'),
            ExamOption(id: 'B', content: '选项B'),
            ExamOption(id: 'C', content: '选项C'),
            ExamOption(id: 'D', content: '选项D'),
          ],
          correctAnswers: ['B', 'D'],
          number: 4,
        ),
      ];
    });

    test('应该正确计算全部答对的分数', () {
      final userAnswers = {
        '1': ['A'],
        '2': ['B'],
        '3': ['A', 'C'],
        '4': ['B', 'D'],
      };

      final score = ExamScoreCalculator.calculateScore(testQuestions, userAnswers);
      expect(score, 100);
    });

    test('应该正确计算部分答对的分数', () {
      final userAnswers = {
        '1': ['A'], // 正确
        '2': ['A'], // 错误
        '3': ['A', 'C'], // 正确
        '4': ['B'], // 错误（多选题不完整）
      };

      final score = ExamScoreCalculator.calculateScore(testQuestions, userAnswers);
      expect(score, 50); // 4题中答对2题，50分
    });

    test('应该正确计算全部答错的分数', () {
      final userAnswers = {
        '1': ['B'], // 错误
        '2': ['A'], // 错误
        '3': ['B'], // 错误（多选题不完整）
        '4': ['A', 'C'], // 错误
      };

      final score = ExamScoreCalculator.calculateScore(testQuestions, userAnswers);
      expect(score, 0);
    });

    test('应该正确处理多选题的部分选择', () {
      final userAnswers = {
        '3': ['A'], // 多选题只选了一个正确选项，应该算错
      };

      final score = ExamScoreCalculator.calculateScore(testQuestions, userAnswers);
      expect(score, 0); // 多选题必须完全正确才得分
    });

    test('应该正确处理多选题的多余选择', () {
      final userAnswers = {
        '3': ['A', 'B', 'C'], // 多选题选择了错误选项，应该算错
      };

      final score = ExamScoreCalculator.calculateScore(testQuestions, userAnswers);
      expect(score, 0); // 多选题必须完全正确才得分
    });

    test('应该正确计算答对题目数量', () {
      final userAnswers = {
        '1': ['A'], // 正确
        '2': ['A'], // 错误
        '3': ['A', 'C'], // 正确
      };

      final correctCount = ExamScoreCalculator.getCorrectCount(testQuestions, userAnswers);
      expect(correctCount, 2);
    });

    test('应该正确计算答错题目数量', () {
      final userAnswers = {
        '1': ['A'], // 正确
        '2': ['A'], // 错误
        '3': ['A', 'C'], // 正确
        '4': ['A'], // 错误
      };

      final incorrectCount = ExamScoreCalculator.getIncorrectCount(testQuestions, userAnswers);
      expect(incorrectCount, 2);
    });

    test('应该正确生成考试结果摘要', () {
      final userAnswers = {
        '1': ['A'], // 正确
        '2': ['A'], // 错误
        '3': ['A', 'C'], // 正确
        // '4' 未回答
      };

      final summary = ExamScoreCalculator.getExamSummary(testQuestions, userAnswers);
      
      expect(summary.totalQuestions, 4);
      expect(summary.correctCount, 2);
      expect(summary.incorrectCount, 1);
      expect(summary.unansweredCount, 1);
      expect(summary.score, 50);
      expect(summary.accuracyRate, 50.0);
      expect(summary.isPassed, false); // 50分不及格
    });

    test('应该正确判断及格状态', () {
      final userAnswers = {
        '1': ['A'], // 正确
        '2': ['B'], // 正确
        '3': ['A', 'C'], // 正确
        '4': ['B'], // 错误
      };

      final summary = ExamScoreCalculator.getExamSummary(testQuestions, userAnswers);
      expect(summary.score, 75); // 4题中答对3题，75分
      expect(summary.isPassed, true); // 75分及格
    });

    test('应该处理空题目列表', () {
      final score = ExamScoreCalculator.calculateScore([], {});
      expect(score, 0);
    });

    test('应该处理空用户答案', () {
      final score = ExamScoreCalculator.calculateScore(testQuestions, {});
      expect(score, 0);
    });
  });
}
