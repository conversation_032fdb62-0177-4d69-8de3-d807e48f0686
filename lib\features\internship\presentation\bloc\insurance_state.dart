/// -----
/// insurance_state.dart
/// 
/// 实习保险相关状态定义
///
/// <AUTHOR>
/// @date 2025-05-26
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/internship/domain/entities/insurance_info.dart';

/// 实习保险状态基类
abstract class InsuranceState extends Equatable {
  const InsuranceState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class InsuranceInitial extends InsuranceState {}

/// 加载中状态
class InsuranceLoading extends InsuranceState {}

/// 加载成功状态
class InsuranceLoaded extends InsuranceState {
  /// 保险信息
  final InsuranceInfo insuranceInfo;
  
  /// 可用课程列表
  final List<String> availableCourses;
  
  /// 当前选中的课程
  final String currentCourse;

  const InsuranceLoaded({
    required this.insuranceInfo,
    required this.availableCourses,
    required this.currentCourse,
  });

  @override
  List<Object?> get props => [insuranceInfo, availableCourses, currentCourse];
}

/// 加载失败状态
class InsuranceError extends InsuranceState {
  /// 错误信息
  final String message;

  const InsuranceError({required this.message});

  @override
  List<Object?> get props => [message];
}

/// 合同查看状态
class InsuranceContractViewing extends InsuranceState {
  /// 文件路径
  final String filePath;
  
  /// 文件名
  final String fileName;

  const InsuranceContractViewing({
    required this.filePath,
    required this.fileName,
  });

  @override
  List<Object?> get props => [filePath, fileName];
}
