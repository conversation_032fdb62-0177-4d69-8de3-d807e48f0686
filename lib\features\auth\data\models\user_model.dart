/// -----
/// user_model.dart
/// 
/// 用户数据模型，用于处理API响应数据
///
/// <AUTHOR>
/// @date 2025-05-28
/// @copyright Copyright © 2025 亿硕教育
/// -----

import '../../domain/entities/user.dart';

/// 用户数据模型
///
/// 继承自 User 实体，用于处理 API 响应数据
class UserModel extends User {
  const UserModel({
    required String userKey,
    String? deptName,
    String? dept,
    String? userType,
    String? userName,
    String? userCode,
    String? token,
    String? phone,
  }) : super(
          userKey: userKey,
          deptName: deptName,
          dept: dept,
          userType: userType,
          userName: userName,
          userCode: userCode,
          token: token,
          phone: phone,
        );

  /// 从 JSON 创建 UserModel
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      userKey: json['userKey'] ?? '',
      deptName: json['deptName'],
      dept: json['dept'],
      userType: json['userType'],
      userName: json['userName'],
      userCode: json['userCode'],
      token: json['token'],
      phone: json['phone'],
    );
  }
  /// 从登录响应创建 UserModel
  factory UserModel.fromLoginResponse(Map<String, dynamic> json) {
    // 如果响应中包含 data 字段，则使用 data 中的数据
    final data = json.containsKey('data') ? json['data'] : json;

    // 如果 data 是一个整数，则返回一个只包含 token 的用户模型
    if (data is int) {
      return UserModel(
        userKey: '',
        token: json['token'],
        phone: json['phone'],
      );
    }

    // 如果 data 是一个对象，则使用对象中的数据
    if (data is Map<String, dynamic>) {
      return UserModel(
        userKey: data['userKey'] ?? '',
        deptName: data['deptName'],
        dept: data['dept'],
        userType: data['userType']?.toString(),
        userName: data['userName'],
        userCode: data['userCode'],
        token: data['token'],
        phone: data['phone'],
      );
    }

    // 如果以上情况都不满足，则创建一个只包含 token 的用户模型
    return UserModel(
      userKey: '',
      token: json['token'],
      phone: json['phone'],
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'userKey': userKey,
      'deptName': deptName,
      'dept': dept,
      'userType': userType,
      'userName': userName,
      'userCode': userCode,
      'token': token,
      'phone': phone,
    };
  }

  /// 创建一个新的 UserModel 实例，并更新指定的字段
  UserModel copyWith({
    String? userKey,
    String? deptName,
    String? dept,
    String? userType,
    String? userName,
    String? userCode,
    String? token,
    String? phone,
  }) {
    return UserModel(
      userKey: userKey ?? this.userKey,
      deptName: deptName ?? this.deptName,
      dept: dept ?? this.dept,
      userType: userType ?? this.userType,
      userName: userName ?? this.userName,
      userCode: userCode ?? this.userCode,
      token: token ?? this.token,
      phone: phone ?? this.phone,
    );
  }
}
