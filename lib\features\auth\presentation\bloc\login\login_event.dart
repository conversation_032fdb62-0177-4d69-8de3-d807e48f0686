import 'package:equatable/equatable.dart';

/// 登录事件基类
///
/// 所有登录相关的事件都应继承此类
abstract class LoginEvent extends Equatable {
  const LoginEvent();

  @override
  List<Object?> get props => [];
}

/// 登录请求事件
///
/// 当用户尝试登录时触发
class LoginRequestEvent extends LoginEvent {

  const LoginRequestEvent({
    required this.phone,
    required this.password,
  });
  final String phone;
  final String password;

  @override
  List<Object?> get props => [phone, password];
}

/// 退出登录事件
///
/// 当用户退出登录时触发
class LogoutEvent extends LoginEvent {}

/// 检查登录状态事件
///
/// 当需要检查用户是否已登录时触发
class CheckLoginStatusEvent extends LoginEvent {}

/// 获取当前用户事件
///
/// 当需要获取当前登录用户信息时触发
class GetCurrentUserEvent extends LoginEvent {}
