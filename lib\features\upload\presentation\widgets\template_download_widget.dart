/// -----
/// template_download_widget.dart
/// 
/// 模板下载组件
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 模板下载组件
class TemplateDownloadWidget extends StatelessWidget {
  /// 文件名
  final String fileName;
  /// 下载链接
  final String downloadUrl;
  /// 下载回调
  final VoidCallback? onDownload;

  const TemplateDownloadWidget({
    Key? key,
    required this.fileName,
    required this.downloadUrl,
    this.onDownload,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 25.w,right: 25.w, top: 20.h),
      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 30.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Row(
        children: [
          // Word图标
          Image.asset('assets/images/upload_blue_doc_icon.png', width: 40.w, height: 48.h),
          SizedBox(width: 20.w),
          // 文件名
          Text(
            fileName,
            style: TextStyle(
              fontSize: 28.sp,
              color: const Color(0xFF333333),
              fontWeight: FontWeight.w500,
            ),
          ),
          const Spacer(),
          // 下载按钮
          GestureDetector(
            onTap: onDownload,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    '去下载',
                    style: TextStyle(
                      fontSize: 24.sp,
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(width: 20.w),
                  const Icon(
                    Icons.chevron_right,
                    size: 20,
                    color: AppTheme.primaryColor,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
