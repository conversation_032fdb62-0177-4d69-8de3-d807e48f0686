/// -----
/// sign_progress_ring.dart
///
/// 签到进度圆环组件，显示签到天数进度及说明
/// 基于统一的圆环进度组件重构
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/widgets/progress_ring/progress_ring.dart';

class SignProgressRing extends StatelessWidget {
  final int signedDays;
  final int totalDays;
  final double size;
  final Color ringColor;
  final String label;
  final double strokeWidth;
  final bool enableAnimation;

  const SignProgressRing({
    Key? key,
    required this.signedDays,
    required this.totalDays,
    this.size = 100,
    this.ringColor = const Color(0xFF2165F6),
    this.label = '签到天数',
    this.strokeWidth = 12,
    this.enableAnimation = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double progress = totalDays == 0 ? 0 : signedDays / totalDays;

    // 使用预定义的签到样式
    return UnifiedProgressRingStyles.signIn(
      progress: progress,
      signedDays: signedDays,
      totalDays: totalDays,
      label: label,
      size: size,
      progressColor: ringColor,
      enableAnimation: enableAnimation,
    );
  }
}