/// -----
/// exam_detail_state.dart
/// 
/// 考试详情状态类
///
/// <AUTHOR>
/// @date 2025-06-19
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import 'package:flutter_demo/features/safety/domain/models/exam_detail_response.dart';

/// 考试详情状态基类
///
/// 所有考试详情相关状态的基类
abstract class ExamDetailState extends Equatable {
  /// 构造函数
  const ExamDetailState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
///
/// 考试详情初始状态
class ExamDetailInitial extends ExamDetailState {}

/// 加载中状态
///
/// 正在加载考试详情
class ExamDetailLoading extends ExamDetailState {}

/// 加载成功状态
///
/// 成功加载考试详情
class ExamDetailLoaded extends ExamDetailState {
  /// 考试详情数据
  final ExamDetailResponse examDetail;

  /// 构造函数
  const ExamDetailLoaded({
    required this.examDetail,
  });

  @override
  List<Object?> get props => [examDetail];
}

/// 加载失败状态
///
/// 加载考试详情失败
class ExamDetailError extends ExamDetailState {
  /// 错误信息
  final String message;

  /// 构造函数
  const ExamDetailError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

/// 刷新中状态
///
/// 正在刷新考试详情
class ExamDetailRefreshing extends ExamDetailState {
  /// 之前的考试详情数据
  final ExamDetailResponse previousData;

  /// 构造函数
  const ExamDetailRefreshing({
    required this.previousData,
  });

  @override
  List<Object?> get props => [previousData];
}
