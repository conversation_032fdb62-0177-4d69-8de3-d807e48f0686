/// -----
/// auth_exception.dart
/// 
/// 认证相关异常类定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

/// 认证异常基类
/// 
/// 当认证相关操作失败时抛出此异常
abstract class AuthException implements Exception {
  final String message;

  AuthException([this.message = '认证错误']);

  @override
  String toString() => 'AuthException: $message';
}

/// 未授权异常
/// 
/// 当用户未登录或token无效时抛出此异常
class UnauthorizedException extends AuthException {
  UnauthorizedException([String message = '未授权，请重新登录']) : super(message);

  @override
  String toString() => 'UnauthorizedException: $message';
}

/// Token过期异常
/// 
/// 当token过期需要刷新时抛出此异常
class TokenExpiredException extends AuthException {
  TokenExpiredException([String message = 'Token已过期']) : super(message);

  @override
  String toString() => 'TokenExpiredException: $message';
}

/// 权限不足异常
/// 
/// 当用户权限不足时抛出此异常
class ForbiddenException extends AuthException {
  ForbiddenException([String message = '权限不足，禁止访问']) : super(message);

  @override
  String toString() => 'ForbiddenException: $message';
}
