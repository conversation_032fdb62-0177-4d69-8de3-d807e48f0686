/// -----
/// plan_detail_state.dart
/// 
/// 实习计划详情页面状态定义
/// 
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:equatable/equatable.dart';
import '../../../domain/entities/internship_plan.dart';

/// 实习计划详情页面状态基类
abstract class PlanDetailState extends Equatable {
  const PlanDetailState();

  @override
  List<Object?> get props => [];
}

/// 初始状态
class PlanDetailInitialState extends PlanDetailState {}

/// 加载中状态
class PlanDetailLoadingState extends PlanDetailState {}

/// 加载成功状态
class PlanDetailLoadedState extends PlanDetailState {
  /// 实习计划详情
  final InternshipPlan plan;

  const PlanDetailLoadedState({required this.plan});

  @override
  List<Object?> get props => [plan];
}

/// 加载失败状态
class PlanDetailErrorState extends PlanDetailState {
  /// 错误消息
  final String message;

  const PlanDetailErrorState({required this.message});

  @override
  List<Object?> get props => [message];
}

/// 刷新成功状态
class PlanDetailRefreshSuccessState extends PlanDetailLoadedState {
  const PlanDetailRefreshSuccessState({required InternshipPlan plan})
      : super(plan: plan);
}

/// 刷新失败状态
class PlanDetailRefreshErrorState extends PlanDetailState {
  /// 错误消息
  final String message;
  
  /// 之前的计划数据（如果有的话）
  final InternshipPlan? previousPlan;

  const PlanDetailRefreshErrorState({
    required this.message,
    this.previousPlan,
  });

  @override
  List<Object?> get props => [message, previousPlan];
}
