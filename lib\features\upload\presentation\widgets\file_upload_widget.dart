/// -----
/// file_upload_widget.dart
///
/// 文件上传组件
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

/// 文件上传组件
class FileUploadWidget extends StatefulWidget {
  /// 文件选择回调
  final Function(String filePath, String fileName, int fileSize)? onFileSelected;
  /// 文件移除回调
  final VoidCallback? onFileRemoved;
  /// 是否正在上传
  final bool isUploading;
  /// 上传进度 (0.0 - 1.0)
  final double uploadProgress;
  /// 上传步骤描述
  final String? stepDescription;
  /// 选中的文件信息
  final File? selectedFile;

  const FileUploadWidget({
    Key? key,
    this.onFileSelected,
    this.onFileRemoved,
    this.isUploading = false,
    this.uploadProgress = 0.0,
    this.stepDescription,
    this.selectedFile,
  }) : super(key: key);

  @override
  State<FileUploadWidget> createState() => _FileUploadWidgetState();
}

class _FileUploadWidgetState extends State<FileUploadWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 25.w, vertical: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 文件上传标题
          Text(
            '文件上传',
            style: TextStyle(
              fontSize: 32.sp,
              color: AppTheme.black333,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),

          // 文件上传区域
          _buildFileUploadArea(),
        ],
      ),
    );
  }

  /// 构建文件上传区域
  Widget _buildFileUploadArea() {
    return Container(
      padding: EdgeInsets.all(30.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
      ),
      child: Column(
        children: [
          // 如果有选中的文件，显示文件信息
          if (widget.selectedFile != null) ...[
            _buildSelectedFileItem(),
            SizedBox(height: 20.h),
          ],

          // 如果正在上传，显示上传进度
          if (widget.isUploading) ...[
            _buildUploadProgress(),
            SizedBox(height: 20.h),
          ],

          // 如果没有选中文件或上传完成，显示选择按钮
          if (widget.selectedFile == null && !widget.isUploading)
            _buildSelectFileButton(),

          SizedBox(height: 20.h),

          // 上传提示
          Text(
            '目前仅支持上传PDF格式文件，每个文件大小不超过100M。',
            style: TextStyle(
              fontSize: 24.sp,
              color: AppTheme.black999,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建选中的文件项
  Widget _buildSelectedFileItem() {
    final file = widget.selectedFile!;
    final fileName = file.path.split('/').last;

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F8F8),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: const Color(0xFFE5E5E5)),
      ),
      child: Row(
        children: [
          // PDF图标
          Icon(
            Icons.picture_as_pdf,
            size: 40.w,
            color: Colors.red,
          ),
          SizedBox(width: 12.w),

          // 文件信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: TextStyle(
                    fontSize: 28.sp,
                    color: AppTheme.black333,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 4.h),
                FutureBuilder<int>(
                  future: file.length(),
                  builder: (context, snapshot) {
                    if (snapshot.hasData) {
                      final sizeInMB = snapshot.data! / (1024 * 1024);
                      return Text(
                        '${sizeInMB.toStringAsFixed(1)} MB',
                        style: TextStyle(
                          fontSize: 24.sp,
                          color: AppTheme.black999,
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),

          // 删除按钮
          if (!widget.isUploading)
            GestureDetector(
              onTap: widget.onFileRemoved,
              child: Icon(
                Icons.close,
                size: 24.w,
                color: Colors.grey,
              ),
            ),
        ],
      ),
    );
  }

  /// 构建上传进度
  Widget _buildUploadProgress() {
    return Column(
      children: [
        // 进度条
        LinearProgressIndicator(
          value: widget.uploadProgress,
          backgroundColor: const Color(0xFFE5E5E5),
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),
        SizedBox(height: 8.h),

        // 进度文本
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.stepDescription ?? '上传中...',
              style: TextStyle(
                fontSize: 24.sp,
                color: AppTheme.black666,
              ),
            ),
            Text(
              '${(widget.uploadProgress * 100).toInt()}%',
              style: TextStyle(
                fontSize: 24.sp,
                color: AppTheme.black666,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建选择文件按钮
  Widget _buildSelectFileButton() {
    return GestureDetector(
      onTap: _pickPDFFile,
      child: Container(
        height: 120.h,
        decoration: BoxDecoration(
          color: const Color(0xFFF8F8F8),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: const Color(0xFFE5E5E5),
            width: 2.0,
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.cloud_upload_outlined,
                size: 40.w,
                color: AppTheme.primaryColor,
              ),
              SizedBox(height: 8.h),
              Text(
                '点击选择PDF文件',
                style: TextStyle(
                  fontSize: 28.sp,
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 选择PDF文件
  Future<void> _pickPDFFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final fileName = result.files.first.name;
        final fileSize = await file.length();

        // 检查文件大小（100MB限制）
        if (fileSize > 100 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('文件大小不能超过100MB')),
            );
          }
          return;
        }

        // 触发文件选择回调
        widget.onFileSelected?.call(file.path, fileName, fileSize);
      }
    } catch (e) {
      debugPrint('Error picking PDF file: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('选择文件失败，请重试')),
        );
      }
    }
  }
}
